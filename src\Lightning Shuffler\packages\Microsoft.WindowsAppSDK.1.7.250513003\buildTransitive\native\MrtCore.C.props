<!--
  Copyright (c) Microsoft Corporation. Licensed under the MIT License
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <_MrtCoreRuntimeIdentifier Condition="'$(Platform)' == 'Win32'">x86</_MrtCoreRuntimeIdentifier>
    <_MrtCoreRuntimeIdentifier Condition="'$(Platform)' != 'Win32'">$(Platform)</_MrtCoreRuntimeIdentifier>

    <!-- Set DefaultLanguage so that Multilingual Application Toolkit can be enabled when applicable -->
    <DefaultLanguage Condition="'$(DefaultLanguage)' == ''">en-US</DefaultLanguage>
  </PropertyGroup>

  <!--
    Some of the manual reference adding below may not be necessary due to the files in question being in default locations, but there's no harm in it.
  -->

  <ItemDefinitionGroup>
    <ClCompile>
      <AdditionalIncludeDirectories>$(MSBuildThisFileDirectory)..\..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
  </ItemDefinitionGroup>

  <ItemDefinitionGroup>
    <Link>
      <AdditionalDependencies>
        $(MSBuildThisFileDirectory)..\..\lib\win10-$(_MrtCoreRuntimeIdentifier)\mrm.lib;
        %(AdditionalDependencies);
      </AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>

</Project>
