<!--
  Copyright (c) Microsoft Corporation. Licensed under the MIT License
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="$(MSBuildThisFileDirectory)MicrosoftWindowsAppSDKFoundationAppXVersion.props" />

  <PropertyGroup>
    <WinAppSDKPackageName Condition="'$(WinAppSDKPackageName)'==''">Microsoft.WindowsAppRuntime.1.7</WinAppSDKPackageName>
  </PropertyGroup>

  <ItemGroup>
    <AppxPackageRegistration Include="$(MSBuildThisFileDirectory)..\tools\MSIX\win10-x86\$(WinAppSDKPackageName).msix">
      <Architecture>x86</Architecture>
      <Version>$(MicrosoftWindowsAppSDKFoundationAppXVersion)</Version>
      <Publisher>'CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US'</Publisher>
      <WindowsAppSDK>true</WindowsAppSDK>
    </AppxPackageRegistration>
  </ItemGroup>
  <ItemGroup>
    <AppxPackageRegistration Include="$(MSBuildThisFileDirectory)..\tools\MSIX\win10-x86\$(WinAppSDKPackageName).msix">
      <Architecture>win32</Architecture>
      <Version>$(MicrosoftWindowsAppSDKFoundationAppXVersion)</Version>
      <Publisher>'CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US'</Publisher>
      <WindowsAppSDK>true</WindowsAppSDK>
    </AppxPackageRegistration>
  </ItemGroup>
  <ItemGroup>
    <AppxPackageRegistration Include="$(MSBuildThisFileDirectory)..\tools\MSIX\win10-x64\$(WinAppSDKPackageName).msix">
      <Architecture>x64</Architecture>
      <Version>$(MicrosoftWindowsAppSDKFoundationAppXVersion)</Version>
      <Publisher>'CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US'</Publisher>
      <WindowsAppSDK>true</WindowsAppSDK>
    </AppxPackageRegistration>
  </ItemGroup>
  <ItemGroup>
    <AppxPackageRegistration Include="$(MSBuildThisFileDirectory)..\tools\MSIX\win10-arm64\$(WinAppSDKPackageName).msix">
      <Architecture>arm64</Architecture>
      <Version>$(MicrosoftWindowsAppSDKFoundationAppXVersion)</Version>
      <Publisher>'CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US'</Publisher>
      <WindowsAppSDK>true</WindowsAppSDK>
    </AppxPackageRegistration>
  </ItemGroup>

</Project>
