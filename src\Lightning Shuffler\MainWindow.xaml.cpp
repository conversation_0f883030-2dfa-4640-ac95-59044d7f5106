#include "pch.h"
#include "MainWindow.xaml.h"
#include "VideoItemViewModel.h"
#include "AddPlaylistDialog.h"
#if __has_include("MainWindow.g.cpp")
#include "MainWindow.g.cpp"
#endif
#include <algorithm>
#include <cctype>

using namespace winrt;
using namespace Microsoft::UI::Xaml;
using namespace Microsoft::UI::Xaml::Controls;
using namespace Microsoft::UI::Xaml::Input;
using namespace Windows::Foundation;
using namespace Windows::Foundation::Collections;

// To learn more about WinUI, the WinUI project structure,
// and more about our project templates, see: http://aka.ms/winui-project-info.

namespace winrt::Lightning_Shuffler::implementation
{
    // Playlist Management Event Handlers
    void MainWindow::AddPlaylistButton_Click(IInspectable const &, RoutedEventArgs const &)
    {
        // For now, add some sample data (dialog integration will be added later)
        auto samplePlaylist = std::make_shared<PlaylistItem>(L"Sample Playlist", L"PLrAXtmRdnEQy4Qy9", L"");

        // Add some sample videos
        samplePlaylist->videos.push_back(std::make_shared<VideoItem>(
            L"Sample Video 1", L"Sample Artist", L"dQw4w9WgXcQ", L"", L"3:32"));
        samplePlaylist->videos.push_back(std::make_shared<VideoItem>(
            L"Sample Video 2", L"Another Artist", L"oHg5SJYRHA0", L"", L"4:15"));

        m_playlists.push_back(samplePlaylist);

        // Update the queue with videos from this playlist
        for (const auto &video : samplePlaylist->videos)
        {
            m_currentQueue.push_back(video);
        }

        FilterQueue();
    }

    void MainWindow::CreateMixButton_Click(IInspectable const &, RoutedEventArgs const &)
    {
        // TODO: Show dialog to create mix from multiple playlists
    }

    void MainWindow::SearchBox_TextChanged(IInspectable const &sender, TextChangedEventArgs const &)
    {
        auto textBox = sender.as<TextBox>();
        m_searchText = textBox.Text().c_str();
        FilterQueue();
    }

    void MainWindow::QueueListView_SelectionChanged(IInspectable const &sender, SelectionChangedEventArgs const &)
    {
        auto listView = sender.as<ListView>();
        auto selectedIndex = listView.SelectedIndex();

        if (selectedIndex >= 0 && selectedIndex < static_cast<int>(m_currentQueue.size()))
        {
            PlayVideo(selectedIndex);
        }
    }

    // Media Control Event Handlers
    void MainWindow::PlayPauseButton_Click(IInspectable const &, RoutedEventArgs const &)
    {
        m_isPlaying = !m_isPlaying;
        UpdatePlayPauseButton();

        // TODO: Implement actual video playback control
    }

    void MainWindow::PreviousButton_Click(IInspectable const &, RoutedEventArgs const &)
    {
        PlayPrevious();
    }

    void MainWindow::NextButton_Click(IInspectable const &, RoutedEventArgs const &)
    {
        PlayNext();
    }

    void MainWindow::ShuffleButton_Click(IInspectable const &, RoutedEventArgs const &)
    {
        m_isShuffled = !m_isShuffled;
        if (m_isShuffled)
        {
            ShuffleQueue();
        }
        // TODO: Update shuffle button visual state
    }

    void MainWindow::LoopButton_Click(IInspectable const &, RoutedEventArgs const &)
    {
        // Toggle loop on/off
        if (m_loopCount == 0)
        {
            m_loopCount = -1; // Infinite loop
        }
        else
        {
            m_loopCount = 0; // No loop
        }
        UpdateLoopButton();
    }

    void MainWindow::LoopButton_RightTapped(IInspectable const &, RightTappedRoutedEventArgs const &)
    {
        // Increase loop count
        if (m_loopCount == 0)
        {
            m_loopCount = 1;
        }
        else if (m_loopCount > 0)
        {
            m_loopCount++;
        }
        UpdateLoopButton();
    }

    void MainWindow::VolumeButton_Click(IInspectable const &, RoutedEventArgs const &)
    {
        m_isMuted = !m_isMuted;
        UpdateVolumeIcon();
        // TODO: Implement actual volume muting
    }

    void MainWindow::VolumeSlider_ValueChanged(IInspectable const &sender, Microsoft::UI::Xaml::Controls::Primitives::RangeBaseValueChangedEventArgs const &)
    {
        auto slider = sender.as<Microsoft::UI::Xaml::Controls::Slider>();
        m_volume = slider.Value();
        m_isMuted = (m_volume == 0);
        UpdateVolumeIcon();
        // TODO: Implement actual volume control
    }

    // Private Helper Methods
    void MainWindow::UpdatePlayPauseButton()
    {
        auto playPauseIcon = this->FindName(L"PlayPauseIcon").as<FontIcon>();
        if (m_isPlaying)
        {
            playPauseIcon.Glyph(L"\uE769"); // Pause icon
        }
        else
        {
            playPauseIcon.Glyph(L"\uE768"); // Play icon
        }
    }

    void MainWindow::UpdateCurrentVideoInfo()
    {
        auto titleBlock = this->FindName(L"CurrentVideoTitle").as<TextBlock>();
        auto authorBlock = this->FindName(L"CurrentVideoAuthor").as<TextBlock>();

        if (m_currentVideoIndex >= 0 && m_currentVideoIndex < static_cast<int>(m_currentQueue.size()))
        {
            auto currentVideo = m_currentQueue[m_currentVideoIndex];
            titleBlock.Text(currentVideo->title);
            authorBlock.Text(currentVideo->author);
        }
        else
        {
            titleBlock.Text(L"No video playing");
            authorBlock.Text(L"");
        }
    }

    void MainWindow::UpdateLoopButton()
    {
        auto loopCountText = this->FindName(L"LoopCountText").as<TextBlock>();

        if (m_loopCount == 0)
        {
            loopCountText.Visibility(Visibility::Collapsed);
        }
        else if (m_loopCount == -1)
        {
            loopCountText.Text(L"∞");
            loopCountText.Visibility(Visibility::Visible);
        }
        else
        {
            loopCountText.Text(std::to_wstring(m_loopCount));
            loopCountText.Visibility(Visibility::Visible);
        }
    }

    void MainWindow::UpdateVolumeIcon()
    {
        auto volumeIcon = this->FindName(L"VolumeIcon").as<FontIcon>();

        if (m_isMuted || m_volume == 0)
        {
            volumeIcon.Glyph(L"\uE74F"); // Mute icon
        }
        else if (m_volume < 33)
        {
            volumeIcon.Glyph(L"\uE993"); // Low volume icon
        }
        else if (m_volume < 66)
        {
            volumeIcon.Glyph(L"\uE994"); // Medium volume icon
        }
        else
        {
            volumeIcon.Glyph(L"\uE767"); // High volume icon
        }
    }

    void MainWindow::FilterQueue()
    {
        auto listView = this->FindName(L"QueueListView").as<ListView>();

        // Create a collection for the ListView with proper data binding
        auto collection = winrt::single_threaded_observable_vector<Lightning_Shuffler::VideoItemViewModel>();

        for (const auto &video : m_currentQueue)
        {
            // Filter based on search text if provided
            if (!m_searchText.empty())
            {
                std::wstring title = video->title;
                std::wstring author = video->author;
                std::wstring search = m_searchText;

                // Convert to lowercase for case-insensitive search
                std::transform(title.begin(), title.end(), title.begin(), ::towlower);
                std::transform(author.begin(), author.end(), author.begin(), ::towlower);
                std::transform(search.begin(), search.end(), search.begin(), ::towlower);

                if (title.find(search) == std::wstring::npos &&
                    author.find(search) == std::wstring::npos)
                {
                    continue; // Skip this video if it doesn't match search
                }
            }

            // Create ViewModel for data binding
            auto viewModel = winrt::make<implementation::VideoItemViewModel>(
                video->title, video->author, video->thumbnailUrl);
            collection.Append(viewModel);
        }

        listView.ItemsSource(collection);
    }

    void MainWindow::ShuffleQueue()
    {
        if (m_currentQueue.size() <= 1)
            return;

        // Simple shuffle algorithm
        for (size_t i = m_currentQueue.size() - 1; i > 0; --i)
        {
            size_t j = rand() % (i + 1);
            std::swap(m_currentQueue[i], m_currentQueue[j]);
        }

        FilterQueue();
    }

    void MainWindow::PlayVideo(int index)
    {
        if (index >= 0 && index < static_cast<int>(m_currentQueue.size()))
        {
            m_currentVideoIndex = index;
            m_isPlaying = true;
            UpdatePlayPauseButton();
            UpdateCurrentVideoInfo();

            // TODO: Load video in WebView2
        }
    }

    void MainWindow::PlayNext()
    {
        if (m_currentQueue.empty())
            return;

        int nextIndex = m_currentVideoIndex + 1;
        if (nextIndex >= static_cast<int>(m_currentQueue.size()))
        {
            if (m_loopCount != 0)
            {
                nextIndex = 0; // Loop back to beginning
                if (m_loopCount > 0)
                {
                    m_loopCount--;
                    UpdateLoopButton();
                }
            }
            else
            {
                return; // End of queue
            }
        }

        PlayVideo(nextIndex);
    }

    void MainWindow::PlayPrevious()
    {
        if (m_currentQueue.empty())
            return;

        int prevIndex = m_currentVideoIndex - 1;
        if (prevIndex < 0)
        {
            prevIndex = static_cast<int>(m_currentQueue.size()) - 1; // Loop to end
        }

        PlayVideo(prevIndex);
    }
}
