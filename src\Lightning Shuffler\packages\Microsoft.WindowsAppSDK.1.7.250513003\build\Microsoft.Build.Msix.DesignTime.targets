<Project ToolsVersion="14.0">

  <PropertyGroup>
    <_MicrosoftBuildMsixProjRulesDir>$(MSBuildThisFileDirectory)Rules\</_MicrosoftBuildMsixProjRulesDir>
  </PropertyGroup>
  <!--
    Rule files that don't need localization go in the neutral directory to save duplicating files into each language
  -->
  <PropertyGroup Condition="'$(MicrosoftBuildMsixXamlNeutralResourcesDirectory)' == ''">
    <MicrosoftBuildMsixXamlNeutralResourcesDirectory>$(_MicrosoftBuildMsixProjRulesDir)</MicrosoftBuildMsixXamlNeutralResourcesDirectory>
  </PropertyGroup>
  <!--
    Locate the approriate localized xaml resources based on the language ID or name.
    The logic here matches the resource manager sufficiently to handle the fixed set of
    possible languages and directories on disk.
    We cannot respect the exact probe order of the resource manager as this has to evaluate statically
    and we have only LangName and LangID and no access to System.Globalization API.
  -->
  <PropertyGroup Condition="'$(MicrosoftBuildMsixXamlResourcesDirectory)' == ''">
    <!-- 1. Probe for exact match against LangName. (e.g. pt-BR) -->
    <MicrosoftBuildMsixXamlResourcesDirectory>$(_MicrosoftBuildMsixProjRulesDir)$(LangName)</MicrosoftBuildMsixXamlResourcesDirectory>

    <!-- 2. Handle special cases of languages which would not match above or below. -->
    <MicrosoftBuildMsixXamlResourcesDirectory Condition="!Exists('$(MicrosoftBuildMsixXamlResourcesDirectory)') and '$(LangID)' == '2052'">$(_MicrosoftBuildMsixProjRulesDir)zh-Hans</MicrosoftBuildMsixXamlResourcesDirectory>
    <MicrosoftBuildMsixXamlResourcesDirectory Condition="!Exists('$(MicrosoftBuildMsixXamlResourcesDirectory)') and '$(LangID)' == '1028'">$(_MicrosoftBuildMsixProjRulesDir)zh-Hant</MicrosoftBuildMsixXamlResourcesDirectory>

    <!-- 3. Probe for parent by taking the portion before the hyphen (e.g. fr-FR -> fr) -->
    <MicrosoftBuildMsixXamlResourcesDirectory Condition="!Exists('$(MicrosoftBuildMsixXamlResourcesDirectory)')">$(_MicrosoftBuildMsixProjRulesDir)$(LangName.Split('-')[0])</MicrosoftBuildMsixXamlResourcesDirectory>

    <!-- 4. Fall back to neutral resources if all of the above fail -->
    <MicrosoftBuildMsixXamlResourcesDirectory Condition="!Exists('$(MicrosoftBuildMsixXamlResourcesDirectory)')">$(MicrosoftBuildMsixXamlNeutralResourcesDirectory)</MicrosoftBuildMsixXamlResourcesDirectory>
  </PropertyGroup>

  <PropertyGroup>
    <MicrosoftBuildMsixXamlResourcesDirectory Condition="!HasTrailingSlash('$(MicrosoftBuildMsixXamlResourcesDirectory)')">$(MicrosoftBuildMsixXamlResourcesDirectory)\</MicrosoftBuildMsixXamlResourcesDirectory>
  </PropertyGroup>

  <ItemGroup>
    <ProjectCapability Include="Msix"/>
    <ProjectCapability Include="WindowsAppContainer" />
    <ProjectCapability Include="AppDesigner" />
    <ProjectCapability Include="ImageSet"/>
    <ProjectCapability Include="SupportUniversalAuthentication" />
  </ItemGroup>

  <!-- Import CPS XAML rules -->
  <ItemGroup>
    <PropertyPageSchema Include="$(MicrosoftBuildMsixXamlResourcesDirectory)MsixPackageDebugPropertyPage.xaml"
                        Condition="'$(WindowsPackageType)'=='MSIX'">
      <Context>Project</Context>
    </PropertyPageSchema>
    <PropertyPageSchema Include="$(MicrosoftBuildMsixXamlResourcesDirectory)WindowsPackageTypePropertyPage.xaml"
                        Condition="'$(EnableMsixTooling)'=='true'">
      <Context>Project</Context>
    </PropertyPageSchema>
  </ItemGroup>
</Project>