<?xml version="1.0" encoding="utf-8"?>
<!--
    Copyright (c) Microsoft. All rights reserved.
    This code is licensed under the MIT License.
    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF
    ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED
    TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
    PARTICULAR PURPOSE AND NONINFRINGEMENT.
-->
<AutoVisualizer xmlns="http://schemas.microsoft.com/vstudio/debugger/natvis/2010">
    <Type Name="wistd::default_delete&lt;*&gt;">
        <DisplayString>default_delete</DisplayString>
        <Expand />
    </Type>

    <Type Name="wistd::__compressed_pair_elem&lt;*,*,0&gt;">
        <Intrinsic Name="get" Expression="__value_" />
        <DisplayString>{get()}</DisplayString>
        <Expand>
            <ExpandedItem>get()</ExpandedItem>
        </Expand>
    </Type>

    <Type Name="wistd::__compressed_pair_elem&lt;*,*,1&gt;">
        <Intrinsic Name="get" Expression="*($T1*)this" />
        <DisplayString>{get()}</DisplayString>
        <Expand>
            <ExpandedItem>get()</ExpandedItem>
        </Expand>
    </Type>

    <Type Name="wistd::__compressed_pair&lt;*&gt;">
        <Intrinsic Name="first" Expression="(static_cast&lt;_Base1*&gt;(this))->get()" />
        <Intrinsic Name="second" Expression="(static_cast&lt;_Base2*&gt;(this))->get()" />
        <DisplayString>({first()}, {second()})</DisplayString>
        <Expand>
            <Item Name="[first]">first()</Item>
            <Item Name="[second]">second()</Item>
        </Expand>
    </Type>

    <Type Name="wistd::__function::__func&lt;*&gt;">
        <DisplayString>{__f_}</DisplayString>
        <Expand>
        </Expand>
    </Type>

    <Type Name="wistd::function&lt;*&gt;">
        <DisplayString Condition="__f_ == 0">empty</DisplayString>
        <DisplayString Condition="__f_ != 0">{*__f_}</DisplayString>
        <Expand>
            <ExpandedItem Condition="__f_ != 0">*__f_</ExpandedItem>
        </Expand>
    </Type>

    <Type Name="wistd::unique_ptr&lt;*&gt;">
        <SmartPointer Usage="Minimal">__ptr_.first()</SmartPointer>
        <DisplayString Condition="__ptr_.first() == 0">empty</DisplayString>
        <DisplayString Condition="__ptr_.first() != 0">{*__ptr_.first()}</DisplayString>
        <Expand>
            <Item Name="[ptr]" Condition="__ptr_.first() != 0">__ptr_.first()</Item>
            <Item Name="[deleter]" Condition="__ptr_.first() != 0">__ptr_.second()</Item>
        </Expand>
    </Type>

    <Type Name="wistd::unique_ptr&lt;wchar_t[],*&gt;">
        <DisplayString Condition="__ptr_.first() == 0">empty</DisplayString>
        <DisplayString Condition="__ptr_.first() != 0">{__ptr_.first(),su}</DisplayString>
        <StringView>__ptr_.first()</StringView>
        <Expand>
            <Item Name="[ptr]" Condition="__ptr_.first() != 0">__ptr_.first()</Item>
            <Item Name="[length]" Condition="__ptr_.first() != 0">wcslen(__ptr_.first())</Item>
            <Item Name="[deleter]" Condition="__ptr_.first() != 0">__ptr_.second()</Item>
        </Expand>
    </Type>

    <Type Name="wistd::unique_ptr&lt;wchar_t[*],*&gt;">
        <DisplayString Condition="__ptr_.first() == 0">empty</DisplayString>
        <DisplayString Condition="__ptr_.first() != 0">{__ptr_.first(),su}</DisplayString>
        <StringView>__ptr_.first()</StringView>
        <Expand>
            <Item Name="[ptr]" Condition="__ptr_.first() != 0">__ptr_.first()</Item>
            <Item Name="[length]" Condition="__ptr_.first() != 0">wcslen(__ptr_.first())</Item>
            <Item Name="[deleter]" Condition="__ptr_.first() != 0">__ptr_.second()</Item>
        </Expand>
    </Type>

    <Type Name="wistd::unique_ptr&lt;char[],*&gt;">
        <DisplayString Condition="__ptr_.first() == 0">empty</DisplayString>
        <DisplayString Condition="__ptr_.first() != 0">{__ptr_.first(),s}</DisplayString>
        <StringView>__ptr_.first()</StringView>
        <Expand>
            <Item Name="[ptr]" Condition="__ptr_.first() != 0">__ptr_.first()</Item>
            <Item Name="[length]" Condition="__ptr_.first() != 0">strlen(__ptr_.first())</Item>
            <Item Name="[deleter]" Condition="__ptr_.first() != 0">__ptr_.second()</Item>
        </Expand>
    </Type>

    <Type Name="wistd::unique_ptr&lt;char[*],*&gt;">
        <DisplayString Condition="__ptr_.first() == 0">empty</DisplayString>
        <DisplayString Condition="__ptr_.first() != 0">{__ptr_.first(),s}</DisplayString>
        <StringView>__ptr_.first()</StringView>
        <Expand>
            <Item Name="[ptr]" Condition="__ptr_.first() != 0">__ptr_.first()</Item>
            <Item Name="[length]" Condition="__ptr_.first() != 0">strlen(__ptr_.first())</Item>
            <Item Name="[deleter]" Condition="__ptr_.first() != 0">__ptr_.second()</Item>
        </Expand>
    </Type>

    <Type Name="wil::details::shared_storage&lt;*&gt;">
        <DisplayString Condition="m_ptr == 0">empty</DisplayString>
        <DisplayString Condition="m_ptr != 0">{*m_ptr}</DisplayString>
        <StringView>m_ptr</StringView>
        <Expand>
            <Item Name="[pointer]" Condition="m_ptr != 0">m_ptr</Item>
        </Expand>
    </Type>

    <Type Name="wil::details::unique_storage&lt;wil::details::handle_null_resource_policy&lt;*&gt;&gt;">
        <DisplayString Condition="m_ptr == 0">empty</DisplayString>
        <DisplayString Condition="m_ptr != 0">{m_ptr}</DisplayString>
        <Expand>
        </Expand>
    </Type>
    <Type Name="wil::details::unique_storage&lt;wil::details::handle_invalid_resource_policy&lt;*&gt;&gt;">
        <DisplayString Condition="m_ptr == 0">empty</DisplayString>
        <DisplayString Condition="m_ptr != 0">{m_ptr}</DisplayString>
        <Expand>
        </Expand>
    </Type>
    <Type Name="wil::details::unique_storage&lt;wil::details::resource_policy&lt;*&gt;&gt;">
        <DisplayString Condition="m_ptr == 0">empty</DisplayString>
        <DisplayString Condition="m_ptr != 0">{m_ptr}</DisplayString>
        <StringView>m_ptr</StringView>
        <Expand>
        </Expand>
    </Type>
    <Type Name="wil::details::unique_storage&lt;wil::details::resource_policy&lt;wchar_t *,*&gt;&gt;">
        <DisplayString Condition="m_ptr == 0">empty</DisplayString>
        <DisplayString Condition="m_ptr != 0">{m_ptr,su}</DisplayString>
        <StringView>m_ptr</StringView>
        <Expand>
            <Item Name="[length]" Condition="m_ptr != 0">wcslen(m_ptr)</Item>
        </Expand>
    </Type>
    <Type Name="wil::details::unique_storage&lt;wil::details::resource_policy&lt;char *,*&gt;&gt;">
        <DisplayString Condition="m_ptr == 0">empty</DisplayString>
        <DisplayString Condition="m_ptr != 0">{m_ptr,s}</DisplayString>
        <StringView>m_ptr</StringView>
        <Expand>
            <Item Name="[length]" Condition="m_ptr != 0">strlen(m_ptr)</Item>
        </Expand>
    </Type>

    <Type Name="wil::com_ptr_t&lt;*&gt;">
        <DisplayString>{m_ptr}</DisplayString>
        <Expand>
            <ExpandedItem>m_ptr</ExpandedItem>
        </Expand>
    </Type>

    <Type Name="wil::basic_zstring_view&lt;*&gt;">
        <Intrinsic Name="size" Expression="_Mysize" />
        <Intrinsic Name="data" Expression="_Mydata" />
        <DisplayString>{_Mydata,[_Mysize]}</DisplayString>
        <Expand>
            <Item Name="[size]" ExcludeView="simple">size()</Item>
        </Expand>
    </Type>
</AutoVisualizer>