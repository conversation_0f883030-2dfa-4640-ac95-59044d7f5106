<doc>
  <assembly>
    <name>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentInitializeOptions">
      <summary>A class that represents deployment options. Passed as a parameter to DeploymentManager.Initialize(DeploymentInitializeOptions).</summary>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentInitializeOptions.#ctor">
      <summary>Initializes a new instance of the DeploymentInitializeOptions class.</summary>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentInitializeOptions.ForceDeployment">
      <summary>Gets or sets a value that indicates whether, when registering the Windows App SDK packages, the processes associated with the Windows App SDK Main and Singleton packages will be shut down forcibly if they are currently in use.</summary>
      <returns>true if the processes will be shut down forcibly if they are currently in use; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentInitializeOptions.OnErrorShowUI">
      <summary>Gets or sets a value that indicates whether UI should be displayed in the event of an error.</summary>
      <returns>true UI should be displayed; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentManager">
      <summary>Provides access to deployment information for the Windows App SDK runtime.

&gt; [!IMPORTANT]
&gt; Your app should call DeploymentManager.Initialize.</summary>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentManager.GetStatus">
      <summary>Returns the current deployment status of the Windows App SDK runtime that is currently loaded. Use this method to identify if there is work required to install Windows App SDK runtime packages before the current app can use Windows App SDK features.</summary>
      <returns>An object that provides deployment status and error information for the Windows App SDK runtime referenced by the current package.</returns>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentManager.Initialize">
      <summary>Checks the status of the Windows App SDK runtime referenced by the current package, and attempts to register any missing Windows App SDK packages that can be registered

&gt; [!IMPORTANT]
&gt; Your app should call DeploymentManager.Initialize during startup. For more info, see Initialize the Windows App SDK.</summary>
      <returns>An object that provides deployment status and error information for the Windows App SDK runtime referenced by the current package.</returns>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentManager.Initialize(Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentInitializeOptions)">
      <summary>Checks the status of the Windows App SDK runtime referenced by the current package, and attempts to register any missing Windows App SDK packages that can be registered, while applying the options supplied.

&gt; [!IMPORTANT]
&gt; Your app should call DeploymentManager.Initialize during startup. For more info, see Initialize the Windows App SDK.</summary>
      <param name="deploymentInitializeOptions">A DeploymentInitializeOptions object that specifies options for the registration operation.</param>
      <returns>An object that provides deployment status and error information for the Windows App SDK runtime referenced by the current package.</returns>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentResult">
      <summary>Provides deployment status and error information for the Windows App SDK runtime referenced by the current package.</summary>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentResult.#ctor(Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentStatus,Windows.Foundation.HResult)">
      <summary>Initializes a new instance of the DeploymentResult class.</summary>
      <param name="status">The deployment status of the Windows App SDK runtime that is currently loaded.</param>
      <param name="extendedError">The first encountered error if there was an error initializing the Windows App SDK runtime or getting the status of the runtime.</param>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentResult.ExtendedError">
      <summary>Gets the first encountered error if there was an error initializing the Windows App SDK runtime or getting the status of the runtime.</summary>
      <returns>The first encountered error if there was an error initializing the Windows App SDK runtime or getting the status of the runtime. If there is no error, then this property returns S_OK.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentResult.Status">
      <summary>Gets the deployment status of the Windows App SDK runtime that is currently loaded.</summary>
      <returns>The deployment status of the Windows App SDK runtime that is currently loaded.</returns>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentStatus">
      <summary>Represents the deployment status of the Windows App SDK runtime that is currently loaded.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentStatus.Ok">
      <summary>The Windows App SDK runtime is in a good deployment state.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentStatus.PackageInstallFailed">
      <summary>The installation of a package for the Windows App SDK runtime failed.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentStatus.PackageInstallRequired">
      <summary>A package install is required in order for the Windows App SDK runtime to be in a good deployment state.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentStatus.Unknown">
      <summary>The Windows App SDK runtime is in an unknown deployment state.</summary>
    </member>
  </members>
</doc>