// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Widgets_Notifications_0_H
#define WINRT_Microsoft_Windows_Widgets_Notifications_0_H
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct Uri;
}
WINRT_EXPORT namespace winrt::Microsoft::Windows::Widgets::Notifications
{
    enum class AnnouncementActionKind : int32_t
    {
        Shown = 0,
        Engaged = 1,
    };
    enum class AnnouncementTextColor : int32_t
    {
        Default = 0,
        Dark = 1,
        Light = 2,
        Accent = 3,
        Good = 4,
        Warning = 5,
        Attention = 6,
    };
    struct IFeedAnnouncement;
    struct IFeedAnnouncementFactory;
    struct IFeedAnnouncementInvokedArgs;
    struct FeedAnnouncement;
    struct FeedAnnouncementInvokedArgs;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncement>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementInvokedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Notifications::FeedAnnouncement>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Notifications::FeedAnnouncementInvokedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Notifications::AnnouncementActionKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Notifications::AnnouncementTextColor>{ using type = enum_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Notifications::FeedAnnouncement> = L"Microsoft.Windows.Widgets.Notifications.FeedAnnouncement";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Notifications::FeedAnnouncementInvokedArgs> = L"Microsoft.Windows.Widgets.Notifications.FeedAnnouncementInvokedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Notifications::AnnouncementActionKind> = L"Microsoft.Windows.Widgets.Notifications.AnnouncementActionKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Notifications::AnnouncementTextColor> = L"Microsoft.Windows.Widgets.Notifications.AnnouncementTextColor";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncement> = L"Microsoft.Windows.Widgets.Notifications.IFeedAnnouncement";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementFactory> = L"Microsoft.Windows.Widgets.Notifications.IFeedAnnouncementFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementInvokedArgs> = L"Microsoft.Windows.Widgets.Notifications.IFeedAnnouncementInvokedArgs";
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncement>{ 0xB88E8C2C,0xD251,0x5344,{ 0xAC,0xC2,0x8C,0xF9,0xBA,0x07,0xEC,0x15 } }; // B88E8C2C-D251-5344-ACC2-8CF9BA07EC15
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementFactory>{ 0x22074243,0x46D8,0x5AF2,{ 0x87,0x15,0x1C,0x76,0xD1,0xCB,0x77,0x4C } }; // 22074243-46D8-5AF2-8715-1C76D1CB774C
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementInvokedArgs>{ 0x70A48D98,0x323D,0x5F19,{ 0xA1,0xE1,0xB6,0x3F,0xE3,0x6E,0xDB,0xF2 } }; // 70A48D98-323D-5F19-A1E1-B63FE36EDBF2
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Notifications::FeedAnnouncement>{ using type = winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncement; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Notifications::FeedAnnouncementInvokedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementInvokedArgs; };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncement>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Id(void**) noexcept = 0;
            virtual int32_t __stdcall put_Id(void*) noexcept = 0;
            virtual int32_t __stdcall get_PrimaryText(void**) noexcept = 0;
            virtual int32_t __stdcall put_PrimaryText(void*) noexcept = 0;
            virtual int32_t __stdcall get_SecondaryText(void**) noexcept = 0;
            virtual int32_t __stdcall put_SecondaryText(void*) noexcept = 0;
            virtual int32_t __stdcall get_LightModeIconUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_LightModeIconUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_DarkModeIconUri(void**) noexcept = 0;
            virtual int32_t __stdcall put_DarkModeIconUri(void*) noexcept = 0;
            virtual int32_t __stdcall get_PrimaryTextColor(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_PrimaryTextColor(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_SecondaryTextColor(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_SecondaryTextColor(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_CustomAccessibilityText(void**) noexcept = 0;
            virtual int32_t __stdcall put_CustomAccessibilityText(void*) noexcept = 0;
            virtual int32_t __stdcall get_IsSecondaryTextSubtle(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsSecondaryTextSubtle(bool) noexcept = 0;
            virtual int32_t __stdcall get_ShowBadgeIfUserNotEngaged(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ShowBadgeIfUserNotEngaged(bool) noexcept = 0;
            virtual int32_t __stdcall get_ExpirationTime(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_ExpirationTime(int64_t) noexcept = 0;
            virtual int32_t __stdcall get_Duration(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_Duration(int64_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void*, void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementInvokedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_FeedDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_AnnouncementId(void**) noexcept = 0;
            virtual int32_t __stdcall get_ActionKind(int32_t*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Notifications_IFeedAnnouncement
    {
        [[nodiscard]] auto Id() const;
        auto Id(param::hstring const& value) const;
        [[nodiscard]] auto PrimaryText() const;
        auto PrimaryText(param::hstring const& value) const;
        [[nodiscard]] auto SecondaryText() const;
        auto SecondaryText(param::hstring const& value) const;
        [[nodiscard]] auto LightModeIconUri() const;
        auto LightModeIconUri(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] auto DarkModeIconUri() const;
        auto DarkModeIconUri(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] auto PrimaryTextColor() const;
        auto PrimaryTextColor(winrt::Microsoft::Windows::Widgets::Notifications::AnnouncementTextColor const& value) const;
        [[nodiscard]] auto SecondaryTextColor() const;
        auto SecondaryTextColor(winrt::Microsoft::Windows::Widgets::Notifications::AnnouncementTextColor const& value) const;
        [[nodiscard]] auto CustomAccessibilityText() const;
        auto CustomAccessibilityText(param::hstring const& value) const;
        [[nodiscard]] auto IsSecondaryTextSubtle() const;
        auto IsSecondaryTextSubtle(bool value) const;
        [[nodiscard]] auto ShowBadgeIfUserNotEngaged() const;
        auto ShowBadgeIfUserNotEngaged(bool value) const;
        [[nodiscard]] auto ExpirationTime() const;
        auto ExpirationTime(winrt::Windows::Foundation::DateTime const& value) const;
        [[nodiscard]] auto Duration() const;
        auto Duration(winrt::Windows::Foundation::TimeSpan const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncement>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Notifications_IFeedAnnouncement<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Notifications_IFeedAnnouncementFactory
    {
        auto CreateInstance(param::hstring const& id, param::hstring const& primaryText, param::hstring const& secondaryText, winrt::Windows::Foundation::Uri const& lightModeIcon, winrt::Windows::Foundation::Uri const& darkModeIcon) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementFactory>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Notifications_IFeedAnnouncementFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Notifications_IFeedAnnouncementInvokedArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
        [[nodiscard]] auto FeedDefinitionId() const;
        [[nodiscard]] auto AnnouncementId() const;
        [[nodiscard]] auto ActionKind() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementInvokedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Notifications_IFeedAnnouncementInvokedArgs<D>;
    };
}
#endif
