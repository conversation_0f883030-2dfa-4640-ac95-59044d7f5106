<doc>
  <assembly>
    <name>Microsoft.Windows.AppNotifications.Projection</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.AppNotifications.AppNotification">
      <summary>Represents an app notification.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotification.#ctor(System.String)">
      <summary>Creates a new instance of the AppNotification class.</summary>
      <param name="payload">The notification payload representation in xml.</param>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotification.Expiration">
      <summary>Gets or sets the time after which the app notification should not be displayed.</summary>
      <returns>The time after which the app notification should not be displayed.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotification.ExpiresOnReboot">
      <summary>Gets a value specifying whether the app notification will remain in the Notification Center (called Action Center in Windows 10) after a reboot.</summary>
      <returns>True if the app notification should remain in the Notification Center after a reboot; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotification.Group">
      <summary>Gets or sets the unique identifier for an app notification group.</summary>
      <returns>The identifier for an app notification group. This value must be unique within the app.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotification.Id">
      <summary>Gets a unique, platform-generated identifier for the app notification.</summary>
      <returns>The unique identifier for the app notification.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotification.Payload">
      <summary>Gets the notification's XML payload.</summary>
      <returns>The notification's XML payload.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotification.Priority">
      <summary>Gets or sets the priority for the app notification.</summary>
      <returns>A value from the AppNotificationPriority enumeration specifying the priority for the app notification.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotification.Progress">
      <summary>Gets or sets information about the progress of the app notification.</summary>
      <returns>An AppNotificationProgressData object containing information about the progress of the app notification.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotification.SuppressDisplay">
      <summary>Gets or sets a value that specifies whether the app notification's pop-up UI is displayed on the user's screen.</summary>
      <returns>True is the display of the app notification should be suppressed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotification.Tag">
      <summary>Gets or sets a unique identifier that can be used to identify a set of app notifications across groups.</summary>
      <returns>The unique identifier that specifies a set of app notifications across groups.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.AppNotificationActivatedEventArgs">
      <summary>Represents event args associated with an app activation triggered by an app notification.</summary>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationActivatedEventArgs.Argument">
      <summary>Gets the text provided in the arguments attribute of the action element associated with the app notification button input that triggered the app activation.</summary>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationActivatedEventArgs.Arguments">
      <summary>Gets a dictionary of arguments set in the arguments attribute of the action element associated with the app notification button input that triggered the app activation.</summary>
      <returns>A dictionary of arguments.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationActivatedEventArgs.UserInput">
      <summary>Gets a map of IDs and values of the input elements of an app notification.</summary>
      <returns>A dictionary where the key is the ID of an app notification input, such as a TextBox, and the value is the value of the input.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.AppNotificationManager">
      <summary>Provides APIs for managing app notifications, including showing and removing notifications in Notification Center (called Action Center in Windows 10), updating notification progress, and registering and unregistering for app notification invocations.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="E:Microsoft.Windows.AppNotifications.AppNotificationManager.NotificationInvoked">
      <summary>Raised when an app notification for the app is invoked through user interaction.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.GetAllAsync">
      <summary>Gets all the app notifications for calling app that are currently displayed in Action Center.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <returns>A list of AppNotification objects.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.IsSupported">
      <summary>Gets a boolean value indicating if the Microsoft.Windows.PushNotifications notification APIs are supported for the calling app. 

&gt; [!NOTE]
&gt; The PushNotificationManager class has a dependency on the Singleton package.</summary>
      <returns>True if push notifications APIs are supported; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.Register">
      <summary>Registers the app to receive NotificationInvoked events when the user interacts with an app notification.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.Register(System.String,Windows.Foundation.Uri)">
      <summary>Registers the app to receive NotificationInvoked events when the user interacts with an app notification.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <param name="displayName">
      </param>
      <param name="iconUri">
      </param>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.RemoveAllAsync">
      <summary>Asynchronously removes all app notifications for the app from Notification Center (called Action Center in Windows 10).

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <returns>An asynchronous action.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.RemoveByGroupAsync(System.String)">
      <summary>Asynchronously removes all app notifications for the app that have the specified group identifier from Action Center.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <param name="group">The unique identifier for the app notification group to be removed, accessed with the Group property.</param>
      <returns>An asynchronous action.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.RemoveByIdAsync(System.UInt32)">
      <summary>Asynchronously removes the app notification with the specified ID from Notification Center (called Action Center in Windows 10).

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <param name="notificationId">The unique identifier for the app notification to be removed. The ID is set by the platform and can be accessed with the Id property.</param>
      <returns>An asynchronous action.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.RemoveByTagAndGroupAsync(System.String,System.String)">
      <summary>Asynchronously removes all app notifications for the app that have the specified group and tag identifiers from Action Center.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <param name="tag">The unique identifier for the set of notifications to be removed from the specified group, accessed with the Tag property.</param>
      <param name="group">The unique identifier for the app notification group from which notifications to be removed, accessed with the Group property.</param>
      <returns>An asynchronous action.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.RemoveByTagAsync(System.String)">
      <summary>Asynchronously removes all app notifications for the app that have the specified tag identifier from Notification Center (called Action Center in Windows 10).

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <param name="tag">The unique identifier for the set of notifications to be removed, accessed with the Tag property.</param>
      <returns>An asynchronous action.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.Show(Microsoft.Windows.AppNotifications.AppNotification)">
      <summary>Displays the specified app notification in Action Center.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <param name="notification">An AppNotification object representing the notification to be shown.</param>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.Unregister">
      <summary>Unregisters the app from receiving NotificationInvoked events when the user interacts with an app notification.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.UnregisterAll">
      <summary>Cleans up all registration-related data for app notifications. After this, app notifications for the app will not function until until Register is called again.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.UpdateAsync(Microsoft.Windows.AppNotifications.AppNotificationProgressData,System.String,System.String)">
      <summary>Updates the progress data for app notifications with the specified tag and group identifiers.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <param name="data">An AppNotificationProgressData representing the progress of an app notification.</param>
      <param name="tag">The unique identifier for the set of notifications to be updated, accessed with the Tag property.</param>
      <param name="group">The unique identifier for the app notification group to be updated, accessed with the Group property.</param>
      <returns>An asynchronous operation that returns an AppNotificationProgressResult.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationManager.UpdateAsync(Microsoft.Windows.AppNotifications.AppNotificationProgressData,System.String)">
      <summary>Updates the progress data for app notifications with the specified tag identifier.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <param name="data">An AppNotificationProgressData representing the progress of an app notification.</param>
      <param name="tag">The unique identifier for the set of notifications to be updated, accessed with the Tag property.</param>
      <returns>An asynchronous operation that returns an AppNotificationProgressResult value.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationManager.Default">
      <summary>Gets the default instance of the AppNotificationManager class.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <returns>The default instance of the AppNotificationManager class.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationManager.Setting">
      <summary>Get the app notification setting status for the app, determining whether notifications are currently enabled or, if not, the mechanism through which notifications are currently disabled.

&gt; [!NOTE]
&gt; The AppNotificationManager class has a dependency on the Singleton package.</summary>
      <returns>A value from the AppNotificationSetting enumeration.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.AppNotificationPriority">
      <summary>Specifies the priority of an app notification.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationPriority.Default">
      <summary>Default priority.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationPriority.High">
      <summary>High priority.</summary>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.AppNotificationProgressData">
      <summary>Represents progress data for an app notification.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.AppNotificationProgressData.#ctor(System.UInt32)">
      <summary>Creates an instance of the AppNotificationProgressData class with the specified sequence number.</summary>
      <param name="sequenceNumber">A non-zero integer that specifies the order of progress data updates. When multiple AppNotificationProgressData objects are received, the platform displays the data with the greatest non-zero number.</param>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationProgressData.SequenceNumber">
      <summary>Gets or sets a non-zero integer that specifies the order of progress data updates.</summary>
      <returns>A non-zero integer that specifies the order of progress data updates.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationProgressData.Status">
      <summary>Gets or sets the status string that reflects the status of the progress operation.</summary>
      <returns>A string that reflects the status of the progress operation. For example, "Downloading..." or "Installing...".</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationProgressData.Title">
      <summary>Gets or sets an optional title string of the progress bar associated with the progress update.</summary>
      <returns>A title string.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationProgressData.Value">
      <summary>Gets or sets the status value for the progress data.</summary>
      <returns>A floating point number between 0 and 1.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.AppNotificationProgressData.ValueStringOverride">
      <summary>Gets or sets optional string to be displayed instead of the default percentage string.</summary>
      <returns>The optional value string override.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.AppNotificationProgressResult">
      <summary>Specifies the result of an app notification progress update operation invoked with a call to UpdateAsync.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationProgressResult.AppNotificationNotFound">
      <summary>The specified app notification was not found.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationProgressResult.Succeeded">
      <summary>The progress update was successful.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationProgressResult.Unsupported" />
    <member name="T:Microsoft.Windows.AppNotifications.AppNotificationSetting">
      <summary>Specifies the app notification setting status for the app.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationSetting.DisabledByGroupPolicy">
      <summary>App notifications are blocked by Group Policy.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationSetting.DisabledByManifest">
      <summary>App notifications are blocked by a setting in the manifest. This value only applies to packaged applications.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationSetting.DisabledForApplication">
      <summary>App notifications are blocked by a user-defined app setting.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationSetting.DisabledForUser">
      <summary>App notifications are blocked by a user-defined global setting.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationSetting.Enabled">
      <summary>App notifications are enabled.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.AppNotificationSetting.Unsupported" />
  </members>
</doc>