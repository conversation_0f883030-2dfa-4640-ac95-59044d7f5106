<doc>
  <assembly>
    <name>Microsoft.Windows.System.Power.Projection</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.System.Power.BatteryStatus">
      <summary>Defines values that represent the status of the battery on the device.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.BatteryStatus.Charging">
      <summary>The battery is charging.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.BatteryStatus.Discharging">
      <summary>The battery is discharging.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.BatteryStatus.Idle">
      <summary>The battery is idle.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.BatteryStatus.NotPresent">
      <summary>The battery is not present.</summary>
    </member>
    <member name="T:Microsoft.Windows.System.Power.DisplayStatus">
      <summary>Defines values that represent the status of the display that is associated with the app's session.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.DisplayStatus.Dimmed">
      <summary>The display is dimmed.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.DisplayStatus.Off">
      <summary>The display is off.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.DisplayStatus.On">
      <summary>The display is on.</summary>
    </member>
    <member name="T:Microsoft.Windows.System.Power.EffectivePowerMode">
      <summary>Defines values that represent the effective power mode of the device.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EffectivePowerMode.Balanced">
      <summary>The device is in the balanced effective power mode.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EffectivePowerMode.BatterySaver">
      <summary>The device is in battery saver mode.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EffectivePowerMode.BetterBattery">
      <summary>The device is in the better battery effective power mode.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EffectivePowerMode.GameMode">
      <summary>The device is in game mode power mode.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EffectivePowerMode.HighPerformance">
      <summary>The device is in the high performance effective power mode.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EffectivePowerMode.MaxPerformance">
      <summary>The device is in the maximum performance effective power mode.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EffectivePowerMode.MixedReality">
      <summary>The device is in the windows mixed reality power mode.</summary>
    </member>
    <member name="T:Microsoft.Windows.System.Power.EnergySaverStatus">
      <summary>Defines values that represent the battery saver states of the device.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EnergySaverStatus.Disabled">
      <summary>Battery saver is disabled.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EnergySaverStatus.Off">
      <summary>Battery saver is off.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EnergySaverStatus.On">
      <summary>Battery saver is on.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.EnergySaverStatus.Uninitialized">
      <summary>Battery saver is uninitialized.</summary>
    </member>
    <member name="T:Microsoft.Windows.System.Power.PowerManager">
      <summary>Provides static events that notify your app of changes to the devices power state and static properties that provide access to current power state information.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.BatteryStatusChanged">
      <summary>Raised when the status of the battery on the device has changed.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.DisplayStatusChanged">
      <summary>Raised when the status of the display that is associated with the app's session has changed.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.EffectivePowerModeChanged">
      <summary>Raised when the effective power mode of the device has changed.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.EnergySaverStatusChanged">
      <summary>Raised when battery saver has been turned off or on in response to changing power conditions.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.PowerSourceKindChanged">
      <summary>Raised when the power source of the device has changed.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.PowerSupplyStatusChanged">
      <summary>Raised when the power supply status of the device has changed.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.RemainingChargePercentChanged">
      <summary>Raised when the remaining charge percentage of the battery on the device has changed.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.RemainingDischargeTimeChanged">
      <summary>Raised when the remaining discharge time of the battery on the device has changed.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.SystemIdleStatusChanged">
      <summary>Raised when the system is busy. This indicates that the system will not be moving into an idle state in the near future and that the current time is a good time for components to perform background or idle tasks that would otherwise prevent the computer from entering an idle state.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.SystemSuspendStatusChanged">
      <summary>Raised when the suspend status of the device has changed.</summary>
    </member>
    <member name="E:Microsoft.Windows.System.Power.PowerManager.UserPresenceStatusChanged">
      <summary>Raised when the user status associated with the app's session has changed.</summary>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.BatteryStatus">
      <summary>Gets the current status of the battery on the device.</summary>
      <returns>The current status of the battery.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.DisplayStatus">
      <summary>Gets the current status of the display that is associated with the app's session.</summary>
      <returns>The current status of the display that is associated with the app's session.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.EffectivePowerMode">
      <summary>Gets the current effective power mode of the device.</summary>
      <returns>The current effective power mode of the device.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.EffectivePowerMode2">
      <summary>Gets the current effective power mode of the device.</summary>
      <returns>The current effective power mode of the device.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.EnergySaverStatus">
      <summary>Gets the current state of battery saver on the device.</summary>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.PowerSourceKind">
      <summary>Gets the current power source of the device.</summary>
      <returns>The current power source of the device.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.PowerSupplyStatus">
      <summary>Gets the current power supply status of the device.</summary>
      <returns>The current power supply status of the device.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.RemainingChargePercent">
      <summary>Gets the remaining charge percentage of the battery on the device.</summary>
      <returns>The remaining charge percentage of the battery.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.RemainingDischargeTime">
      <summary>Gets the remaining discharge time of the battery on the device.</summary>
      <returns>The remaining discharge time of the battery.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.SystemSuspendStatus">
      <summary>Gets the current suspend status of the device.</summary>
      <returns>The current suspend status of the device.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.Power.PowerManager.UserPresenceStatus">
      <summary>Gets the current user status associated with the app's session.</summary>
      <returns>The current user present status of the device.</returns>
    </member>
    <member name="T:Microsoft.Windows.System.Power.PowerSourceKind">
      <summary>Defines values that represent the power source of the device.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.PowerSourceKind.AC">
      <summary>The computer is powered by an AC power source (or similar, such as a laptop powered by a 12V automotive adapter).</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.PowerSourceKind.DC">
      <summary>The computer is powered by an onboard battery power source.</summary>
    </member>
    <member name="T:Microsoft.Windows.System.Power.PowerSupplyStatus">
      <summary>Defines values that represent the power supply status of the device.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.PowerSupplyStatus.Adequate">
      <summary>Power supply is adequate.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.PowerSupplyStatus.Inadequate">
      <summary>Power supply is not adequate.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.PowerSupplyStatus.NotPresent">
      <summary>Power supply is not present.</summary>
    </member>
    <member name="T:Microsoft.Windows.System.Power.SystemSuspendStatus">
      <summary>Defines values that represent the suspend status of the device.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.SystemSuspendStatus.AutoResume">
      <summary>The device is automatically resuming from suspend state.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.SystemSuspendStatus.Entering">
      <summary>The device is entering suspend state.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.SystemSuspendStatus.ManualResume">
      <summary>The user has manually resumed the device from suspend state.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.SystemSuspendStatus.Uninitialized">
      <summary>The suspend status is not initialized.</summary>
    </member>
    <member name="T:Microsoft.Windows.System.Power.UserPresenceStatus">
      <summary>Defines values that represent the user status associated with the app's session.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.UserPresenceStatus.Absent">
      <summary>The user is absent.</summary>
    </member>
    <member name="F:Microsoft.Windows.System.Power.UserPresenceStatus.Present">
      <summary>The user is present.</summary>
    </member>
  </members>
</doc>