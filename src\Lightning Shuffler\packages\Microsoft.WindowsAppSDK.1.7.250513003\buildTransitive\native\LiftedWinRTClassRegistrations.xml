<Data xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.UI.Xaml.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.AdaptiveTrigger" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Application" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.AnnotationPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.AutomationAnnotation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.AutomationElementIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.AutomationProperties" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.DockPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.DragPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.DropTargetPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.ExpandCollapsePatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.GridItemPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.GridPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.MultipleViewPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.AppBarAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.AppBarButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.AppBarToggleButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.AutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.AutomationPeerAnnotation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.AutoSuggestBoxAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ButtonBaseAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.CalendarDatePickerAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.CheckBoxAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ComboBoxAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ComboBoxItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ComboBoxItemDataAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.DatePickerAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.FlipViewAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.FlipViewItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.FlipViewItemDataAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.FlyoutPresenterAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.FrameworkElementAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.GridViewAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.GridViewHeaderItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.GridViewItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.GridViewItemDataAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.GroupItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.HubAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.HubSectionAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.HyperlinkButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ImageAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ItemsControlAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ListBoxAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ListBoxItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ListBoxItemDataAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ListViewAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ListViewBaseAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ListViewBaseHeaderItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ListViewHeaderItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ListViewItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ListViewItemDataAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.MediaPlayerElementAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.MediaTransportControlsAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.MenuFlyoutItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.MenuFlyoutPresenterAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.PasswordBoxAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.RadioButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.RangeBaseAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.RepeatButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.RichEditBoxAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.RichTextBlockAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.RichTextBlockOverflowAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ScrollBarAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ScrollViewerAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.SelectorAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.SelectorItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.SemanticZoomAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.SliderAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.TextBlockAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.TextBoxAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ThumbAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.TimePickerAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ToggleButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ToggleMenuFlyoutItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ToggleSwitchAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.RangeValuePatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.ScrollPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.SelectionItemPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.SelectionPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.SpreadsheetItemPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.StylesPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.TableItemPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.TablePatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.TogglePatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.TransformPattern2Identifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.TransformPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.ValuePatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.WindowPatternIdentifiers" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.BringIntoViewOptions" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.BrushTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.ColorDisplayNameHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.ColorPaletteResources" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AppBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AppBarButton" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AppBarElementContainer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AppBarSeparator" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AppBarToggleButton" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AutoSuggestBox" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AutoSuggestBoxQuerySubmittedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AutoSuggestBoxSuggestionChosenEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AutoSuggestBoxTextChangedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.BitmapIcon" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.BitmapIconSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Border" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Button" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.CalendarDatePicker" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.CalendarView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.CalendarViewDayItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Canvas" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.CheckBox" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ChoosingGroupHeaderContainerEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ChoosingItemContainerEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ColumnDefinition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ComboBox" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ComboBoxItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.CommandBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.CommandBarOverflowPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.CommandingContainer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ContentControl" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ContentDialog" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ContentPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Control" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ControlTemplate" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.DataTemplateSelector" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.DatePicker" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.DragItemsStartingEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.DynamicOverflowItemsChangingEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.FlipView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.FlipViewItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Flyout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.FlyoutPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.FontIcon" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.FontIconSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Frame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Grid" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.GridView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.GridViewHeaderItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.GridViewItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.GroupItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.GroupStyle" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.GroupStyleSelector" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Hub" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.HubSection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.HubSectionHeaderClickEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.HyperlinkButton" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.IconElement" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.IconSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.IconSourceElement" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Image" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.InputValidationCommand" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.InputValidationContext" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.InputValidationError" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemClickEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsControl" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsPanelTemplate" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsStackPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsWrapGrid" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ListBox" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ListBoxItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ListView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ListViewBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ListViewHeaderItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ListViewItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ListViewPersistenceHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MediaPlayerElement" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MediaPlayerPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MediaTransportControls" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MediaTransportControlsHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MenuFlyout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MenuFlyoutItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MenuFlyoutPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MenuFlyoutSeparator" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MenuFlyoutSubItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Page" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Panel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PasswordBox" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PathIcon" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PathIconSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ButtonBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.CalendarPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.CarouselPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.DragCompletedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.DragDeltaEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.DragStartedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.FlyoutBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.FlyoutShowOptions" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.GeneratorPositionHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.GridViewItemPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.LayoutInformation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ListViewItemPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.Popup" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.RangeBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.RepeatButton" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ScrollBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ScrollEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.Selector" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.SelectorItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.Thumb" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.TickBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ToggleButton" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RadioButton" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RelativePanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RichEditBox" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RichTextBlock" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RichTextBlockOverflow" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RowDefinition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ScrollContentPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ScrollViewer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ScrollViewerViewChangedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SelectionChangedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SemanticZoom" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SemanticZoomLocation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SemanticZoomViewChangedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Slider" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SplitView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.StackPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.StyleSelector" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SwapChainBackgroundPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SwapChainPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SymbolIcon" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SymbolIconSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TextBlock" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TextBox" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TimePicker" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ToggleMenuFlyoutItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ToggleSwitch" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ToolTip" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ToolTipService" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.UserControl" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.VariableSizedWrapGrid" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Viewbox" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.VirtualizingStackPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.WrapGrid" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.CornerRadiusHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Data.Binding" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Data.BindingBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Data.BindingOperations" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Data.CollectionViewSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Data.CurrentChangingEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Data.DataErrorsChangedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Data.ItemIndexRange" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Data.PropertyChangedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Data.RelativeSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.DataTemplate" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.DataTemplateKey" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.DependencyObject" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.DependencyObjectCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.DependencyProperty" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.DispatcherTimer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Block" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Bold" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Glyphs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Hyperlink" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Inline" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.InlineUIContainer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Italic" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.LineBreak" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Paragraph" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Run" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Span" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.TextElement" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.TextHighlighter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Typography" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Documents.Underline" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.DurationHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.DxamlCoreTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.ElementFactoryGetArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.ElementFactoryRecycleArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.ElementSoundPlayer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.EventTrigger" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.FrameworkElement" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.FrameworkElementEx" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.FrameworkTemplate" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.FrameworkView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.FrameworkViewSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.GridLengthHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Hosting.DesktopWindowXamlSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Hosting.ElementCompositionPreview" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Hosting.WindowsXamlManager" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Hosting.XamlIslandRoot" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Hosting.XamlSourceFocusNavigationRequest" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Hosting.XamlSourceFocusNavigationResult" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.AccessKeyDisplayDismissedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.AccessKeyDisplayRequestedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.AccessKeyInvokedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.AccessKeyManager" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.ContextRequestedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.DoubleTappedRoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.FindNextElementOptions" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.FocusManager" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.HoldingRoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.InputManager" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.InputScope" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.InputScopeName" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.KeyboardAccelerator" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.ManipulationCompletedRoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.ManipulationDeltaRoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.ManipulationInertiaStartingRoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.ManipulationPivot" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.ManipulationStartedRoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.ManipulationStartingRoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.RightTappedRoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.StandardUICommand" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.TappedRoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Input.XamlUICommand" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.InteractionBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Internal.LayoutTransitionElementUtilities" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Internal.SecondaryContentRelationship" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Interop.NotifyCollectionChangedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Markup.MarkupExtension" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Markup.ProvideValueTargetProperty" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Markup.XamlBinaryWriter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Markup.XamlBindingHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Markup.XamlMarkupHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Markup.XamlReader" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.AddDeleteThemeTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.BackEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.BasicConnectedAnimationConfiguration" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.BeginStoryboard" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.BounceEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.CircleEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ColorAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ColorAnimationUsingKeyFrames" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ColorKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ColorKeyFrameCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ConnectedAnimationService" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ContentThemeTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.CubicEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DirectConnectedAnimationConfiguration" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DiscreteColorKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DiscreteDoubleKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DiscreteObjectKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DiscretePointKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DoubleAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DoubleAnimationUsingKeyFrames" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DoubleKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DoubleKeyFrameCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DragItemThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DragOverThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DrillInThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DrillOutThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DropTargetItemThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.EasingColorKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.EasingDoubleKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.EasingFunctionBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.EasingPointKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.EdgeUIThemeTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ElasticEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.EntranceThemeTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ExponentialEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.FadeInThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.FadeOutThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.GravityConnectedAnimationConfiguration" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.KeySpline" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.KeyTimeHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.LinearColorKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.LinearDoubleKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.LinearPointKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.NavigationTransitionInfo" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ObjectAnimationUsingKeyFrames" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ObjectKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ObjectKeyFrameCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PaneThemeTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PointAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PointAnimationUsingKeyFrames" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PointerDownThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PointerUpThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PointKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PointKeyFrameCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PopInThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PopOutThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PopupThemeTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.PowerEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.QuadraticEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.QuarticEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.QuinticEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ReorderThemeTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.RepeatBehaviorHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.RepositionThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.RepositionThemeTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SineEase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SplineColorKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SplineDoubleKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SplinePointKeyFrame" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SplitCloseThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SplitOpenThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.Storyboard" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SwipeBackThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SwipeHintThemeAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ThemeAnimationBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.Timeline" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.TimelineCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.Transition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.TransitionCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.ArcSegment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.BezierSegment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.BitmapCache" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Brush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.BrushCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.CacheMode" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.CompositeTransform" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.CompositionTarget" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.DoubleCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.EllipseGeometry" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.FontFamily" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.GeneralTransform" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Geometry" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.GeometryCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.GeometryGroup" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.GradientBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.GradientStop" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.GradientStopCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.ImageBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Imaging.BitmapImage" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Imaging.BitmapSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Imaging.RenderTargetBitmap" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Imaging.SoftwareBitmapSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Imaging.SurfaceImageSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Imaging.SvgImageSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Imaging.VirtualSurfaceImageSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Imaging.WriteableBitmap" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Imaging.XamlRenderingBackgroundTask" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.LinearGradientBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.LineGeometry" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.LineSegment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.LoadedImageSurface" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Matrix3DProjection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.MatrixHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.MatrixTransform" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Media3D.CompositeTransform3D" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Media3D.Matrix3DHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Media3D.PerspectiveTransform3D" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Media3D.Transform3D" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.PathFigure" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.PathFigureCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.PathGeometry" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.PathSegmentCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.PlaneProjection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.PointCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.PolyBezierSegment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.PolyLineSegment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.PolyQuadraticBezierSegment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Projection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.QuadraticBezierSegment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.RectangleGeometry" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.RotateTransform" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.ScaleTransform" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.SkewTransform" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.SolidColorBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.SystemBackdrop" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.ThemeShadow" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.TileBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.TransformCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.TransformGroup" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.TranslateTransform" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.VisualTreeHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.XamlCompositionBrushBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.XamlLight" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Navigation.FrameNavigationOptions" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Navigation.PageStackEntry" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.PanelEx" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.PointHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Printing.AddPagesEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Printing.GetPreviewPageEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Printing.PaginateEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Printing.PrintDocument" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.PropertyMetadata" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.PropertyPath" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.RectHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.ResourceDictionary" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Resources.CustomXamlResourceLoader" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.RoutedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.ScalarTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Setter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.SetterBaseCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Shapes.Ellipse" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Shapes.Line" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Shapes.Path" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Shapes.Polygon" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Shapes.Polyline" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Shapes.Rectangle" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Shapes.Shape" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.SizeHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.StateTrigger" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.StateTriggerBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Style" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.TargetPropertyPath" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.ThicknessHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.TriggerActionCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.UIElement" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.UIElementWeakCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Vector3Transition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.VisualState" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.VisualStateChangedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.VisualStateGroup" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.VisualStateManager" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.VisualTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Window" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.WindowChrome" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.XamlIsland" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.UI.Xaml.Controls.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.AnimatedIconTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.ButtonInteraction" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.DisplayRegionHelperTestApi" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.ItemsViewTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.LayoutsTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.MUXControlsTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.PullToRefreshHelperTestApi" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.RadioButtonsTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.RepeaterTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.ScrollPresenterTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.ScrollViewerIRefreshInfoProviderAdapter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.ScrollViewTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.SelectorBarTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.SliderInteraction" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.SpectrumBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.SplitButtonTestApi" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.SwipeTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Controls.TeachingTipTestHooks" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Media.AcrylicTestApi" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Media.MaterialHelperTestApi" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Media.RevealBorderLight" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Media.RevealBrushTestApi" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Media.RevealHoverLight" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Media.RevealTestApi" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Private.Media.XamlAmbientLight" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.AnimatedVisualPlayerAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.BreadcrumbBarItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ColorPickerSliderAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ColorSpectrumAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.DropDownButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ExpanderAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.InfoBarAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.InkCanvasAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ItemContainerAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ItemsViewAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.MenuBarAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.NavigationViewAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.NavigationViewItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.NumberBoxAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.PagerControlAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.PersonPictureAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.PipsPagerAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ProgressBarAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.RadioButtonsAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.RatingControlAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.RepeaterAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ScrollPresenterAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.SelectorBarItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.TabViewAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.TabViewItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.TeachingTipAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.TreeViewItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.TreeViewListAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.WebView2AutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedIcon" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedIconSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedAcceptVisualSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedBackVisualSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronDownSmallVisualSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronRightDownSmallVisualSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronUpDownSmallVisualSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedFindVisualSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedGlobalNavigationButtonVisualSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedSettingsVisualSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnnotatedScrollBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.AnnotatedScrollBarLabel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.BreadcrumbBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.BreadcrumbBarItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ColorPicker" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.CommandBarFlyout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.DropDownButton" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ElementFactory" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Expander" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.FlowLayout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.FlowLayoutState" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ImageIcon" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ImageIconSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.IndexPath" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.InfoBadge" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.InfoBadgeTemplateSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.InfoBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.InfoBarTemplateSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.InkCanvas" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemCollectionTransitionProvider" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemContainer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsRepeater" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsRepeaterScrollHost" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsSourceView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.LayoutPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.LinedFlowLayout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.LinedFlowLayoutItemCollectionTransitionProvider" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MapControl" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MapElementsLayer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MapIcon" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MenuBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MenuBarItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.MenuBarItemFlyout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NavigationView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NavigationViewItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NavigationViewItemBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NavigationViewItemHeader" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NavigationViewItemInvokedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NavigationViewItemSeparator" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NonVirtualizingLayout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.NumberBox" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PagerControl" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PagerControlTemplateSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ParallaxView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PersonPicture" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PipsPager" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.AutoSuggestBoxHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ColorPickerSlider" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ComboBoxHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarAutomationProperties" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.RepeatedScrollSnapPoint" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.RepeatedZoomSnapPoint" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerAddScrollVelocityRequestedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerPanRequestedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerScrollByRequestedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerScrollToRequestedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ScrollPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ScrollSnapPoint" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.TabViewListView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.ZoomSnapPoint" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ProgressBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ProgressRing" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RadioButtons" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RatingControl" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RatingItemFontInfo" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RatingItemImageInfo" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RatingItemInfo" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RecyclePool" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RecyclingElementFactory" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RefreshContainer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RefreshVisualizer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.RevealListViewItemPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ScrollingScrollOptions" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ScrollingZoomOptions" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ScrollView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SelectionModel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SelectorBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SelectorBarItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SplitButton" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.StackLayout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.StackLayoutState" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SwipeControl" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SwipeItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.SwipeItems" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TabView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TabViewItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TabViewItemTemplateSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TeachingTip" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TextCommandBarFlyout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TitleBar" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TitleBarAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TitleBarTemplateSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ToggleSplitButton" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TreeView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TreeViewItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TreeViewList" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TreeViewNode" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TwoPaneView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.UniformGridLayout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.UniformGridLayoutState" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.VirtualizingLayout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.WebView2" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.XamlControlsResources" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.AcrylicBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.DesktopAcrylicBackdrop" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.MicaBackdrop" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.RadialGradientBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.RevealBackgroundBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.RevealBorderBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.RevealBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.UI.Xaml.Phone.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.PivotAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.PivotItemAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Automation.Peers.PivotItemDataAutomationPeer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.DatePickedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.DatePickerFlyout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.DatePickerFlyoutItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.DatePickerFlyoutPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ItemsPickedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.ListPickerFlyout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PickerConfirmedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PickerFlyout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Pivot" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PivotItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.PivotItemEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.JumpListItemBackgroundConverter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.JumpListItemForegroundConverter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.LoopingSelector" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.PickerFlyoutBase" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.PivotHeaderItem" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.PivotHeaderPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.Primitives.PivotPanel" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TimePickedEventArgs" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TimePickerFlyout" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Controls.TimePickerFlyoutPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.CommonNavigationTransitionInfo" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.ContinuumNavigationTransitionInfo" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.DrillInNavigationTransitionInfo" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.EntranceNavigationTransitionInfo" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.NavigationThemeTransition" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SlideNavigationTransitionInfo" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Xaml.Media.Animation.SuppressNavigationTransitionInfo" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.Windows.ApplicationModel.Resources.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.Windows.ApplicationModel.Resources.KnownResourceQualifierName" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.Windows.ApplicationModel.Resources.ResourceCandidate" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.Windows.ApplicationModel.Resources.ResourceLoader" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.Windows.ApplicationModel.Resources.ResourceManager" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>WinUIEdit.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Text.FontWeights" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Text.TextConstants" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.Web.WebView2.Core.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2CompositionController" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2ControllerWindowReference" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2CustomSchemeRegistration" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2Environment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>CoreMessagingXP.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Dispatching.DispatcherExitDeferral" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Dispatching.DispatcherQueue" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Dispatching.DispatcherQueueController" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>dcompi.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.AnimationController" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionApiInformation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionCapabilities" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionClip" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionDrawingSurface" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionEasingFunction" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionEffectSourceParameter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionGeometry" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionGradientBrush" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionLight" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionObject" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionPath" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionProjectedShadowCasterCollection" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionShadow" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionShape" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionTransform" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.CompositionVirtualDrawingSurface" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Compositor" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.ContainerVisual" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Core.CompositorController" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Diagnostics.CompositionDebugSettings" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Effects.SceneLightingEffect" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Experimental.ExpCompositionVisualSurface" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Interactions.CompositionConditionalValue" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Interactions.InteractionTracker" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaModifier" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaMotion" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaNaturalMotion" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaRestingValue" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Interactions.InteractionTrackerVector2InertiaModifier" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Interactions.InteractionTrackerVector2InertiaNaturalMotion" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Interactions.VisualInteractionSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.KeyFrameAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.NaturalMotionAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.ScalarNaturalMotionAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneComponent" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneMaterial" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneMaterialInput" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneMesh" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneMeshRendererComponent" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneMetallicRoughnessMaterial" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneNode" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneObject" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.ScenePbrMaterial" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneRendererComponent" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneSurfaceMaterialInput" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Scenes.SceneVisual" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Vector2NaturalMotionAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Vector3NaturalMotionAnimation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.Visual" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.Graphics.Display.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.Graphics.Display.DisplayInformation" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.UI.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Colors" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.ColorHelper" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.System.ThemeSettings" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.UI.Input.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ChildSiteLink" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentAppWindowBridge" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentCoordinateConverter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentExternalBackdropLink" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentExternalOutputLink" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentIsland" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentIslandEnvironment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentSite" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentSiteEnvironment" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentSiteEnvironmentView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ContentSiteView" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.CoreWindowSiteBridge" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.DesktopAttachedSiteBridge" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.DesktopChildSiteBridge" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.DesktopPopupSiteBridge" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.DesktopSiteBridge" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.ProcessStarter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Content.SystemVisualSiteBridge" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.DragDrop.DragDropManager" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.DragDrop.DragOperation" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.Experimental.ExpPointerPoint" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.FocusNavigationRequest" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.GestureRecognizer" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputActivationListener" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputCursor" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputCustomCursor" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputDesktopResourceCursor" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputDesktopNamedResourceCursor" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputFocusController" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputFocusNavigationHost" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputKeyboardSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputLightDismissAction" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputObject" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputPreTranslateKeyboardSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputNonClientPointerSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputPointerSource" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.InputSystemCursor" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.Interop.PenDeviceInterop" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Input.PointerPredictor" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.UI.Windowing.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Windowing.AppWindow" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Windowing.AppWindowPlacementDetails" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Windowing.AppWindowPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Windowing.CompactOverlayPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Windowing.DisplayArea" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Windowing.FullScreenPresenter" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Windowing.OverlappedPresenter" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>Microsoft.UI.Windowing.Core.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Windowing.AppWindowTitleBar" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
  <Extension Category="windows.activatableClass.inProcessServer" xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
    <InProcessServer>
      <Path>wuceffectsi.dll</Path>
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.SystemBackdrops.DesktopAcrylicController" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.SystemBackdrops.MicaController" ThreadingModel="both" />
      <ActivatableClass ActivatableClassId="Microsoft.UI.Composition.SystemBackdrops.SystemBackdropConfiguration" ThreadingModel="both" />
    </InProcessServer>
  </Extension>
</Data>