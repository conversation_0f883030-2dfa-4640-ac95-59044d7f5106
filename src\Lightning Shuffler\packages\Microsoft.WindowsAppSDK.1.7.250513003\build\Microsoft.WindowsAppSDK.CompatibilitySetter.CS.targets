<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. See LICENSE in the project root for license information. -->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Condition="'$(WindowsAppSDKRuntimePatchLevel1)' != '' or '$(WindowsAppSDKRuntimePatchLevel2)' != '' or '$(WindowsAppSDKDisabledChanges)' != ''">
      <WindowsAppSdkCompatibilityInitialize>true</WindowsAppSdkCompatibilityInitialize>
  </PropertyGroup>

  <Target Name="WindowsAppSdkCompatibilitySetterTarget" BeforeTargets="CoreCompile"
          Condition="'$(WindowsAppSDKRuntimePatchLevel1)' != '' or '$(WindowsAppSDKRuntimePatchLevel2)' != '' or '$(WindowsAppSDKDisabledChanges)' != ''">
    <PropertyGroup>
      <WindowsAppSDKGeneratedFilesDir Condition="'$(WindowsAppSDKGeneratedFilesDir)' == '' and '$(GeneratedFilesDir)' != ''">$(GeneratedFilesDir)\WindowsAppSDK\</WindowsAppSDKGeneratedFilesDir>
      <WindowsAppSDKGeneratedFilesDir Condition="'$(WindowsAppSDKGeneratedFilesDir)' == ''">$([MSBuild]::NormalizeDirectory('$(MSBuildProjectDirectory)', '$(IntermediateOutputPath)', 'Generated Files', 'WindowsAppSDK'))</WindowsAppSDKGeneratedFilesDir>
        
      <WindowsAppSDKCompatibilitySetterFile>$(WindowsAppSDKGeneratedFilesDir)WindowsAppSDKCompatibilitySetter.cs</WindowsAppSDKCompatibilitySetterFile>
      <WindowsAppSDKCompatibilityPatchLevel1Lines Condition="'$(WindowsAppSDKRuntimePatchLevel1)' != ''">
            compatibilityOptions.PatchLevel1 = new WindowsAppRuntimeVersion($(WindowsAppSDKRuntimePatchLevel1.Replace(".", ",")))%3B
      </WindowsAppSDKCompatibilityPatchLevel1Lines>
      <WindowsAppSDKCompatibilityPatchLevel2Lines Condition="'$(WindowsAppSDKRuntimePatchLevel2)' != ''">
            compatibilityOptions.PatchLevel2 = new WindowsAppRuntimeVersion($(WindowsAppSDKRuntimePatchLevel2.Replace(".", ",")))%3B
      </WindowsAppSDKCompatibilityPatchLevel2Lines>

      <WindowsAppSDKCompatibilityDisabledChangesNoSpaces Condition="'$(WindowsAppSDKDisabledChanges)' != ''">$([System.Text.RegularExpressions.Regex]::Replace($(WindowsAppSDKDisabledChanges), "\s+", ""))</WindowsAppSDKCompatibilityDisabledChangesNoSpaces>
      <WindowsAppSDKCompatibilityDisabledChangesLines Condition="'$(WindowsAppSDKCompatibilityDisabledChangesNoSpaces)' != ''">
            var disabledChangesArray = new RuntimeCompatibilityChange[] { RuntimeCompatibilityChange.$([System.Text.RegularExpressions.Regex]::Replace($(WindowsAppSDKCompatibilityDisabledChangesNoSpaces), ",([A-Za-z])", ", RuntimeCompatibilityChange.$1")) }%3B
            foreach (var changeId in disabledChangesArray)
            {
                compatibilityOptions.DisabledChanges.Add(changeId)%3B
            }
      </WindowsAppSDKCompatibilityDisabledChangesLines>

      <WindowsAppSDKCompatibilitySetterFileLines>
using Microsoft.Windows.ApplicationModel.WindowsAppRuntime%3B

// This file is generated by the build based on project properties.
namespace Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Compatibility
{
    class AutoInitialize
    {
        // Called by WindowsAppRuntimeAutoInitializer.cs
        internal static void ConfigureRuntimeCompatibility()
        {
            var compatibilityOptions = new RuntimeCompatibilityOptions()%3B
$(WindowsAppSDKCompatibilityPatchLevel1Lines)
$(WindowsAppSDKCompatibilityPatchLevel2Lines)
$(WindowsAppSDKCompatibilityDisabledChangesLines)
            compatibilityOptions.Apply()%3B
        }
    }
}
      </WindowsAppSDKCompatibilitySetterFileLines>
    </PropertyGroup>

    <WriteLinesToFile
        File="$(WindowsAppSDKCompatibilitySetterFile)" Lines="$(WindowsAppSDKCompatibilitySetterFileLines)"
        Overwrite="true"
        WriteOnlyWhenDifferent="true" />

    <ItemGroup>
        <Compile Include="$(WindowsAppSDKCompatibilitySetterFile)" />
    </ItemGroup>
  </Target>

</Project>
