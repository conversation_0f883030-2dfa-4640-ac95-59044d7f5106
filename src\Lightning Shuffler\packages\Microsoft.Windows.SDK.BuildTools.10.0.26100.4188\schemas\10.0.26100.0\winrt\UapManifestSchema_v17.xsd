<?xml version="1.0" encoding="utf-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://schemas.microsoft.com/appx/manifest/uap/windows10/17"
           xmlns="http://schemas.microsoft.com/appx/manifest/uap/windows10/17"
           xmlns:t="http://schemas.microsoft.com/appx/manifest/types"
           xmlns:f="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
           xmlns:uap10="http://schemas.microsoft.com/appx/manifest/uap/windows10/10"
           xmlns:uap11="http://schemas.microsoft.com/appx/manifest/uap/windows10/11"
           xmlns:desktop11="http://schemas.microsoft.com/appx/manifest/desktop/windows10/11"
           >

  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/types"/>
  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/foundation/windows10"/>
  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/uap/windows10/10"/>
  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/uap/windows10/11"/>
  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/desktop/windows10/11"/>

  <!-- NOTE: Copied from UAP16 namespace added for zinc namespace during Nov 2022 not break to existing mapping-->
  <!-- Per manifest review disussion, Uap17 should be used for zinc namespace specific changes going foward-->

  <xs:attribute name="BaseNamedObjectsIsolation" type="t:ST_BaseNamedObjectsIsolation" />
  <xs:element name="UpdateWhileInUse" type="t:ST_UpdateWhileInUse"/>

  <!-- End of Copy from UAP16 namespace -->
  
  <xs:element name="Extension" substitutionGroup="f:ExtensionChoice">
    <xs:complexType>
      <xs:choice minOccurs="0">
        <xs:element name="PackageExtensionHost" type="CT_PackageExtensionHost"/>
        <xs:element name="PackageExtension" type="CT_PackageExtension"/>
      </xs:choice>
      <xs:attribute name="Category" type="t:ST_ExtensionCategory_Uap17" use="required"/>
      <xs:attribute ref="desktop11:AppLifecycleBehavior" use="optional"/>
      <xs:attributeGroup ref="t:ExtensionBaseAttributes"/>
      <xs:attributeGroup ref="uap10:TrustLevelGroup"/>
      <xs:attributeGroup ref="uap11:ManganeseExtensionAttributesGroup"/>
    </xs:complexType>
  </xs:element>

  <xs:complexType name="CT_PackageExtensionHost">
    <xs:sequence>
      <xs:element name="Name" type="t:ST_AppServiceNameLonger" minOccurs="1" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <xs:complexType name="CT_PackageExtension">
    <xs:sequence>
      <xs:element name="Properties" minOccurs="0" maxOccurs="1">
        <!-- Under this node, extension developers can provide custom data to pass a Host,
             so we are relaxing schema validations to allow custom data -->
        <xs:complexType>
          <xs:sequence>
            <xs:any minOccurs="0" maxOccurs="unbounded" processContents="skip"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
    <xs:attribute name="Name" type="t:ST_AppServiceNameLonger" use="required"/>
    <xs:attribute name="Id" type="t:ST_AppServiceName" use="required"/>
    <xs:attribute name="PublicFolder" type="t:ST_FileName" use="optional"/>
    <xs:attribute name="DisplayName" type="t:ST_DisplayName" use="required"/>
    <xs:attribute name="Description" type="t:ST_Description" use="optional"/>
  </xs:complexType>

  <xs:element name="PackageDependency" type="CT_PackageDependency"/>

  <xs:complexType name="CT_PackageDependency">
    <xs:complexContent>
      <xs:extension base="f:CT_PackageDependency">
        <xs:attribute name="Type" type="t:ST_PackageDependencyType" use="optional"/>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>

</xs:schema>

