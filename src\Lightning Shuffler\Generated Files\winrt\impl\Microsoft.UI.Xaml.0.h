// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_UI_Xaml_0_H
#define WINRT_Microsoft_UI_Xaml_0_H
WINRT_EXPORT namespace winrt::Microsoft::UI::Composition
{
    struct AnimationPropertyInfo;
    struct Compositor;
    struct ICompositionAnimationBase;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Content
{
    struct ContentCoordinateConverter;
    struct ContentIsland;
    struct ContentIslandEnvironment;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Dispatching
{
    struct DispatcherQueue;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Input
{
    struct InputCursor;
    struct PointerPoint;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Windowing
{
    struct AppWindow;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Automation::Peers
{
    struct AutomationPeer;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls
{
    struct ContainerContentChangingEventArgs;
    struct Control;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Controls::Primitives
{
    enum class ComponentResourceLocation : int32_t;
    struct FlyoutBase;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Data
{
    struct BindingBase;
    struct BindingExpression;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Input
{
    struct AccessKeyDisplayDismissedEventArgs;
    struct AccessKeyDisplayRequestedEventArgs;
    struct AccessKeyInvokedEventArgs;
    struct CharacterReceivedRoutedEventArgs;
    struct ContextRequestedEventArgs;
    struct DoubleTappedEventHandler;
    struct GettingFocusEventArgs;
    struct HoldingEventHandler;
    struct KeyEventHandler;
    enum class KeyTipPlacementMode : int32_t;
    struct KeyboardAcceleratorInvokedEventArgs;
    enum class KeyboardAcceleratorPlacementMode : int32_t;
    enum class KeyboardNavigationMode : int32_t;
    struct LosingFocusEventArgs;
    struct ManipulationCompletedEventHandler;
    struct ManipulationDeltaEventHandler;
    struct ManipulationInertiaStartingEventHandler;
    enum class ManipulationModes : uint32_t;
    struct ManipulationStartedEventHandler;
    struct ManipulationStartingEventHandler;
    struct NoFocusCandidateFoundEventArgs;
    struct Pointer;
    struct PointerEventHandler;
    struct ProcessKeyboardAcceleratorEventArgs;
    struct RightTappedEventHandler;
    struct TappedEventHandler;
    enum class XYFocusKeyboardNavigationMode : int32_t;
    enum class XYFocusNavigationStrategy : int32_t;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Media
{
    struct Brush;
    struct CacheMode;
    enum class ElementCompositeMode : int32_t;
    struct GeneralTransform;
    struct Projection;
    struct RectangleGeometry;
    struct Shadow;
    struct SystemBackdrop;
    struct Transform;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Media::Animation
{
    struct EasingFunctionBase;
    struct Storyboard;
    struct TransitionCollection;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Media::Imaging
{
    struct BitmapImage;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml::Media::Media3D
{
    struct Transform3D;
}
WINRT_EXPORT namespace winrt::Microsoft::Windows::ApplicationModel::Resources
{
    struct IResourceManager;
}
WINRT_EXPORT namespace winrt::Windows::ApplicationModel
{
    struct EnteredBackgroundEventArgs;
    struct LeavingBackgroundEventArgs;
    struct SuspendingEventArgs;
}
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::Activation
{
    struct LaunchActivatedEventArgs;
}
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::DataTransfer
{
    struct DataPackage;
    enum class DataPackageOperation : uint32_t;
    struct DataPackageView;
}
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::DataTransfer::DragDrop
{
    enum class DragDropModifiers : uint32_t;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    template <typename T> struct WINRT_IMPL_EMPTY_BASES EventHandler;
    struct EventRegistrationToken;
    struct HResult;
    template <typename T> struct WINRT_IMPL_EMPTY_BASES IReference;
    struct Point;
    struct Rect;
    struct Size;
    template <typename TSender, typename TResult> struct WINRT_IMPL_EMPTY_BASES TypedEventHandler;
    struct Uri;
}
WINRT_EXPORT namespace winrt::Windows::Foundation::Collections
{
    template <typename T> struct WINRT_IMPL_EMPTY_BASES IObservableVector;
    template <typename T> struct WINRT_IMPL_EMPTY_BASES IVector;
}
WINRT_EXPORT namespace winrt::Windows::Foundation::Numerics
{
}
WINRT_EXPORT namespace winrt::Windows::Graphics::Imaging
{
    struct SoftwareBitmap;
}
WINRT_EXPORT namespace winrt::Windows::UI
{
    struct Color;
}
WINRT_EXPORT namespace winrt::Windows::UI::Core
{
    struct CoreDispatcher;
    struct CoreWindow;
}
WINRT_EXPORT namespace winrt::Windows::UI::Xaml::Interop
{
    struct TypeName;
}
WINRT_EXPORT namespace winrt::Microsoft::UI::Xaml
{
    enum class ApplicationHighContrastAdjustment : uint32_t
    {
        None = 0,
        Auto = 0xffffffff,
    };
    enum class ApplicationRequiresPointerMode : int32_t
    {
        Auto = 0,
        WhenRequested = 1,
    };
    enum class ApplicationTheme : int32_t
    {
        Light = 0,
        Dark = 1,
    };
    enum class AutomationTextAttributesEnum : int32_t
    {
        AnimationStyleAttribute = 40000,
        BackgroundColorAttribute = 40001,
        BulletStyleAttribute = 40002,
        CapStyleAttribute = 40003,
        CultureAttribute = 40004,
        FontNameAttribute = 40005,
        FontSizeAttribute = 40006,
        FontWeightAttribute = 40007,
        ForegroundColorAttribute = 40008,
        HorizontalTextAlignmentAttribute = 40009,
        IndentationFirstLineAttribute = 40010,
        IndentationLeadingAttribute = 40011,
        IndentationTrailingAttribute = 40012,
        IsHiddenAttribute = 40013,
        IsItalicAttribute = 40014,
        IsReadOnlyAttribute = 40015,
        IsSubscriptAttribute = 40016,
        IsSuperscriptAttribute = 40017,
        MarginBottomAttribute = 40018,
        MarginLeadingAttribute = 40019,
        MarginTopAttribute = 40020,
        MarginTrailingAttribute = 40021,
        OutlineStylesAttribute = 40022,
        OverlineColorAttribute = 40023,
        OverlineStyleAttribute = 40024,
        StrikethroughColorAttribute = 40025,
        StrikethroughStyleAttribute = 40026,
        TabsAttribute = 40027,
        TextFlowDirectionsAttribute = 40028,
        UnderlineColorAttribute = 40029,
        UnderlineStyleAttribute = 40030,
        AnnotationTypesAttribute = 40031,
        AnnotationObjectsAttribute = 40032,
        StyleNameAttribute = 40033,
        StyleIdAttribute = 40034,
        LinkAttribute = 40035,
        IsActiveAttribute = 40036,
        SelectionActiveEndAttribute = 40037,
        CaretPositionAttribute = 40038,
        CaretBidiModeAttribute = 40039,
    };
    enum class DispatcherShutdownMode : int32_t
    {
        OnLastWindowClose = 0,
        OnExplicitShutdown = 1,
    };
    enum class DurationType : int32_t
    {
        Automatic = 0,
        TimeSpan = 1,
        Forever = 2,
    };
    enum class ElementHighContrastAdjustment : uint32_t
    {
        None = 0,
        Application = 0x80000000,
        Auto = 0xffffffff,
    };
    enum class ElementSoundKind : int32_t
    {
        Focus = 0,
        Invoke = 1,
        Show = 2,
        Hide = 3,
        MovePrevious = 4,
        MoveNext = 5,
        GoBack = 6,
    };
    enum class ElementSoundMode : int32_t
    {
        Default = 0,
        FocusOnly = 1,
        Off = 2,
    };
    enum class ElementSoundPlayerState : int32_t
    {
        Auto = 0,
        Off = 1,
        On = 2,
    };
    enum class ElementSpatialAudioMode : int32_t
    {
        Auto = 0,
        Off = 1,
        On = 2,
    };
    enum class ElementTheme : int32_t
    {
        Default = 0,
        Light = 1,
        Dark = 2,
    };
    enum class FlowDirection : int32_t
    {
        LeftToRight = 0,
        RightToLeft = 1,
    };
    enum class FocusState : int32_t
    {
        Unfocused = 0,
        Pointer = 1,
        Keyboard = 2,
        Programmatic = 3,
    };
    enum class FocusVisualKind : int32_t
    {
        DottedLine = 0,
        HighVisibility = 1,
        Reveal = 2,
    };
    enum class FontCapitals : int32_t
    {
        Normal = 0,
        AllSmallCaps = 1,
        SmallCaps = 2,
        AllPetiteCaps = 3,
        PetiteCaps = 4,
        Unicase = 5,
        Titling = 6,
    };
    enum class FontEastAsianLanguage : int32_t
    {
        Normal = 0,
        HojoKanji = 1,
        Jis04 = 2,
        Jis78 = 3,
        Jis83 = 4,
        Jis90 = 5,
        NlcKanji = 6,
        Simplified = 7,
        Traditional = 8,
        TraditionalNames = 9,
    };
    enum class FontEastAsianWidths : int32_t
    {
        Normal = 0,
        Full = 1,
        Half = 2,
        Proportional = 3,
        Quarter = 4,
        Third = 5,
    };
    enum class FontFraction : int32_t
    {
        Normal = 0,
        Stacked = 1,
        Slashed = 2,
    };
    enum class FontNumeralAlignment : int32_t
    {
        Normal = 0,
        Proportional = 1,
        Tabular = 2,
    };
    enum class FontNumeralStyle : int32_t
    {
        Normal = 0,
        Lining = 1,
        OldStyle = 2,
    };
    enum class FontVariants : int32_t
    {
        Normal = 0,
        Superscript = 1,
        Subscript = 2,
        Ordinal = 3,
        Inferior = 4,
        Ruby = 5,
    };
    enum class GridUnitType : int32_t
    {
        Auto = 0,
        Pixel = 1,
        Star = 2,
    };
    enum class HorizontalAlignment : int32_t
    {
        Left = 0,
        Center = 1,
        Right = 2,
        Stretch = 3,
    };
    enum class LayoutCycleDebugBreakLevel : int32_t
    {
        None = 0,
        Low = 1,
        High = 2,
    };
    enum class LayoutCycleTracingLevel : int32_t
    {
        None = 0,
        Low = 1,
        High = 2,
    };
    enum class LineStackingStrategy : int32_t
    {
        MaxHeight = 0,
        BlockLineHeight = 1,
        BaselineToBaseline = 2,
    };
    enum class OpticalMarginAlignment : int32_t
    {
        None = 0,
        TrimSideBearings = 1,
    };
    enum class TextAlignment : int32_t
    {
        Center = 0,
        Left = 1,
        Start = 1,
        Right = 2,
        End = 2,
        Justify = 3,
        DetectFromContent = 4,
    };
    enum class TextLineBounds : int32_t
    {
        Full = 0,
        TrimToCapHeight = 1,
        TrimToBaseline = 2,
        Tight = 3,
    };
    enum class TextReadingOrder : int32_t
    {
        Default = 0,
        UseFlowDirection = 0,
        DetectFromContent = 1,
    };
    enum class TextTrimming : int32_t
    {
        None = 0,
        CharacterEllipsis = 1,
        WordEllipsis = 2,
        Clip = 3,
    };
    enum class TextWrapping : int32_t
    {
        NoWrap = 1,
        Wrap = 2,
        WrapWholeWords = 3,
    };
    enum class Vector3TransitionComponents : uint32_t
    {
        X = 0x1,
        Y = 0x2,
        Z = 0x4,
    };
    enum class VerticalAlignment : int32_t
    {
        Top = 0,
        Center = 1,
        Bottom = 2,
        Stretch = 3,
    };
    enum class Visibility : int32_t
    {
        Visible = 0,
        Collapsed = 1,
    };
    enum class WindowActivationState : int32_t
    {
        CodeActivated = 0,
        Deactivated = 1,
        PointerActivated = 2,
    };
    struct IAdaptiveTrigger;
    struct IAdaptiveTriggerFactory;
    struct IAdaptiveTriggerStatics;
    struct IApplication;
    struct IApplication2;
    struct IApplication3;
    struct IApplicationFactory;
    struct IApplicationInitializationCallbackParams;
    struct IApplicationOverrides;
    struct IApplicationStatics;
    struct IBindingFailedEventArgs;
    struct IBringIntoViewOptions;
    struct IBringIntoViewRequestedEventArgs;
    struct IBrushTransition;
    struct IBrushTransitionFactory;
    struct IColorPaletteResources;
    struct IColorPaletteResourcesFactory;
    struct ICornerRadiusHelper;
    struct ICornerRadiusHelperStatics;
    struct IDataContextChangedEventArgs;
    struct IDataTemplate;
    struct IDataTemplateExtension;
    struct IDataTemplateFactory;
    struct IDataTemplateKey;
    struct IDataTemplateKeyFactory;
    struct IDataTemplateStatics;
    struct IDebugSettings;
    struct IDebugSettings2;
    struct IDebugSettings3;
    struct IDependencyObject;
    struct IDependencyObjectCollectionFactory;
    struct IDependencyObjectFactory;
    struct IDependencyProperty;
    struct IDependencyPropertyChangedEventArgs;
    struct IDependencyPropertyStatics;
    struct IDispatcherTimer;
    struct IDispatcherTimerFactory;
    struct IDragEventArgs;
    struct IDragOperationDeferral;
    struct IDragStartingEventArgs;
    struct IDragUI;
    struct IDragUIOverride;
    struct IDropCompletedEventArgs;
    struct IDurationHelper;
    struct IDurationHelperStatics;
    struct IEffectiveViewportChangedEventArgs;
    struct IElementFactory;
    struct IElementFactoryGetArgs;
    struct IElementFactoryGetArgsFactory;
    struct IElementFactoryRecycleArgs;
    struct IElementFactoryRecycleArgsFactory;
    struct IElementSoundPlayer;
    struct IElementSoundPlayerStatics;
    struct IEventTrigger;
    struct IExceptionRoutedEventArgs;
    struct IExceptionRoutedEventArgsFactory;
    struct IFrameworkElement;
    struct IFrameworkElementFactory;
    struct IFrameworkElementOverrides;
    struct IFrameworkElementProtected;
    struct IFrameworkElementStatics;
    struct IFrameworkTemplate;
    struct IFrameworkTemplateFactory;
    struct IFrameworkView;
    struct IFrameworkViewSource;
    struct IGridLengthHelper;
    struct IGridLengthHelperStatics;
    struct ILaunchActivatedEventArgs;
    struct IMediaFailedRoutedEventArgs;
    struct IPointHelper;
    struct IPointHelperStatics;
    struct IPropertyMetadata;
    struct IPropertyMetadataFactory;
    struct IPropertyMetadataStatics;
    struct IPropertyPath;
    struct IPropertyPathFactory;
    struct IRectHelper;
    struct IRectHelperStatics;
    struct IResourceDictionary;
    struct IResourceDictionaryFactory;
    struct IResourceManagerRequestedEventArgs;
    struct IRoutedEvent;
    struct IRoutedEventArgs;
    struct IRoutedEventArgsFactory;
    struct IScalarTransition;
    struct IScalarTransitionFactory;
    struct ISetter;
    struct ISetterBase;
    struct ISetterBaseCollection;
    struct ISetterBaseFactory;
    struct ISetterFactory;
    struct ISizeChangedEventArgs;
    struct ISizeHelper;
    struct ISizeHelperStatics;
    struct IStateTrigger;
    struct IStateTriggerBase;
    struct IStateTriggerBaseFactory;
    struct IStateTriggerBaseProtected;
    struct IStateTriggerStatics;
    struct IStyle;
    struct IStyleFactory;
    struct ITargetPropertyPath;
    struct ITargetPropertyPathFactory;
    struct IThicknessHelper;
    struct IThicknessHelperStatics;
    struct ITriggerAction;
    struct ITriggerActionFactory;
    struct ITriggerBase;
    struct ITriggerBaseFactory;
    struct IUIElement;
    struct IUIElementFactory;
    struct IUIElementOverrides;
    struct IUIElementProtected;
    struct IUIElementStatics;
    struct IUIElementWeakCollectionFactory;
    struct IUnhandledExceptionEventArgs;
    struct IVector3Transition;
    struct IVector3TransitionFactory;
    struct IVisualState;
    struct IVisualStateChangedEventArgs;
    struct IVisualStateGroup;
    struct IVisualStateManager;
    struct IVisualStateManagerFactory;
    struct IVisualStateManagerOverrides;
    struct IVisualStateManagerProtected;
    struct IVisualStateManagerStatics;
    struct IVisualTransition;
    struct IVisualTransitionFactory;
    struct IWindow;
    struct IWindow2;
    struct IWindowActivatedEventArgs;
    struct IWindowEventArgs;
    struct IWindowFactory;
    struct IWindowSizeChangedEventArgs;
    struct IWindowStatics;
    struct IWindowVisibilityChangedEventArgs;
    struct IXamlIsland;
    struct IXamlIslandFactory;
    struct IXamlResourceReferenceFailedEventArgs;
    struct IXamlRoot;
    struct IXamlRoot2;
    struct IXamlRoot3;
    struct IXamlRoot4;
    struct IXamlRootChangedEventArgs;
    struct IXamlServiceProvider;
    struct AdaptiveTrigger;
    struct Application;
    struct ApplicationInitializationCallbackParams;
    struct BindingFailedEventArgs;
    struct BringIntoViewOptions;
    struct BringIntoViewRequestedEventArgs;
    struct BrushTransition;
    struct ColorPaletteResources;
    struct CornerRadiusHelper;
    struct DataContextChangedEventArgs;
    struct DataTemplate;
    struct DataTemplateKey;
    struct DebugSettings;
    struct DependencyObject;
    struct DependencyObjectCollection;
    struct DependencyProperty;
    struct DependencyPropertyChangedEventArgs;
    struct DispatcherTimer;
    struct DragEventArgs;
    struct DragOperationDeferral;
    struct DragStartingEventArgs;
    struct DragUI;
    struct DragUIOverride;
    struct DropCompletedEventArgs;
    struct DurationHelper;
    struct EffectiveViewportChangedEventArgs;
    struct ElementFactoryGetArgs;
    struct ElementFactoryRecycleArgs;
    struct ElementSoundPlayer;
    struct EventTrigger;
    struct ExceptionRoutedEventArgs;
    struct FrameworkElement;
    struct FrameworkTemplate;
    struct FrameworkView;
    struct FrameworkViewSource;
    struct GridLengthHelper;
    struct LaunchActivatedEventArgs;
    struct MediaFailedRoutedEventArgs;
    struct PointHelper;
    struct PropertyMetadata;
    struct PropertyPath;
    struct RectHelper;
    struct ResourceDictionary;
    struct ResourceManagerRequestedEventArgs;
    struct RoutedEvent;
    struct RoutedEventArgs;
    struct ScalarTransition;
    struct Setter;
    struct SetterBase;
    struct SetterBaseCollection;
    struct SizeChangedEventArgs;
    struct SizeHelper;
    struct StateTrigger;
    struct StateTriggerBase;
    struct Style;
    struct TargetPropertyPath;
    struct ThicknessHelper;
    struct TriggerAction;
    struct TriggerActionCollection;
    struct TriggerBase;
    struct TriggerCollection;
    struct UIElement;
    struct UIElementWeakCollection;
    struct UnhandledExceptionEventArgs;
    struct Vector3Transition;
    struct VisualState;
    struct VisualStateChangedEventArgs;
    struct VisualStateGroup;
    struct VisualStateManager;
    struct VisualTransition;
    struct Window;
    struct WindowActivatedEventArgs;
    struct WindowEventArgs;
    struct WindowSizeChangedEventArgs;
    struct WindowVisibilityChangedEventArgs;
    struct XamlIsland;
    struct XamlResourceReferenceFailedEventArgs;
    struct XamlRoot;
    struct XamlRootChangedEventArgs;
    struct CornerRadius;
    struct Duration;
    struct GridLength;
    struct Thickness;
    struct ApplicationInitializationCallback;
    struct BindingFailedEventHandler;
    struct CreateDefaultValueCallback;
    struct DependencyPropertyChangedCallback;
    struct DependencyPropertyChangedEventHandler;
    struct DragEventHandler;
    struct EnteredBackgroundEventHandler;
    struct ExceptionRoutedEventHandler;
    struct LeavingBackgroundEventHandler;
    struct PropertyChangedCallback;
    struct RoutedEventHandler;
    struct SizeChangedEventHandler;
    struct SuspendingEventHandler;
    struct UnhandledExceptionEventHandler;
    struct VisualStateChangedEventHandler;
    struct WinUIContract;
    struct XamlContract;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::UI::Xaml::IAdaptiveTrigger>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IApplication>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IApplication2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IApplication3>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IApplicationFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IApplicationInitializationCallbackParams>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IApplicationOverrides>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IApplicationStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IBindingFailedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IBringIntoViewOptions>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IBringIntoViewRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IBrushTransition>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IBrushTransitionFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IColorPaletteResources>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IColorPaletteResourcesFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ICornerRadiusHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ICornerRadiusHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDataContextChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDataTemplate>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDataTemplateExtension>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDataTemplateFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDataTemplateKey>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDataTemplateKeyFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDataTemplateStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDebugSettings>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDebugSettings2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDebugSettings3>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDependencyObject>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDependencyObjectCollectionFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDependencyObjectFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDependencyProperty>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDependencyPropertyChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDependencyPropertyStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDispatcherTimer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDispatcherTimerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDragEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDragOperationDeferral>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDragStartingEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDragUI>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDragUIOverride>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDropCompletedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDurationHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IDurationHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IEffectiveViewportChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IElementFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgsFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgsFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IElementSoundPlayer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IElementSoundPlayerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IEventTrigger>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgsFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IFrameworkElement>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IFrameworkElementFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IFrameworkElementOverrides>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IFrameworkElementProtected>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IFrameworkElementStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IFrameworkTemplate>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IFrameworkTemplateFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IFrameworkView>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IFrameworkViewSource>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IGridLengthHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IGridLengthHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ILaunchActivatedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IMediaFailedRoutedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IPointHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IPointHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IPropertyMetadata>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IPropertyMetadataFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IPropertyMetadataStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IPropertyPath>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IPropertyPathFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IRectHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IRectHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IResourceDictionary>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IResourceDictionaryFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IResourceManagerRequestedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IRoutedEvent>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IRoutedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IRoutedEventArgsFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IScalarTransition>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IScalarTransitionFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ISetter>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ISetterBase>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ISetterBaseCollection>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ISetterBaseFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ISetterFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ISizeChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ISizeHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ISizeHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IStateTrigger>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IStateTriggerBase>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IStateTriggerBaseFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IStateTriggerBaseProtected>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IStateTriggerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IStyle>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IStyleFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ITargetPropertyPath>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ITargetPropertyPathFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IThicknessHelper>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IThicknessHelperStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ITriggerAction>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ITriggerActionFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ITriggerBase>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ITriggerBaseFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IUIElement>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IUIElementFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IUIElementOverrides>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IUIElementProtected>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IUIElementStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IUIElementWeakCollectionFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IUnhandledExceptionEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVector3Transition>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVector3TransitionFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualState>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualStateChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualStateGroup>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualStateManager>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualStateManagerFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualStateManagerOverrides>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualStateManagerProtected>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualStateManagerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualTransition>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IVisualTransitionFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IWindow>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IWindow2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IWindowActivatedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IWindowEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IWindowFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IWindowSizeChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IWindowStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IWindowVisibilityChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IXamlIsland>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IXamlIslandFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IXamlResourceReferenceFailedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IXamlRoot>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IXamlRoot2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IXamlRoot3>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IXamlRoot4>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IXamlRootChangedEventArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::IXamlServiceProvider>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::AdaptiveTrigger>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Application>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ApplicationInitializationCallbackParams>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::BindingFailedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::BringIntoViewOptions>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::BringIntoViewRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::BrushTransition>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ColorPaletteResources>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::CornerRadiusHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DataContextChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DataTemplate>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DataTemplateKey>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DebugSettings>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DependencyObject>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DependencyObjectCollection>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DependencyProperty>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DispatcherTimer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DragEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DragOperationDeferral>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DragStartingEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DragUI>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DragUIOverride>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DropCompletedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DurationHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::EffectiveViewportChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ElementFactoryGetArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ElementFactoryRecycleArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ElementSoundPlayer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::EventTrigger>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ExceptionRoutedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FrameworkElement>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FrameworkTemplate>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FrameworkView>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FrameworkViewSource>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::GridLengthHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::LaunchActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::MediaFailedRoutedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::PointHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::PropertyMetadata>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::PropertyPath>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::RectHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ResourceDictionary>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ResourceManagerRequestedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::RoutedEvent>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::RoutedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ScalarTransition>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Setter>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::SetterBase>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::SetterBaseCollection>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::SizeChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::SizeHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::StateTrigger>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::StateTriggerBase>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Style>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TargetPropertyPath>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ThicknessHelper>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TriggerAction>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TriggerActionCollection>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TriggerBase>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TriggerCollection>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::UIElement>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::UIElementWeakCollection>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::UnhandledExceptionEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Vector3Transition>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::VisualState>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::VisualStateChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::VisualStateGroup>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::VisualStateManager>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::VisualTransition>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Window>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::WindowActivatedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::WindowEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::WindowSizeChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::WindowVisibilityChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::XamlIsland>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::XamlResourceReferenceFailedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::XamlRoot>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::XamlRootChangedEventArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ApplicationHighContrastAdjustment>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ApplicationRequiresPointerMode>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ApplicationTheme>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::AutomationTextAttributesEnum>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DispatcherShutdownMode>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DurationType>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ElementHighContrastAdjustment>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ElementSoundKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ElementSoundMode>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ElementSoundPlayerState>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ElementSpatialAudioMode>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ElementTheme>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FlowDirection>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FocusState>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FocusVisualKind>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FontCapitals>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FontEastAsianLanguage>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FontEastAsianWidths>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FontFraction>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FontNumeralAlignment>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FontNumeralStyle>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::FontVariants>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::GridUnitType>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::HorizontalAlignment>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::LayoutCycleDebugBreakLevel>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::LayoutCycleTracingLevel>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::LineStackingStrategy>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::OpticalMarginAlignment>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TextAlignment>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TextLineBounds>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TextReadingOrder>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TextTrimming>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::TextWrapping>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Vector3TransitionComponents>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::VerticalAlignment>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Visibility>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::WindowActivationState>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::CornerRadius>{ using type = struct_category<double, double, double, double>; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Duration>{ using type = struct_category<winrt::Windows::Foundation::TimeSpan, winrt::Microsoft::UI::Xaml::DurationType>; };
    template <> struct category<winrt::Microsoft::UI::Xaml::GridLength>{ using type = struct_category<double, winrt::Microsoft::UI::Xaml::GridUnitType>; };
    template <> struct category<winrt::Microsoft::UI::Xaml::Thickness>{ using type = struct_category<double, double, double, double>; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ApplicationInitializationCallback>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::BindingFailedEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::CreateDefaultValueCallback>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedCallback>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::DragEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::EnteredBackgroundEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::ExceptionRoutedEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::LeavingBackgroundEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::PropertyChangedCallback>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::RoutedEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::SizeChangedEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::SuspendingEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::UnhandledExceptionEventHandler>{ using type = delegate_category; };
    template <> struct category<winrt::Microsoft::UI::Xaml::VisualStateChangedEventHandler>{ using type = delegate_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::AdaptiveTrigger> = L"Microsoft.UI.Xaml.AdaptiveTrigger";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Application> = L"Microsoft.UI.Xaml.Application";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ApplicationInitializationCallbackParams> = L"Microsoft.UI.Xaml.ApplicationInitializationCallbackParams";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::BindingFailedEventArgs> = L"Microsoft.UI.Xaml.BindingFailedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::BringIntoViewOptions> = L"Microsoft.UI.Xaml.BringIntoViewOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::BringIntoViewRequestedEventArgs> = L"Microsoft.UI.Xaml.BringIntoViewRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::BrushTransition> = L"Microsoft.UI.Xaml.BrushTransition";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ColorPaletteResources> = L"Microsoft.UI.Xaml.ColorPaletteResources";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::CornerRadiusHelper> = L"Microsoft.UI.Xaml.CornerRadiusHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DataContextChangedEventArgs> = L"Microsoft.UI.Xaml.DataContextChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DataTemplate> = L"Microsoft.UI.Xaml.DataTemplate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DataTemplateKey> = L"Microsoft.UI.Xaml.DataTemplateKey";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DebugSettings> = L"Microsoft.UI.Xaml.DebugSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DependencyObject> = L"Microsoft.UI.Xaml.DependencyObject";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DependencyObjectCollection> = L"Microsoft.UI.Xaml.DependencyObjectCollection";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DependencyProperty> = L"Microsoft.UI.Xaml.DependencyProperty";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedEventArgs> = L"Microsoft.UI.Xaml.DependencyPropertyChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DispatcherTimer> = L"Microsoft.UI.Xaml.DispatcherTimer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DragEventArgs> = L"Microsoft.UI.Xaml.DragEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DragOperationDeferral> = L"Microsoft.UI.Xaml.DragOperationDeferral";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DragStartingEventArgs> = L"Microsoft.UI.Xaml.DragStartingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DragUI> = L"Microsoft.UI.Xaml.DragUI";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DragUIOverride> = L"Microsoft.UI.Xaml.DragUIOverride";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DropCompletedEventArgs> = L"Microsoft.UI.Xaml.DropCompletedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DurationHelper> = L"Microsoft.UI.Xaml.DurationHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::EffectiveViewportChangedEventArgs> = L"Microsoft.UI.Xaml.EffectiveViewportChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ElementFactoryGetArgs> = L"Microsoft.UI.Xaml.ElementFactoryGetArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ElementFactoryRecycleArgs> = L"Microsoft.UI.Xaml.ElementFactoryRecycleArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ElementSoundPlayer> = L"Microsoft.UI.Xaml.ElementSoundPlayer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::EventTrigger> = L"Microsoft.UI.Xaml.EventTrigger";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ExceptionRoutedEventArgs> = L"Microsoft.UI.Xaml.ExceptionRoutedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FrameworkElement> = L"Microsoft.UI.Xaml.FrameworkElement";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FrameworkTemplate> = L"Microsoft.UI.Xaml.FrameworkTemplate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FrameworkView> = L"Microsoft.UI.Xaml.FrameworkView";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FrameworkViewSource> = L"Microsoft.UI.Xaml.FrameworkViewSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::GridLengthHelper> = L"Microsoft.UI.Xaml.GridLengthHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::LaunchActivatedEventArgs> = L"Microsoft.UI.Xaml.LaunchActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::MediaFailedRoutedEventArgs> = L"Microsoft.UI.Xaml.MediaFailedRoutedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::PointHelper> = L"Microsoft.UI.Xaml.PointHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::PropertyMetadata> = L"Microsoft.UI.Xaml.PropertyMetadata";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::PropertyPath> = L"Microsoft.UI.Xaml.PropertyPath";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::RectHelper> = L"Microsoft.UI.Xaml.RectHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ResourceDictionary> = L"Microsoft.UI.Xaml.ResourceDictionary";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ResourceManagerRequestedEventArgs> = L"Microsoft.UI.Xaml.ResourceManagerRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::RoutedEvent> = L"Microsoft.UI.Xaml.RoutedEvent";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::RoutedEventArgs> = L"Microsoft.UI.Xaml.RoutedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ScalarTransition> = L"Microsoft.UI.Xaml.ScalarTransition";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Setter> = L"Microsoft.UI.Xaml.Setter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::SetterBase> = L"Microsoft.UI.Xaml.SetterBase";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::SetterBaseCollection> = L"Microsoft.UI.Xaml.SetterBaseCollection";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::SizeChangedEventArgs> = L"Microsoft.UI.Xaml.SizeChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::SizeHelper> = L"Microsoft.UI.Xaml.SizeHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::StateTrigger> = L"Microsoft.UI.Xaml.StateTrigger";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::StateTriggerBase> = L"Microsoft.UI.Xaml.StateTriggerBase";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Style> = L"Microsoft.UI.Xaml.Style";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TargetPropertyPath> = L"Microsoft.UI.Xaml.TargetPropertyPath";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ThicknessHelper> = L"Microsoft.UI.Xaml.ThicknessHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TriggerAction> = L"Microsoft.UI.Xaml.TriggerAction";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TriggerActionCollection> = L"Microsoft.UI.Xaml.TriggerActionCollection";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TriggerBase> = L"Microsoft.UI.Xaml.TriggerBase";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TriggerCollection> = L"Microsoft.UI.Xaml.TriggerCollection";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::UIElement> = L"Microsoft.UI.Xaml.UIElement";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::UIElementWeakCollection> = L"Microsoft.UI.Xaml.UIElementWeakCollection";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::UnhandledExceptionEventArgs> = L"Microsoft.UI.Xaml.UnhandledExceptionEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Vector3Transition> = L"Microsoft.UI.Xaml.Vector3Transition";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::VisualState> = L"Microsoft.UI.Xaml.VisualState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::VisualStateChangedEventArgs> = L"Microsoft.UI.Xaml.VisualStateChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::VisualStateGroup> = L"Microsoft.UI.Xaml.VisualStateGroup";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::VisualStateManager> = L"Microsoft.UI.Xaml.VisualStateManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::VisualTransition> = L"Microsoft.UI.Xaml.VisualTransition";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Window> = L"Microsoft.UI.Xaml.Window";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::WindowActivatedEventArgs> = L"Microsoft.UI.Xaml.WindowActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::WindowEventArgs> = L"Microsoft.UI.Xaml.WindowEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::WindowSizeChangedEventArgs> = L"Microsoft.UI.Xaml.WindowSizeChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::WindowVisibilityChangedEventArgs> = L"Microsoft.UI.Xaml.WindowVisibilityChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::XamlIsland> = L"Microsoft.UI.Xaml.XamlIsland";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::XamlResourceReferenceFailedEventArgs> = L"Microsoft.UI.Xaml.XamlResourceReferenceFailedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::XamlRoot> = L"Microsoft.UI.Xaml.XamlRoot";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::XamlRootChangedEventArgs> = L"Microsoft.UI.Xaml.XamlRootChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ApplicationHighContrastAdjustment> = L"Microsoft.UI.Xaml.ApplicationHighContrastAdjustment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ApplicationRequiresPointerMode> = L"Microsoft.UI.Xaml.ApplicationRequiresPointerMode";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ApplicationTheme> = L"Microsoft.UI.Xaml.ApplicationTheme";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::AutomationTextAttributesEnum> = L"Microsoft.UI.Xaml.AutomationTextAttributesEnum";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DispatcherShutdownMode> = L"Microsoft.UI.Xaml.DispatcherShutdownMode";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DurationType> = L"Microsoft.UI.Xaml.DurationType";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ElementHighContrastAdjustment> = L"Microsoft.UI.Xaml.ElementHighContrastAdjustment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ElementSoundKind> = L"Microsoft.UI.Xaml.ElementSoundKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ElementSoundMode> = L"Microsoft.UI.Xaml.ElementSoundMode";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ElementSoundPlayerState> = L"Microsoft.UI.Xaml.ElementSoundPlayerState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ElementSpatialAudioMode> = L"Microsoft.UI.Xaml.ElementSpatialAudioMode";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ElementTheme> = L"Microsoft.UI.Xaml.ElementTheme";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FlowDirection> = L"Microsoft.UI.Xaml.FlowDirection";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FocusState> = L"Microsoft.UI.Xaml.FocusState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FocusVisualKind> = L"Microsoft.UI.Xaml.FocusVisualKind";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FontCapitals> = L"Microsoft.UI.Xaml.FontCapitals";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FontEastAsianLanguage> = L"Microsoft.UI.Xaml.FontEastAsianLanguage";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FontEastAsianWidths> = L"Microsoft.UI.Xaml.FontEastAsianWidths";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FontFraction> = L"Microsoft.UI.Xaml.FontFraction";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FontNumeralAlignment> = L"Microsoft.UI.Xaml.FontNumeralAlignment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FontNumeralStyle> = L"Microsoft.UI.Xaml.FontNumeralStyle";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::FontVariants> = L"Microsoft.UI.Xaml.FontVariants";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::GridUnitType> = L"Microsoft.UI.Xaml.GridUnitType";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::HorizontalAlignment> = L"Microsoft.UI.Xaml.HorizontalAlignment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::LayoutCycleDebugBreakLevel> = L"Microsoft.UI.Xaml.LayoutCycleDebugBreakLevel";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::LayoutCycleTracingLevel> = L"Microsoft.UI.Xaml.LayoutCycleTracingLevel";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::LineStackingStrategy> = L"Microsoft.UI.Xaml.LineStackingStrategy";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::OpticalMarginAlignment> = L"Microsoft.UI.Xaml.OpticalMarginAlignment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TextAlignment> = L"Microsoft.UI.Xaml.TextAlignment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TextLineBounds> = L"Microsoft.UI.Xaml.TextLineBounds";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TextReadingOrder> = L"Microsoft.UI.Xaml.TextReadingOrder";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TextTrimming> = L"Microsoft.UI.Xaml.TextTrimming";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::TextWrapping> = L"Microsoft.UI.Xaml.TextWrapping";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Vector3TransitionComponents> = L"Microsoft.UI.Xaml.Vector3TransitionComponents";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::VerticalAlignment> = L"Microsoft.UI.Xaml.VerticalAlignment";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Visibility> = L"Microsoft.UI.Xaml.Visibility";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::WindowActivationState> = L"Microsoft.UI.Xaml.WindowActivationState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::CornerRadius> = L"Microsoft.UI.Xaml.CornerRadius";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Duration> = L"Microsoft.UI.Xaml.Duration";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::GridLength> = L"Microsoft.UI.Xaml.GridLength";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::Thickness> = L"Microsoft.UI.Xaml.Thickness";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IAdaptiveTrigger> = L"Microsoft.UI.Xaml.IAdaptiveTrigger";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerFactory> = L"Microsoft.UI.Xaml.IAdaptiveTriggerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerStatics> = L"Microsoft.UI.Xaml.IAdaptiveTriggerStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IApplication> = L"Microsoft.UI.Xaml.IApplication";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IApplication2> = L"Microsoft.UI.Xaml.IApplication2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IApplication3> = L"Microsoft.UI.Xaml.IApplication3";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IApplicationFactory> = L"Microsoft.UI.Xaml.IApplicationFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IApplicationInitializationCallbackParams> = L"Microsoft.UI.Xaml.IApplicationInitializationCallbackParams";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IApplicationOverrides> = L"Microsoft.UI.Xaml.IApplicationOverrides";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IApplicationStatics> = L"Microsoft.UI.Xaml.IApplicationStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IBindingFailedEventArgs> = L"Microsoft.UI.Xaml.IBindingFailedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IBringIntoViewOptions> = L"Microsoft.UI.Xaml.IBringIntoViewOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IBringIntoViewRequestedEventArgs> = L"Microsoft.UI.Xaml.IBringIntoViewRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IBrushTransition> = L"Microsoft.UI.Xaml.IBrushTransition";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IBrushTransitionFactory> = L"Microsoft.UI.Xaml.IBrushTransitionFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IColorPaletteResources> = L"Microsoft.UI.Xaml.IColorPaletteResources";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IColorPaletteResourcesFactory> = L"Microsoft.UI.Xaml.IColorPaletteResourcesFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ICornerRadiusHelper> = L"Microsoft.UI.Xaml.ICornerRadiusHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ICornerRadiusHelperStatics> = L"Microsoft.UI.Xaml.ICornerRadiusHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDataContextChangedEventArgs> = L"Microsoft.UI.Xaml.IDataContextChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDataTemplate> = L"Microsoft.UI.Xaml.IDataTemplate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDataTemplateExtension> = L"Microsoft.UI.Xaml.IDataTemplateExtension";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDataTemplateFactory> = L"Microsoft.UI.Xaml.IDataTemplateFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDataTemplateKey> = L"Microsoft.UI.Xaml.IDataTemplateKey";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDataTemplateKeyFactory> = L"Microsoft.UI.Xaml.IDataTemplateKeyFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDataTemplateStatics> = L"Microsoft.UI.Xaml.IDataTemplateStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDebugSettings> = L"Microsoft.UI.Xaml.IDebugSettings";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDebugSettings2> = L"Microsoft.UI.Xaml.IDebugSettings2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDebugSettings3> = L"Microsoft.UI.Xaml.IDebugSettings3";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDependencyObject> = L"Microsoft.UI.Xaml.IDependencyObject";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDependencyObjectCollectionFactory> = L"Microsoft.UI.Xaml.IDependencyObjectCollectionFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDependencyObjectFactory> = L"Microsoft.UI.Xaml.IDependencyObjectFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDependencyProperty> = L"Microsoft.UI.Xaml.IDependencyProperty";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDependencyPropertyChangedEventArgs> = L"Microsoft.UI.Xaml.IDependencyPropertyChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDependencyPropertyStatics> = L"Microsoft.UI.Xaml.IDependencyPropertyStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDispatcherTimer> = L"Microsoft.UI.Xaml.IDispatcherTimer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDispatcherTimerFactory> = L"Microsoft.UI.Xaml.IDispatcherTimerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDragEventArgs> = L"Microsoft.UI.Xaml.IDragEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDragOperationDeferral> = L"Microsoft.UI.Xaml.IDragOperationDeferral";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDragStartingEventArgs> = L"Microsoft.UI.Xaml.IDragStartingEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDragUI> = L"Microsoft.UI.Xaml.IDragUI";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDragUIOverride> = L"Microsoft.UI.Xaml.IDragUIOverride";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDropCompletedEventArgs> = L"Microsoft.UI.Xaml.IDropCompletedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDurationHelper> = L"Microsoft.UI.Xaml.IDurationHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IDurationHelperStatics> = L"Microsoft.UI.Xaml.IDurationHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IEffectiveViewportChangedEventArgs> = L"Microsoft.UI.Xaml.IEffectiveViewportChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IElementFactory> = L"Microsoft.UI.Xaml.IElementFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgs> = L"Microsoft.UI.Xaml.IElementFactoryGetArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgsFactory> = L"Microsoft.UI.Xaml.IElementFactoryGetArgsFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgs> = L"Microsoft.UI.Xaml.IElementFactoryRecycleArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgsFactory> = L"Microsoft.UI.Xaml.IElementFactoryRecycleArgsFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IElementSoundPlayer> = L"Microsoft.UI.Xaml.IElementSoundPlayer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IElementSoundPlayerStatics> = L"Microsoft.UI.Xaml.IElementSoundPlayerStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IEventTrigger> = L"Microsoft.UI.Xaml.IEventTrigger";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgs> = L"Microsoft.UI.Xaml.IExceptionRoutedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgsFactory> = L"Microsoft.UI.Xaml.IExceptionRoutedEventArgsFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IFrameworkElement> = L"Microsoft.UI.Xaml.IFrameworkElement";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IFrameworkElementFactory> = L"Microsoft.UI.Xaml.IFrameworkElementFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IFrameworkElementOverrides> = L"Microsoft.UI.Xaml.IFrameworkElementOverrides";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IFrameworkElementProtected> = L"Microsoft.UI.Xaml.IFrameworkElementProtected";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IFrameworkElementStatics> = L"Microsoft.UI.Xaml.IFrameworkElementStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IFrameworkTemplate> = L"Microsoft.UI.Xaml.IFrameworkTemplate";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IFrameworkTemplateFactory> = L"Microsoft.UI.Xaml.IFrameworkTemplateFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IFrameworkView> = L"Microsoft.UI.Xaml.IFrameworkView";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IFrameworkViewSource> = L"Microsoft.UI.Xaml.IFrameworkViewSource";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IGridLengthHelper> = L"Microsoft.UI.Xaml.IGridLengthHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IGridLengthHelperStatics> = L"Microsoft.UI.Xaml.IGridLengthHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ILaunchActivatedEventArgs> = L"Microsoft.UI.Xaml.ILaunchActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IMediaFailedRoutedEventArgs> = L"Microsoft.UI.Xaml.IMediaFailedRoutedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IPointHelper> = L"Microsoft.UI.Xaml.IPointHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IPointHelperStatics> = L"Microsoft.UI.Xaml.IPointHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IPropertyMetadata> = L"Microsoft.UI.Xaml.IPropertyMetadata";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IPropertyMetadataFactory> = L"Microsoft.UI.Xaml.IPropertyMetadataFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IPropertyMetadataStatics> = L"Microsoft.UI.Xaml.IPropertyMetadataStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IPropertyPath> = L"Microsoft.UI.Xaml.IPropertyPath";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IPropertyPathFactory> = L"Microsoft.UI.Xaml.IPropertyPathFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IRectHelper> = L"Microsoft.UI.Xaml.IRectHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IRectHelperStatics> = L"Microsoft.UI.Xaml.IRectHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IResourceDictionary> = L"Microsoft.UI.Xaml.IResourceDictionary";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IResourceDictionaryFactory> = L"Microsoft.UI.Xaml.IResourceDictionaryFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IResourceManagerRequestedEventArgs> = L"Microsoft.UI.Xaml.IResourceManagerRequestedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IRoutedEvent> = L"Microsoft.UI.Xaml.IRoutedEvent";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IRoutedEventArgs> = L"Microsoft.UI.Xaml.IRoutedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IRoutedEventArgsFactory> = L"Microsoft.UI.Xaml.IRoutedEventArgsFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IScalarTransition> = L"Microsoft.UI.Xaml.IScalarTransition";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IScalarTransitionFactory> = L"Microsoft.UI.Xaml.IScalarTransitionFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ISetter> = L"Microsoft.UI.Xaml.ISetter";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ISetterBase> = L"Microsoft.UI.Xaml.ISetterBase";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ISetterBaseCollection> = L"Microsoft.UI.Xaml.ISetterBaseCollection";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ISetterBaseFactory> = L"Microsoft.UI.Xaml.ISetterBaseFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ISetterFactory> = L"Microsoft.UI.Xaml.ISetterFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ISizeChangedEventArgs> = L"Microsoft.UI.Xaml.ISizeChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ISizeHelper> = L"Microsoft.UI.Xaml.ISizeHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ISizeHelperStatics> = L"Microsoft.UI.Xaml.ISizeHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IStateTrigger> = L"Microsoft.UI.Xaml.IStateTrigger";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IStateTriggerBase> = L"Microsoft.UI.Xaml.IStateTriggerBase";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IStateTriggerBaseFactory> = L"Microsoft.UI.Xaml.IStateTriggerBaseFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IStateTriggerBaseProtected> = L"Microsoft.UI.Xaml.IStateTriggerBaseProtected";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IStateTriggerStatics> = L"Microsoft.UI.Xaml.IStateTriggerStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IStyle> = L"Microsoft.UI.Xaml.IStyle";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IStyleFactory> = L"Microsoft.UI.Xaml.IStyleFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ITargetPropertyPath> = L"Microsoft.UI.Xaml.ITargetPropertyPath";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ITargetPropertyPathFactory> = L"Microsoft.UI.Xaml.ITargetPropertyPathFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IThicknessHelper> = L"Microsoft.UI.Xaml.IThicknessHelper";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IThicknessHelperStatics> = L"Microsoft.UI.Xaml.IThicknessHelperStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ITriggerAction> = L"Microsoft.UI.Xaml.ITriggerAction";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ITriggerActionFactory> = L"Microsoft.UI.Xaml.ITriggerActionFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ITriggerBase> = L"Microsoft.UI.Xaml.ITriggerBase";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ITriggerBaseFactory> = L"Microsoft.UI.Xaml.ITriggerBaseFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IUIElement> = L"Microsoft.UI.Xaml.IUIElement";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IUIElementFactory> = L"Microsoft.UI.Xaml.IUIElementFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IUIElementOverrides> = L"Microsoft.UI.Xaml.IUIElementOverrides";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IUIElementProtected> = L"Microsoft.UI.Xaml.IUIElementProtected";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IUIElementStatics> = L"Microsoft.UI.Xaml.IUIElementStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IUIElementWeakCollectionFactory> = L"Microsoft.UI.Xaml.IUIElementWeakCollectionFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IUnhandledExceptionEventArgs> = L"Microsoft.UI.Xaml.IUnhandledExceptionEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVector3Transition> = L"Microsoft.UI.Xaml.IVector3Transition";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVector3TransitionFactory> = L"Microsoft.UI.Xaml.IVector3TransitionFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualState> = L"Microsoft.UI.Xaml.IVisualState";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualStateChangedEventArgs> = L"Microsoft.UI.Xaml.IVisualStateChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualStateGroup> = L"Microsoft.UI.Xaml.IVisualStateGroup";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualStateManager> = L"Microsoft.UI.Xaml.IVisualStateManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualStateManagerFactory> = L"Microsoft.UI.Xaml.IVisualStateManagerFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualStateManagerOverrides> = L"Microsoft.UI.Xaml.IVisualStateManagerOverrides";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualStateManagerProtected> = L"Microsoft.UI.Xaml.IVisualStateManagerProtected";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualStateManagerStatics> = L"Microsoft.UI.Xaml.IVisualStateManagerStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualTransition> = L"Microsoft.UI.Xaml.IVisualTransition";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IVisualTransitionFactory> = L"Microsoft.UI.Xaml.IVisualTransitionFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IWindow> = L"Microsoft.UI.Xaml.IWindow";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IWindow2> = L"Microsoft.UI.Xaml.IWindow2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IWindowActivatedEventArgs> = L"Microsoft.UI.Xaml.IWindowActivatedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IWindowEventArgs> = L"Microsoft.UI.Xaml.IWindowEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IWindowFactory> = L"Microsoft.UI.Xaml.IWindowFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IWindowSizeChangedEventArgs> = L"Microsoft.UI.Xaml.IWindowSizeChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IWindowStatics> = L"Microsoft.UI.Xaml.IWindowStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IWindowVisibilityChangedEventArgs> = L"Microsoft.UI.Xaml.IWindowVisibilityChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IXamlIsland> = L"Microsoft.UI.Xaml.IXamlIsland";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IXamlIslandFactory> = L"Microsoft.UI.Xaml.IXamlIslandFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IXamlResourceReferenceFailedEventArgs> = L"Microsoft.UI.Xaml.IXamlResourceReferenceFailedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IXamlRoot> = L"Microsoft.UI.Xaml.IXamlRoot";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IXamlRoot2> = L"Microsoft.UI.Xaml.IXamlRoot2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IXamlRoot3> = L"Microsoft.UI.Xaml.IXamlRoot3";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IXamlRoot4> = L"Microsoft.UI.Xaml.IXamlRoot4";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IXamlRootChangedEventArgs> = L"Microsoft.UI.Xaml.IXamlRootChangedEventArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::IXamlServiceProvider> = L"Microsoft.UI.Xaml.IXamlServiceProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ApplicationInitializationCallback> = L"Microsoft.UI.Xaml.ApplicationInitializationCallback";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::BindingFailedEventHandler> = L"Microsoft.UI.Xaml.BindingFailedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::CreateDefaultValueCallback> = L"Microsoft.UI.Xaml.CreateDefaultValueCallback";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedCallback> = L"Microsoft.UI.Xaml.DependencyPropertyChangedCallback";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedEventHandler> = L"Microsoft.UI.Xaml.DependencyPropertyChangedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::DragEventHandler> = L"Microsoft.UI.Xaml.DragEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::EnteredBackgroundEventHandler> = L"Microsoft.UI.Xaml.EnteredBackgroundEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::ExceptionRoutedEventHandler> = L"Microsoft.UI.Xaml.ExceptionRoutedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::LeavingBackgroundEventHandler> = L"Microsoft.UI.Xaml.LeavingBackgroundEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::PropertyChangedCallback> = L"Microsoft.UI.Xaml.PropertyChangedCallback";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::RoutedEventHandler> = L"Microsoft.UI.Xaml.RoutedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::SizeChangedEventHandler> = L"Microsoft.UI.Xaml.SizeChangedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::SuspendingEventHandler> = L"Microsoft.UI.Xaml.SuspendingEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::UnhandledExceptionEventHandler> = L"Microsoft.UI.Xaml.UnhandledExceptionEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::VisualStateChangedEventHandler> = L"Microsoft.UI.Xaml.VisualStateChangedEventHandler";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::WinUIContract> = L"Microsoft.UI.Xaml.WinUIContract";
    template <> inline constexpr auto& name_v<winrt::Microsoft::UI::Xaml::XamlContract> = L"Microsoft.UI.Xaml.XamlContract";
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IAdaptiveTrigger>{ 0xB2B18AE8,0x48D9,0x5A1D,{ 0xA5,0x55,0x66,0x85,0xDD,0xD4,0xDA,0x80 } }; // B2B18AE8-48D9-5A1D-A555-6685DDD4DA80
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerFactory>{ 0x9C9560BB,0x4099,0x5175,{ 0x92,0x50,0x45,0xA1,0x5E,0x75,0x3D,0xA8 } }; // 9C9560BB-**************-45A15E753DA8
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerStatics>{ 0xE7A3547F,0xC077,0x5F20,{ 0xAA,0xB1,0xD1,0x6C,0x30,0xD9,0xD3,0x7F } }; // E7A3547F-C077-5F20-AAB1-D16C30D9D37F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IApplication>{ 0x06A8F4E7,0x1146,0x55AF,{ 0x82,0x0D,0xEB,0xD5,0x56,0x43,0xB0,0x21 } }; // 06A8F4E7-1146-55AF-820D-EBD55643B021
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IApplication2>{ 0x469E6D36,0x2E11,0x5B06,{ 0x9E,0x0A,0xC5,0xEE,0xF0,0xCF,0x8F,0x12 } }; // 469E6D36-2E11-5B06-9E0A-C5EEF0CF8F12
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IApplication3>{ 0xBE941595,0x61FE,0x5B36,{ 0xA3,0xD3,0x96,0x2A,0x64,0x7D,0x7C,0x6F } }; // BE941595-61FE-5B36-A3D3-962A647D7C6F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IApplicationFactory>{ 0x9FD96657,0x5294,0x5A65,{ 0xA1,0xDB,0x4F,0xEA,0x14,0x35,0x97,0xDA } }; // 9FD96657-5294-5A65-A1DB-4FEA143597DA
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IApplicationInitializationCallbackParams>{ 0x1B1906EA,0x5B7B,0x5876,{ 0x81,0xAB,0x7C,0x22,0x81,0xAC,0x3D,0x20 } }; // 1B1906EA-5B7B-5876-81AB-7C2281AC3D20
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IApplicationOverrides>{ 0xA33E81EF,0xC665,0x503B,{ 0x88,0x27,0xD2,0x7E,0xF1,0x72,0x0A,0x06 } }; // A33E81EF-C665-503B-8827-D27EF1720A06
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IApplicationStatics>{ 0x4E0D09F5,0x4358,0x512C,{ 0xA9,0x87,0x50,0x3B,0x52,0x84,0x8E,0x95 } }; // 4E0D09F5-4358-512C-A987-503B52848E95
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IBindingFailedEventArgs>{ 0xA7BF50F3,0xDBC0,0x5B44,{ 0xBE,0x74,0x56,0xE8,0xF8,0x0F,0xD7,0x16 } }; // A7BF50F3-DBC0-5B44-BE74-56E8F80FD716
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IBringIntoViewOptions>{ 0xEEB4A447,0xEB9E,0x5003,{ 0xA4,0x79,0xB9,0xE3,0xA8,0x86,0xB7,0x08 } }; // EEB4A447-EB9E-5003-A479-B9E3A886B708
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IBringIntoViewRequestedEventArgs>{ 0x807DE8F9,0xB1DC,0x5A63,{ 0x81,0x01,0x5E,0xE9,0x66,0x84,0x1A,0x27 } }; // 807DE8F9-B1DC-5A63-8101-5EE966841A27
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IBrushTransition>{ 0xA996A7BA,0x4567,0x5963,{ 0xA1,0x12,0x76,0xE3,0xC0,0x00,0x02,0x04 } }; // A996A7BA-4567-5963-A112-76E3C0000204
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IBrushTransitionFactory>{ 0x13735998,0xC3B6,0x5C24,{ 0xB4,0x0A,0x7B,0x16,0x6A,0x6F,0xFC,0x2C } }; // 13735998-C3B6-5C24-B40A-7B166A6FFC2C
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IColorPaletteResources>{ 0x1903A03C,0x1750,0x54FE,{ 0xA4,0x34,0x14,0xB2,0x27,0xCB,0xE7,0x01 } }; // 1903A03C-1750-54FE-A434-14B227CBE701
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IColorPaletteResourcesFactory>{ 0x32FDE185,0x8544,0x59C0,{ 0x9E,0x0A,0xE6,0xE0,0xBA,0xD9,0xED,0xCF } }; // 32FDE185-8544-59C0-9E0A-E6E0BAD9EDCF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ICornerRadiusHelper>{ 0xDFCC382D,0xCFA8,0x5614,{ 0xA3,0x5A,0x40,0x91,0xD1,0xA8,0x1C,0x9E } }; // DFCC382D-CFA8-5614-A35A-4091D1A81C9E
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ICornerRadiusHelperStatics>{ 0x77352882,0x894B,0x5DED,{ 0xB5,0x4C,0xA8,0x61,0x05,0xE4,0xE0,0x68 } }; // 77352882-894B-5DED-B54C-A86105E4E068
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDataContextChangedEventArgs>{ 0xA1BE80F4,0xCF83,0x5022,{ 0xB1,0x13,0x92,0x33,0xF1,0xD4,0xFA,0xFA } }; // A1BE80F4-CF83-5022-B113-9233F1D4FAFA
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDataTemplate>{ 0x08FA70FA,0xEE75,0x5E92,{ 0xA1,0x01,0xF5,0x2D,0x0E,0x1E,0x9F,0xAB } }; // 08FA70FA-EE75-5E92-A101-F52D0E1E9FAB
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDataTemplateExtension>{ 0x351E63C4,0x8FA3,0x5CC3,{ 0xB0,0x73,0x7F,0x84,0xBA,0xA6,0x48,0x5D } }; // 351E63C4-8FA3-5CC3-B073-7F84BAA6485D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDataTemplateFactory>{ 0xD8E8249D,0x305B,0x5CA5,{ 0xAC,0xF8,0x3E,0x1B,0xEF,0xFD,0x02,0x19 } }; // D8E8249D-305B-5CA5-ACF8-3E1BEFFD0219
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDataTemplateKey>{ 0x6E704A95,0x4B2F,0x5BA8,{ 0xAD,0xA5,0x12,0x61,0xC8,0x32,0xBA,0xED } }; // 6E704A95-4B2F-5BA8-ADA5-1261C832BAED
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDataTemplateKeyFactory>{ 0x13B2F604,0xEEBC,0x5DAA,{ 0x8A,0x5B,0x46,0x0C,0x4F,0xAB,0xDE,0xB7 } }; // 13B2F604-EEBC-5DAA-8A5B-460C4FABDEB7
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDataTemplateStatics>{ 0xCF6ADA69,0x4BF1,0x5F2D,{ 0x8B,0xDB,0x09,0xEA,0x1A,0x26,0xF9,0x75 } }; // CF6ADA69-4BF1-5F2D-8BDB-09EA1A26F975
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDebugSettings>{ 0x4004943B,0x2509,0x5476,{ 0xBB,0xA2,0x3F,0xE0,0x5E,0xCF,0x61,0x5D } }; // 4004943B-2509-5476-BBA2-3FE05ECF615D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDebugSettings2>{ 0x6DFB6F51,0xD2F8,0x59C4,{ 0x8B,0xCA,0x44,0x10,0x92,0x95,0x77,0xD0 } }; // 6DFB6F51-D2F8-59C4-8BCA-4410929577D0
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDebugSettings3>{ 0x36135BD5,0x3917,0x5C8D,{ 0xA3,0xC6,0x2F,0xC8,0x9A,0x50,0x3F,0x26 } }; // 36135BD5-3917-5C8D-A3C6-2FC89A503F26
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDependencyObject>{ 0xE7BEAEE7,0x160E,0x50F7,{ 0x87,0x89,0xD6,0x34,0x63,0xF9,0x79,0xFA } }; // E7BEAEE7-160E-50F7-8789-D63463F979FA
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDependencyObjectCollectionFactory>{ 0x2A74EE43,0x90FD,0x5D61,{ 0x93,0x83,0x58,0x4E,0xA8,0x42,0x2B,0x39 } }; // 2A74EE43-90FD-5D61-9383-584EA8422B39
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDependencyObjectFactory>{ 0x936B614C,0x475F,0x5D7D,{ 0xB3,0xF7,0xBF,0x1F,0xBE,0xA2,0x81,0x26 } }; // 936B614C-475F-5D7D-B3F7-BF1FBEA28126
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDependencyProperty>{ 0x960EAB49,0x9672,0x58A0,{ 0x99,0x5B,0x3A,0x42,0xE5,0xEA,0x62,0x78 } }; // 960EAB49-9672-58A0-995B-3A42E5EA6278
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDependencyPropertyChangedEventArgs>{ 0x84EAD020,0x7849,0x5E98,{ 0x80,0x30,0x48,0x8A,0x80,0xD1,0x64,0xEC } }; // 84EAD020-7849-5E98-8030-488A80D164EC
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDependencyPropertyStatics>{ 0x61DDC651,0x0383,0x5D6F,{ 0x98,0xCE,0x5C,0x04,0x6A,0xAA,0xAA,0x8F } }; // 61DDC651-0383-5D6F-98CE-5C046AAAAA8F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDispatcherTimer>{ 0x58A4ABF1,0xA4A3,0x53DD,{ 0xAE,0x21,0x08,0xF4,0x32,0x31,0xE8,0x17 } }; // 58A4ABF1-A4A3-53DD-AE21-08F43231E817
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDispatcherTimerFactory>{ 0x1BCB3166,0x22E4,0x50BF,{ 0xA5,0xA2,0xB7,0x8C,0xA4,0x37,0x7B,0xD0 } }; // 1BCB3166-22E4-50BF-A5A2-B78CA4377BD0
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDragEventArgs>{ 0x47AC5757,0xE4BC,0x52BA,{ 0x8A,0xB9,0x1B,0xF8,0x1A,0xAD,0x79,0x00 } }; // 47AC5757-E4BC-52BA-8AB9-1BF81AAD7900
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDragOperationDeferral>{ 0x462C1880,0xFC6A,0x5035,{ 0x8A,0xBF,0x56,0x4B,0xAC,0xB7,0x81,0x58 } }; // 462C1880-FC6A-5035-8ABF-564BACB78158
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDragStartingEventArgs>{ 0xAD17BACE,0x9613,0x5666,{ 0xA3,0x1B,0x79,0xA7,0x3F,0xBA,0x77,0xCF } }; // AD17BACE-9613-5666-A31B-79A73FBA77CF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDragUI>{ 0x35F170E0,0x93BF,0x58DA,{ 0x87,0x7A,0x8E,0xC7,0x7D,0x8D,0x9F,0x00 } }; // 35F170E0-93BF-58DA-877A-8EC77D8D9F00
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDragUIOverride>{ 0x3260B18B,0x70DF,0x5DF2,{ 0xB9,0x8A,0x56,0xBE,0xB0,0x60,0x1F,0x79 } }; // 3260B18B-70DF-5DF2-B98A-56BEB0601F79
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDropCompletedEventArgs>{ 0xE700082D,0xC640,0x5D44,{ 0xB2,0x3A,0xF2,0x13,0xDF,0xBE,0xB2,0x45 } }; // E700082D-C640-5D44-B23A-F213DFBEB245
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDurationHelper>{ 0xCC1089AB,0x8041,0x5C3E,{ 0xB7,0x53,0x83,0x97,0xE7,0x35,0x8C,0xC6 } }; // CC1089AB-8041-5C3E-B753-8397E7358CC6
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IDurationHelperStatics>{ 0x491FEB2C,0x3475,0x5F89,{ 0xB1,0x5C,0x49,0xC2,0x36,0xEB,0x51,0x4C } }; // 491FEB2C-3475-5F89-B15C-49C236EB514C
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IEffectiveViewportChangedEventArgs>{ 0x636E8159,0x2D82,0x538A,{ 0x84,0x83,0xCD,0x57,0x6E,0x41,0xD0,0xDF } }; // 636E8159-2D82-538A-8483-CD576E41D0DF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IElementFactory>{ 0x75FABA47,0x2CF2,0x54AE,{ 0x91,0xE6,0x05,0x81,0x55,0x6F,0xDD,0xAA } }; // 75FABA47-2CF2-54AE-91E6-0581556FDDAA
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgs>{ 0xB7017D68,0xEC9E,0x5435,{ 0xB0,0x78,0xBE,0x6F,0x90,0x6F,0x09,0x53 } }; // B7017D68-EC9E-5435-B078-BE6F906F0953
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgsFactory>{ 0xA88E401B,0x9FE5,0x5960,{ 0x87,0xA3,0x89,0xA3,0xCF,0xE2,0x53,0x1C } }; // A88E401B-9FE5-5960-87A3-89A3CFE2531C
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgs>{ 0x46E444F7,0x05D3,0x5C5E,{ 0x9B,0x7A,0x55,0x41,0xF6,0x3E,0x4E,0xF9 } }; // 46E444F7-05D3-5C5E-9B7A-5541F63E4EF9
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgsFactory>{ 0x30EE194A,0xFE4D,0x53E7,{ 0xA8,0x4A,0xCD,0x34,0xFA,0xB0,0xD4,0xEF } }; // 30EE194A-FE4D-53E7-A84A-CD34FAB0D4EF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IElementSoundPlayer>{ 0x0EA67E68,0x937C,0x5C00,{ 0xB6,0x09,0x53,0xB6,0x3D,0x9A,0x5D,0x42 } }; // 0EA67E68-937C-5C00-B609-53B63D9A5D42
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IElementSoundPlayerStatics>{ 0x5A5A20C3,0x1C9B,0x5D61,{ 0x9D,0x63,0x48,0x7C,0x8B,0xF1,0x6E,0xCB } }; // 5A5A20C3-1C9B-5D61-9D63-487C8BF16ECB
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IEventTrigger>{ 0x8C6F0541,0xC6AC,0x5F27,{ 0x9D,0x45,0xCF,0x8B,0xDB,0xDF,0xAB,0xE6 } }; // 8C6F0541-C6AC-5F27-9D45-CF8BDBDFABE6
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgs>{ 0xE8BCB6D2,0xD3F5,0x5393,{ 0xA8,0x4F,0xDF,0xCD,0x44,0xA2,0xDF,0x34 } }; // E8BCB6D2-D3F5-5393-A84F-DFCD44A2DF34
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgsFactory>{ 0xE1E71FB6,0x2AD0,0x5189,{ 0x8D,0x96,0x33,0xBA,0xE4,0x88,0xC5,0xFB } }; // E1E71FB6-2AD0-5189-8D96-33BAE488C5FB
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IFrameworkElement>{ 0xFE08F13D,0xDC6A,0x5495,{ 0xAD,0x44,0xC2,0xD8,0xD2,0x18,0x63,0xB0 } }; // FE08F13D-DC6A-5495-AD44-C2D8D21863B0
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IFrameworkElementFactory>{ 0xBD3F2272,0x3EFA,0x5F92,{ 0xB7,0x59,0x90,0xB1,0xCC,0x3E,0x78,0x4C } }; // BD3F2272-3EFA-5F92-B759-90B1CC3E784C
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IFrameworkElementOverrides>{ 0xFFC6FD98,0xF38C,0x5904,{ 0x9C,0xE4,0x97,0xA3,0x42,0x7C,0xF4,0xBA } }; // FFC6FD98-F38C-5904-9CE4-97A3427CF4BA
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IFrameworkElementProtected>{ 0xE59A3DB0,0x91E5,0x5903,{ 0x9C,0xAF,0xD1,0xBB,0x9F,0x45,0x8B,0xF2 } }; // E59A3DB0-91E5-5903-9CAF-D1BB9F458BF2
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IFrameworkElementStatics>{ 0x894E2704,0x14E7,0x569A,{ 0xB2,0x1E,0xAF,0xC7,0xDF,0x71,0x45,0xA1 } }; // 894E2704-14E7-569A-B21E-AFC7DF7145A1
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IFrameworkTemplate>{ 0x0084C7C2,0xDE48,0x5B0B,{ 0x8A,0x5A,0xE4,0xFB,0x76,0xB7,0xF7,0xD1 } }; // 0084C7C2-DE48-5B0B-8A5A-E4FB76B7F7D1
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IFrameworkTemplateFactory>{ 0x616DD6DB,0xB064,0x561D,{ 0xB1,0x62,0x46,0xCE,0xB4,0x5D,0xC5,0x62 } }; // 616DD6DB-B064-561D-B162-46CEB45DC562
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IFrameworkView>{ 0xE60094C3,0x45AF,0x5A8F,{ 0x95,0x11,0x17,0x81,0xD7,0xDF,0x17,0x99 } }; // E60094C3-45AF-5A8F-9511-1781D7DF1799
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IFrameworkViewSource>{ 0x52B5D975,0x6FA6,0x5B66,{ 0xA2,0x48,0xD1,0x74,0x43,0xB2,0xBC,0xA0 } }; // 52B5D975-6FA6-5B66-A248-D17443B2BCA0
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IGridLengthHelper>{ 0x592B4FD5,0x6564,0x54E0,{ 0x87,0xD6,0x1C,0x41,0x93,0x9E,0xD4,0x99 } }; // 592B4FD5-6564-54E0-87D6-1C41939ED499
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IGridLengthHelperStatics>{ 0xCEA8881B,0x4E64,0x535D,{ 0x9F,0xCD,0xB4,0x82,0x8D,0x39,0x79,0xB0 } }; // CEA8881B-4E64-535D-9FCD-B4828D3979B0
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ILaunchActivatedEventArgs>{ 0xD505CEA9,0x1BCB,0x5B29,{ 0xA8,0xBE,0x94,0x4E,0x00,0xF0,0x6F,0x78 } }; // D505CEA9-1BCB-5B29-A8BE-944E00F06F78
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IMediaFailedRoutedEventArgs>{ 0xA1DCE737,0x539B,0x5E54,{ 0x99,0xAF,0x75,0xEC,0xE4,0x28,0xBF,0x9B } }; // A1DCE737-539B-5E54-99AF-75ECE428BF9B
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IPointHelper>{ 0x06FCC7A4,0x6099,0x5F2E,{ 0x83,0xA5,0xF3,0xBE,0x0E,0x2C,0x90,0xAA } }; // 06FCC7A4-6099-5F2E-83A5-F3BE0E2C90AA
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IPointHelperStatics>{ 0xB0B2BD44,0x600B,0x51B3,{ 0xA4,0x2C,0x3F,0xD3,0x6C,0x1A,0xB0,0x42 } }; // B0B2BD44-600B-51B3-A42C-3FD36C1AB042
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IPropertyMetadata>{ 0xB3644425,0x9464,0x5434,{ 0xB0,0xAE,0xAF,0xF8,0xD3,0x15,0x9F,0xE1 } }; // B3644425-9464-5434-B0AE-AFF8D3159FE1
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IPropertyMetadataFactory>{ 0x9F420906,0x111A,0x5465,{ 0x91,0xEE,0xBE,0xD1,0x4B,0x3E,0x7F,0xEC } }; // 9F420906-111A-5465-91EE-BED14B3E7FEC
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IPropertyMetadataStatics>{ 0x37B8ADD4,0x7A4A,0x5CF7,{ 0xA1,0x74,0x23,0x51,0x82,0xCD,0x08,0x2E } }; // 37B8ADD4-7A4A-5CF7-A174-235182CD082E
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IPropertyPath>{ 0x8B0712F6,0x9E57,0x53B0,{ 0x80,0xB1,0x96,0x6A,0x79,0xF6,0x0B,0x96 } }; // 8B0712F6-9E57-53B0-80B1-966A79F60B96
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IPropertyPathFactory>{ 0x08A8CCAB,0x7FF8,0x5CEC,{ 0xBD,0x3C,0x72,0xC9,0x88,0x04,0xD9,0x89 } }; // 08A8CCAB-7FF8-5CEC-BD3C-72C98804D989
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IRectHelper>{ 0x5FECE92A,0xA3D2,0x5BC0,{ 0xAC,0xA1,0xE9,0xE1,0xFA,0x86,0xAE,0x9D } }; // 5FECE92A-A3D2-5BC0-ACA1-E9E1FA86AE9D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IRectHelperStatics>{ 0xA9CF37AD,0x5430,0x5086,{ 0xA3,0x9F,0x74,0xF0,0xD1,0xED,0x10,0x72 } }; // A9CF37AD-5430-5086-A39F-74F0D1ED1072
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IResourceDictionary>{ 0x1B690975,0xA710,0x5783,{ 0xA6,0xE1,0x15,0x83,0x6F,0x61,0x86,0xC2 } }; // 1B690975-A710-5783-A6E1-15836F6186C2
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IResourceDictionaryFactory>{ 0xEA22A48F,0xAB71,0x56F6,{ 0xA3,0x92,0xD8,0x23,0x10,0xC8,0xAA,0x7B } }; // EA22A48F-AB71-56F6-A392-D82310C8AA7B
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IResourceManagerRequestedEventArgs>{ 0xC35F4CF1,0xFCD6,0x5C6B,{ 0x9B,0xE2,0x4C,0xFA,0xEF,0xB6,0x8B,0x2A } }; // C35F4CF1-FCD6-5C6B-9BE2-4CFAEFB68B2A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IRoutedEvent>{ 0xB2B432BC,0xEFCA,0x575E,{ 0x9D,0x2A,0x70,0x3F,0x8B,0x9C,0x38,0x0F } }; // B2B432BC-EFCA-575E-9D2A-703F8B9C380F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IRoutedEventArgs>{ 0x0908C407,0x1C7D,0x5DE3,{ 0x9C,0x50,0xD9,0x71,0xC6,0x2E,0xC8,0xEC } }; // 0908C407-1C7D-5DE3-9C50-D971C62EC8EC
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IRoutedEventArgsFactory>{ 0x914B02C7,0x076B,0x5B89,{ 0x98,0xE7,0x6C,0x37,0x33,0x79,0xE9,0xAF } }; // 914B02C7-076B-5B89-98E7-6C373379E9AF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IScalarTransition>{ 0xC2DA2AC8,0x814C,0x5889,{ 0xB2,0xF4,0x4E,0xBE,0x4B,0x00,0x1E,0xE3 } }; // C2DA2AC8-814C-5889-B2F4-4EBE4B001EE3
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IScalarTransitionFactory>{ 0xA1650CF8,0xA15B,0x54FC,{ 0xB5,0x95,0xC5,0x24,0x91,0x31,0x8F,0x58 } }; // A1650CF8-A15B-54FC-B595-C52491318F58
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ISetter>{ 0xBBD6074D,0x686F,0x5AE1,{ 0xB8,0xDE,0x5F,0x16,0xAA,0x30,0xB8,0x0A } }; // BBD6074D-686F-5AE1-B8DE-5F16AA30B80A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ISetterBase>{ 0x5A7C1347,0xCDA3,0x55BE,{ 0xBF,0xEF,0x5C,0x75,0x82,0x21,0x39,0x80 } }; // 5A7C1347-CDA3-55BE-BFEF-5C7582213980
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ISetterBaseCollection>{ 0x63BF7C0F,0xB290,0x5C0C,{ 0x91,0x85,0x33,0x38,0xCD,0x35,0x0D,0x7F } }; // 63BF7C0F-B290-5C0C-9185-3338CD350D7F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ISetterBaseFactory>{ 0x780A1D2F,0xC4BE,0x5707,{ 0x8A,0x8A,0x45,0x50,0xDC,0x22,0x58,0x3E } }; // 780A1D2F-C4BE-5707-8A8A-4550DC22583E
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ISetterFactory>{ 0x13910A06,0xA327,0x5407,{ 0xAE,0x91,0xB9,0xD2,0xCC,0x3A,0x7A,0xB5 } }; // 13910A06-A327-5407-AE91-B9D2CC3A7AB5
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ISizeChangedEventArgs>{ 0xFE76324E,0x6DFB,0x58B1,{ 0x9D,0xCD,0x88,0x6C,0xA8,0xF9,0xA2,0xEA } }; // FE76324E-6DFB-58B1-9DCD-886CA8F9A2EA
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ISizeHelper>{ 0x5DF9EEE1,0xA2A8,0x5E55,{ 0x86,0x68,0xAF,0xED,0xC0,0xB3,0x6D,0xEB } }; // 5DF9EEE1-A2A8-5E55-8668-AFEDC0B36DEB
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ISizeHelperStatics>{ 0xCFF1B27F,0x84F1,0x5B14,{ 0x94,0x59,0x76,0x4A,0xF5,0x71,0x4F,0xE5 } }; // CFF1B27F-84F1-5B14-9459-764AF5714FE5
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IStateTrigger>{ 0x7B098126,0x1DAB,0x5B58,{ 0xAC,0xA7,0xF2,0xB7,0xDE,0x2E,0x10,0x33 } }; // 7B098126-1DAB-5B58-ACA7-F2B7DE2E1033
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IStateTriggerBase>{ 0xF07B0F7B,0x5B94,0x58AE,{ 0x87,0x17,0x22,0xAB,0x09,0x3B,0xC1,0x31 } }; // F07B0F7B-5B94-58AE-8717-22AB093BC131
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IStateTriggerBaseFactory>{ 0xE7724D65,0xFC7E,0x5C67,{ 0xBB,0x84,0xB4,0xC7,0xB0,0x20,0xAD,0xC3 } }; // E7724D65-FC7E-5C67-BB84-B4C7B020ADC3
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IStateTriggerBaseProtected>{ 0x2F695047,0x335B,0x5C00,{ 0xA0,0xD4,0x2A,0x8F,0xA5,0x45,0x44,0xC6 } }; // 2F695047-335B-5C00-A0D4-2A8FA54544C6
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IStateTriggerStatics>{ 0xBD60C019,0x833B,0x5432,{ 0xA4,0x1D,0x89,0xD7,0x24,0x10,0xEB,0x47 } }; // BD60C019-833B-5432-A41D-89D72410EB47
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IStyle>{ 0x65E1D164,0x572F,0x5B0E,{ 0xA8,0x0F,0x9C,0x02,0x44,0x1F,0xAC,0x49 } }; // 65E1D164-572F-5B0E-A80F-9C02441FAC49
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IStyleFactory>{ 0xC2D924A2,0x3862,0x517C,{ 0xB0,0x83,0x9A,0x91,0x20,0xD7,0x30,0x2D } }; // C2D924A2-3862-517C-B083-9A9120D7302D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ITargetPropertyPath>{ 0xB1442F0E,0xF66B,0x531C,{ 0x97,0x9B,0x19,0x3F,0xD3,0x44,0xE2,0xA8 } }; // B1442F0E-F66B-531C-979B-193FD344E2A8
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ITargetPropertyPathFactory>{ 0x894CB11D,0x5C16,0x555B,{ 0xB6,0x61,0xF4,0x1B,0x29,0xFD,0x9B,0x21 } }; // 894CB11D-5C16-555B-B661-F41B29FD9B21
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IThicknessHelper>{ 0x5E496347,0x3C49,0x55EE,{ 0xB4,0x42,0x53,0x07,0x89,0xB4,0x2B,0x6F } }; // 5E496347-3C49-55EE-B442-530789B42B6F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IThicknessHelperStatics>{ 0x0E3B81CE,0xD278,0x577F,{ 0x98,0xEA,0x1B,0x60,0x10,0xF8,0x6D,0x7F } }; // 0E3B81CE-D278-577F-98EA-1B6010F86D7F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ITriggerAction>{ 0x1FA35464,0xA690,0x586C,{ 0xAE,0xDF,0x6C,0x88,0xCA,0xC7,0xD1,0x4A } }; // 1FA35464-A690-586C-AEDF-6C88CAC7D14A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ITriggerActionFactory>{ 0x1E1FAF1A,0xF614,0x554A,{ 0x82,0x2A,0xD9,0x8F,0xE4,0x65,0x75,0xD1 } }; // 1E1FAF1A-F614-554A-822A-D98FE46575D1
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ITriggerBase>{ 0xD37DA89D,0x0D71,0x58CF,{ 0xA9,0x01,0x99,0xA7,0xD3,0xE5,0xE4,0x34 } }; // D37DA89D-0D71-58CF-A901-99A7D3E5E434
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ITriggerBaseFactory>{ 0x23088EAA,0x17EC,0x51B2,{ 0xB1,0x81,0x5B,0xED,0xFA,0x8B,0x8F,0xA4 } }; // 23088EAA-17EC-51B2-B181-5BEDFA8B8FA4
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IUIElement>{ 0xC3C01020,0x320C,0x5CF6,{ 0x9D,0x24,0xD3,0x96,0xBB,0xFA,0x4D,0x8B } }; // C3C01020-320C-5CF6-9D24-D396BBFA4D8B
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IUIElementFactory>{ 0x14D1D309,0xADD0,0x5CCB,{ 0xB9,0x46,0x77,0x48,0x8C,0xD7,0x0F,0x87 } }; // 14D1D309-ADD0-5CCB-B946-77488CD70F87
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IUIElementOverrides>{ 0x9034F41E,0xAB7B,0x59E7,{ 0x81,0x68,0x50,0xDE,0x6B,0x68,0x9D,0xDE } }; // 9034F41E-AB7B-59E7-8168-50DE6B689DDE
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IUIElementProtected>{ 0x8F69B9E9,0x1F00,0x5834,{ 0x9B,0xF1,0xA9,0x25,0x7B,0xED,0x39,0xF0 } }; // 8F69B9E9-1F00-5834-9BF1-A9257BED39F0
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IUIElementStatics>{ 0xD2921D87,0x3584,0x5E22,{ 0x8A,0x3A,0xC2,0xC7,0x8D,0xAB,0x4F,0x6E } }; // D2921D87-3584-5E22-8A3A-C2C78DAB4F6E
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IUIElementWeakCollectionFactory>{ 0xB4D69F09,0xD494,0x5BC8,{ 0xAE,0x68,0xB6,0x30,0x7D,0x84,0x50,0x49 } }; // B4D69F09-D494-5BC8-AE68-B6307D845049
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IUnhandledExceptionEventArgs>{ 0x59EAEBA9,0x8F9C,0x5BE7,{ 0x9B,0x3B,0x82,0x09,0x60,0xFA,0xA2,0x20 } }; // 59EAEBA9-8F9C-5BE7-9B3B-820960FAA220
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVector3Transition>{ 0x0C408BB9,0xF9A2,0x55D7,{ 0x8A,0xED,0x14,0x3D,0x36,0xD6,0x03,0xF2 } }; // 0C408BB9-F9A2-55D7-8AED-143D36D603F2
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVector3TransitionFactory>{ 0xA3A084FC,0xB965,0x534B,{ 0x90,0x0F,0x78,0xE2,0x88,0x12,0x92,0x32 } }; // A3A084FC-B965-534B-900F-78E288129232
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualState>{ 0x4BB32AE8,0x0E28,0x5521,{ 0xA7,0xF5,0x66,0xB6,0x61,0x37,0x29,0x94 } }; // 4BB32AE8-0E28-5521-A7F5-66B661372994
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualStateChangedEventArgs>{ 0x11DE9510,0xA195,0x577B,{ 0x88,0xC8,0x06,0x39,0x16,0x18,0x86,0x8C } }; // 11DE9510-A195-577B-88C8-06391618868C
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualStateGroup>{ 0x8DFD691B,0x710C,0x5D6D,{ 0xB7,0x1A,0x7A,0x7F,0x5E,0xD5,0x4A,0xC7 } }; // 8DFD691B-710C-5D6D-B71A-7A7F5ED54AC7
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualStateManager>{ 0x342C8D32,0xAD61,0x5925,{ 0x93,0xD1,0x0C,0x70,0x4D,0xF2,0xA7,0xD1 } }; // 342C8D32-AD61-5925-93D1-0C704DF2A7D1
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualStateManagerFactory>{ 0x713DAF82,0x92B3,0x58F2,{ 0x8F,0xC1,0xB0,0xD9,0xA2,0xCA,0xD0,0x3C } }; // 713DAF82-92B3-58F2-8FC1-B0D9A2CAD03C
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualStateManagerOverrides>{ 0xE3F8E9C9,0x9432,0x514C,{ 0x92,0x3E,0x14,0x2C,0xD8,0xC8,0x27,0x30 } }; // E3F8E9C9-9432-514C-923E-142CD8C82730
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualStateManagerProtected>{ 0x0F008013,0x787F,0x5599,{ 0xA5,0xAD,0x0A,0x10,0xB9,0x88,0xED,0x24 } }; // 0F008013-787F-5599-A5AD-0A10B988ED24
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualStateManagerStatics>{ 0xA4D5147D,0x88C3,0x57ED,{ 0xAD,0x83,0x24,0x5D,0xF5,0xF6,0xB5,0x0D } }; // A4D5147D-88C3-57ED-AD83-245DF5F6B50D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualTransition>{ 0xDD21AF54,0x2CE1,0x59DE,{ 0x9F,0xD1,0x2B,0x45,0xF6,0xBF,0x65,0x81 } }; // DD21AF54-2CE1-59DE-9FD1-2B45F6BF6581
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IVisualTransitionFactory>{ 0xF3E74C0D,0x0B5B,0x5920,{ 0xA3,0x09,0x08,0xCB,0x6B,0xF2,0xA7,0x39 } }; // F3E74C0D-0B5B-5920-A309-08CB6BF2A739
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IWindow>{ 0x61F0EC79,0x5D52,0x56B5,{ 0x86,0xFB,0x40,0xFA,0x4A,0xF2,0x88,0xB0 } }; // 61F0EC79-5D52-56B5-86FB-40FA4AF288B0
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IWindow2>{ 0x42FEBAA5,0x1C32,0x522A,{ 0xA5,0x91,0x57,0x61,0x8C,0x6F,0x66,0x5D } }; // 42FEBAA5-1C32-522A-A591-57618C6F665D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IWindowActivatedEventArgs>{ 0xC723A5EA,0x82C4,0x5DD6,{ 0x86,0x1B,0x70,0xEF,0x57,0x3B,0x88,0xD6 } }; // C723A5EA-82C4-5DD6-861B-70EF573B88D6
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IWindowEventArgs>{ 0x1140827C,0xFE0A,0x5268,{ 0xBC,0x2B,0xF4,0x49,0x2C,0x2C,0xCB,0x49 } }; // 1140827C-FE0A-5268-BC2B-F4492C2CCB49
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IWindowFactory>{ 0xF0441536,0xAFEF,0x5222,{ 0x91,0x8F,0x32,0x4A,0x9B,0x2D,0xEC,0x75 } }; // F0441536-AFEF-5222-918F-324A9B2DEC75
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IWindowSizeChangedEventArgs>{ 0x542F6F2C,0x4B64,0x5C72,{ 0xA7,0xA5,0x3A,0x7E,0x06,0x64,0xB8,0xFF } }; // 542F6F2C-4B64-5C72-A7A5-3A7E0664B8FF
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IWindowStatics>{ 0x8CC985E3,0xA41A,0x5DF4,{ 0xB5,0x31,0xD3,0xA1,0x78,0x8D,0x86,0xC5 } }; // 8CC985E3-A41A-5DF4-B531-D3A1788D86C5
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IWindowVisibilityChangedEventArgs>{ 0x7BB24A6D,0x070C,0x5CB6,{ 0x8E,0x9C,0x54,0x79,0x05,0xBE,0x82,0x65 } }; // 7BB24A6D-070C-5CB6-8E9C-547905BE8265
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IXamlIsland>{ 0x845A5C62,0xB0F3,0x5DB8,{ 0xB4,0xFF,0x41,0x42,0xBB,0xD8,0xA0,0x44 } }; // 845A5C62-B0F3-5DB8-B4FF-4142BBD8A044
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IXamlIslandFactory>{ 0x267F707C,0x5E18,0x57B4,{ 0x9F,0xF7,0xD1,0x1D,0xA6,0x6E,0x4A,0x11 } }; // 267F707C-5E18-57B4-9FF7-D11DA66E4A11
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IXamlResourceReferenceFailedEventArgs>{ 0x1B175EE6,0xD08B,0x50FF,{ 0x8F,0x89,0xA1,0xFF,0x27,0xED,0xEF,0x66 } }; // 1B175EE6-D08B-50FF-8F89-A1FF27EDEF66
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IXamlRoot>{ 0x60CB215A,0xAD15,0x520A,{ 0x8B,0x01,0x44,0x16,0x82,0x4F,0x04,0x41 } }; // 60CB215A-AD15-520A-8B01-4416824F0441
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IXamlRoot2>{ 0xBDEE0F42,0x71CB,0x50C5,{ 0x82,0x9B,0x46,0x14,0xD9,0x8C,0x57,0x94 } }; // BDEE0F42-71CB-50C5-829B-4614D98C5794
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IXamlRoot3>{ 0xB71DBF3B,0x2E0F,0x5DE0,{ 0xAC,0x68,0xF0,0xC1,0xF6,0x51,0x14,0xC8 } }; // B71DBF3B-2E0F-5DE0-AC68-F0C1F65114C8
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IXamlRoot4>{ 0x377BEC22,0x632B,0x52BE,{ 0xB2,0x6F,0x5E,0xDF,0x78,0x38,0xE5,0xCA } }; // 377BEC22-632B-52BE-B26F-5EDF7838E5CA
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IXamlRootChangedEventArgs>{ 0x61D2C719,0xF8A1,0x515A,{ 0x90,0x2C,0xCF,0xA4,0x98,0xBA,0x7A,0x7F } }; // 61D2C719-F8A1-515A-902C-CFA498BA7A7F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::IXamlServiceProvider>{ 0x68B3A2DF,0x8173,0x539F,{ 0xB5,0x24,0xC8,0xA2,0x34,0x8F,0x5A,0xFB } }; // 68B3A2DF-8173-539F-B524-C8A2348F5AFB
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ApplicationInitializationCallback>{ 0xD8EEF1C9,0x1234,0x56F1,{ 0x99,0x63,0x45,0xDD,0x9C,0x80,0xA6,0x61 } }; // D8EEF1C9-1234-56F1-9963-45DD9C80A661
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::BindingFailedEventHandler>{ 0xA3160AB0,0xA8A9,0x5F38,{ 0xAF,0x17,0x5C,0xD9,0x1A,0x2B,0x33,0xF5 } }; // A3160AB0-A8A9-5F38-AF17-5CD91A2B33F5
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::CreateDefaultValueCallback>{ 0x7F808C05,0x2AC4,0x5AD9,{ 0xAC,0x8A,0x26,0x89,0x03,0x33,0xD8,0x1E } }; // 7F808C05-2AC4-5AD9-AC8A-26890333D81E
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedCallback>{ 0xF055BB21,0x219B,0x5B0C,{ 0x80,0x5D,0xBC,0xAE,0xDA,0xE1,0x54,0x58 } }; // F055BB21-219B-5B0C-805D-BCAEDAE15458
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedEventHandler>{ 0x4BE8DC75,0x373D,0x5F4E,{ 0xA0,0xB4,0x54,0xB9,0xEE,0xAF,0xB4,0xA9 } }; // 4BE8DC75-373D-5F4E-A0B4-54B9EEAFB4A9
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::DragEventHandler>{ 0x277AFC83,0xCB67,0x56C8,{ 0xB6,0x01,0x1B,0x9C,0x0F,0x1C,0x3D,0x32 } }; // 277AFC83-CB67-56C8-B601-1B9C0F1C3D32
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::EnteredBackgroundEventHandler>{ 0xF9A5148D,0x8F72,0x553F,{ 0xB4,0x79,0x21,0xB6,0x86,0x10,0x89,0x9D } }; // F9A5148D-8F72-553F-B479-21B68610899D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::ExceptionRoutedEventHandler>{ 0x45FBB85D,0x54F9,0x5A2A,{ 0x8A,0x38,0x00,0xA3,0xB7,0x76,0x1F,0x96 } }; // 45FBB85D-54F9-5A2A-8A38-00A3B7761F96
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::LeavingBackgroundEventHandler>{ 0x3D723B94,0xFBCF,0x5C0D,{ 0xB6,0xEF,0x50,0x62,0xE6,0x8B,0xF9,0xF8 } }; // 3D723B94-FBCF-5C0D-B6EF-5062E68BF9F8
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::PropertyChangedCallback>{ 0x5FD9243A,0x2422,0x53C9,{ 0x8D,0x6F,0xF1,0xBA,0x1A,0x0B,0xBA,0x9A } }; // 5FD9243A-2422-53C9-8D6F-F1BA1A0BBA9A
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::RoutedEventHandler>{ 0xDAE23D85,0x69CA,0x5BDF,{ 0x80,0x5B,0x61,0x61,0xA3,0xA2,0x15,0xCC } }; // DAE23D85-69CA-5BDF-805B-6161A3A215CC
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::SizeChangedEventHandler>{ 0x8D7B1A58,0x14C6,0x51C9,{ 0x89,0x2C,0x9F,0xCC,0xE3,0x68,0xE7,0x7D } }; // 8D7B1A58-14C6-51C9-892C-9FCCE368E77D
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::SuspendingEventHandler>{ 0xE4BEEC79,0x95FD,0x5841,{ 0xAC,0xEB,0x01,0xA8,0xA1,0xFB,0x73,0xD0 } }; // E4BEEC79-95FD-5841-ACEB-01A8A1FB73D0
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::UnhandledExceptionEventHandler>{ 0x3427C1B6,0x5ECA,0x5631,{ 0x84,0xB8,0x5B,0xAE,0x73,0x2F,0xB6,0x7F } }; // 3427C1B6-5ECA-5631-84B8-5BAE732FB67F
    template <> inline constexpr guid guid_v<winrt::Microsoft::UI::Xaml::VisualStateChangedEventHandler>{ 0xCDBBD854,0x0539,0x5BFF,{ 0xB4,0x48,0x33,0x19,0x3D,0x2F,0x41,0xB8 } }; // CDBBD854-0539-5BFF-B448-33193D2F41B8
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::AdaptiveTrigger>{ using type = winrt::Microsoft::UI::Xaml::IAdaptiveTrigger; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Application>{ using type = winrt::Microsoft::UI::Xaml::IApplication; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ApplicationInitializationCallbackParams>{ using type = winrt::Microsoft::UI::Xaml::IApplicationInitializationCallbackParams; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::BindingFailedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IBindingFailedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::BringIntoViewOptions>{ using type = winrt::Microsoft::UI::Xaml::IBringIntoViewOptions; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::BringIntoViewRequestedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IBringIntoViewRequestedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::BrushTransition>{ using type = winrt::Microsoft::UI::Xaml::IBrushTransition; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ColorPaletteResources>{ using type = winrt::Microsoft::UI::Xaml::IColorPaletteResources; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::CornerRadiusHelper>{ using type = winrt::Microsoft::UI::Xaml::ICornerRadiusHelper; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DataContextChangedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IDataContextChangedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DataTemplate>{ using type = winrt::Microsoft::UI::Xaml::IDataTemplate; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DataTemplateKey>{ using type = winrt::Microsoft::UI::Xaml::IDataTemplateKey; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DebugSettings>{ using type = winrt::Microsoft::UI::Xaml::IDebugSettings; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DependencyObject>{ using type = winrt::Microsoft::UI::Xaml::IDependencyObject; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DependencyObjectCollection>{ using type = winrt::Windows::Foundation::Collections::IObservableVector<winrt::Microsoft::UI::Xaml::DependencyObject>; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DependencyProperty>{ using type = winrt::Microsoft::UI::Xaml::IDependencyProperty; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IDependencyPropertyChangedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DispatcherTimer>{ using type = winrt::Microsoft::UI::Xaml::IDispatcherTimer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DragEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IDragEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DragOperationDeferral>{ using type = winrt::Microsoft::UI::Xaml::IDragOperationDeferral; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DragStartingEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IDragStartingEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DragUI>{ using type = winrt::Microsoft::UI::Xaml::IDragUI; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DragUIOverride>{ using type = winrt::Microsoft::UI::Xaml::IDragUIOverride; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DropCompletedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IDropCompletedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::DurationHelper>{ using type = winrt::Microsoft::UI::Xaml::IDurationHelper; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::EffectiveViewportChangedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IEffectiveViewportChangedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ElementFactoryGetArgs>{ using type = winrt::Microsoft::UI::Xaml::IElementFactoryGetArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ElementFactoryRecycleArgs>{ using type = winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ElementSoundPlayer>{ using type = winrt::Microsoft::UI::Xaml::IElementSoundPlayer; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::EventTrigger>{ using type = winrt::Microsoft::UI::Xaml::IEventTrigger; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ExceptionRoutedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::FrameworkElement>{ using type = winrt::Microsoft::UI::Xaml::IFrameworkElement; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::FrameworkTemplate>{ using type = winrt::Microsoft::UI::Xaml::IFrameworkTemplate; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::FrameworkView>{ using type = winrt::Microsoft::UI::Xaml::IFrameworkView; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::FrameworkViewSource>{ using type = winrt::Microsoft::UI::Xaml::IFrameworkViewSource; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::GridLengthHelper>{ using type = winrt::Microsoft::UI::Xaml::IGridLengthHelper; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::LaunchActivatedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::ILaunchActivatedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::MediaFailedRoutedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IMediaFailedRoutedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::PointHelper>{ using type = winrt::Microsoft::UI::Xaml::IPointHelper; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::PropertyMetadata>{ using type = winrt::Microsoft::UI::Xaml::IPropertyMetadata; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::PropertyPath>{ using type = winrt::Microsoft::UI::Xaml::IPropertyPath; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::RectHelper>{ using type = winrt::Microsoft::UI::Xaml::IRectHelper; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ResourceDictionary>{ using type = winrt::Microsoft::UI::Xaml::IResourceDictionary; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ResourceManagerRequestedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IResourceManagerRequestedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::RoutedEvent>{ using type = winrt::Microsoft::UI::Xaml::IRoutedEvent; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::RoutedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IRoutedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ScalarTransition>{ using type = winrt::Microsoft::UI::Xaml::IScalarTransition; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Setter>{ using type = winrt::Microsoft::UI::Xaml::ISetter; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::SetterBase>{ using type = winrt::Microsoft::UI::Xaml::ISetterBase; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::SetterBaseCollection>{ using type = winrt::Microsoft::UI::Xaml::ISetterBaseCollection; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::SizeChangedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::ISizeChangedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::SizeHelper>{ using type = winrt::Microsoft::UI::Xaml::ISizeHelper; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::StateTrigger>{ using type = winrt::Microsoft::UI::Xaml::IStateTrigger; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::StateTriggerBase>{ using type = winrt::Microsoft::UI::Xaml::IStateTriggerBase; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Style>{ using type = winrt::Microsoft::UI::Xaml::IStyle; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::TargetPropertyPath>{ using type = winrt::Microsoft::UI::Xaml::ITargetPropertyPath; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::ThicknessHelper>{ using type = winrt::Microsoft::UI::Xaml::IThicknessHelper; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::TriggerAction>{ using type = winrt::Microsoft::UI::Xaml::ITriggerAction; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::TriggerActionCollection>{ using type = winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::UI::Xaml::TriggerAction>; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::TriggerBase>{ using type = winrt::Microsoft::UI::Xaml::ITriggerBase; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::TriggerCollection>{ using type = winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::UI::Xaml::TriggerBase>; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::UIElement>{ using type = winrt::Microsoft::UI::Xaml::IUIElement; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::UIElementWeakCollection>{ using type = winrt::Windows::Foundation::Collections::IVector<winrt::Microsoft::UI::Xaml::UIElement>; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::UnhandledExceptionEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IUnhandledExceptionEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Vector3Transition>{ using type = winrt::Microsoft::UI::Xaml::IVector3Transition; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::VisualState>{ using type = winrt::Microsoft::UI::Xaml::IVisualState; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::VisualStateChangedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IVisualStateChangedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::VisualStateGroup>{ using type = winrt::Microsoft::UI::Xaml::IVisualStateGroup; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::VisualStateManager>{ using type = winrt::Microsoft::UI::Xaml::IVisualStateManager; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::VisualTransition>{ using type = winrt::Microsoft::UI::Xaml::IVisualTransition; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::Window>{ using type = winrt::Microsoft::UI::Xaml::IWindow; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::WindowActivatedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IWindowActivatedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::WindowEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IWindowEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::WindowSizeChangedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IWindowSizeChangedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::WindowVisibilityChangedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IWindowVisibilityChangedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::XamlIsland>{ using type = winrt::Microsoft::UI::Xaml::IXamlIsland; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::XamlResourceReferenceFailedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IXamlResourceReferenceFailedEventArgs; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::XamlRoot>{ using type = winrt::Microsoft::UI::Xaml::IXamlRoot; };
    template <> struct default_interface<winrt::Microsoft::UI::Xaml::XamlRootChangedEventArgs>{ using type = winrt::Microsoft::UI::Xaml::IXamlRootChangedEventArgs; };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IAdaptiveTrigger>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_MinWindowWidth(double*) noexcept = 0;
            virtual int32_t __stdcall put_MinWindowWidth(double) noexcept = 0;
            virtual int32_t __stdcall get_MinWindowHeight(double*) noexcept = 0;
            virtual int32_t __stdcall put_MinWindowHeight(double) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_MinWindowWidthProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MinWindowHeightProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IApplication>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Resources(void**) noexcept = 0;
            virtual int32_t __stdcall put_Resources(void*) noexcept = 0;
            virtual int32_t __stdcall get_DebugSettings(void**) noexcept = 0;
            virtual int32_t __stdcall get_RequestedTheme(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_RequestedTheme(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualKind(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_FocusVisualKind(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_HighContrastAdjustment(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_HighContrastAdjustment(uint32_t) noexcept = 0;
            virtual int32_t __stdcall add_UnhandledException(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_UnhandledException(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall Exit() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IApplication2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall add_ResourceManagerRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ResourceManagerRequested(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IApplication3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DispatcherShutdownMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_DispatcherShutdownMode(int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IApplicationFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IApplicationInitializationCallbackParams>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IApplicationOverrides>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnLaunched(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IApplicationStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Current(void**) noexcept = 0;
            virtual int32_t __stdcall Start(void*) noexcept = 0;
            virtual int32_t __stdcall LoadComponent(void*, void*) noexcept = 0;
            virtual int32_t __stdcall LoadComponentWithResourceLocation(void*, void*, int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IBindingFailedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Message(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IBringIntoViewOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_AnimationDesired(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AnimationDesired(bool) noexcept = 0;
            virtual int32_t __stdcall get_TargetRect(void**) noexcept = 0;
            virtual int32_t __stdcall put_TargetRect(void*) noexcept = 0;
            virtual int32_t __stdcall get_HorizontalAlignmentRatio(double*) noexcept = 0;
            virtual int32_t __stdcall put_HorizontalAlignmentRatio(double) noexcept = 0;
            virtual int32_t __stdcall get_VerticalAlignmentRatio(double*) noexcept = 0;
            virtual int32_t __stdcall put_VerticalAlignmentRatio(double) noexcept = 0;
            virtual int32_t __stdcall get_HorizontalOffset(double*) noexcept = 0;
            virtual int32_t __stdcall put_HorizontalOffset(double) noexcept = 0;
            virtual int32_t __stdcall get_VerticalOffset(double*) noexcept = 0;
            virtual int32_t __stdcall put_VerticalOffset(double) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IBringIntoViewRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_TargetElement(void**) noexcept = 0;
            virtual int32_t __stdcall put_TargetElement(void*) noexcept = 0;
            virtual int32_t __stdcall get_AnimationDesired(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AnimationDesired(bool) noexcept = 0;
            virtual int32_t __stdcall get_TargetRect(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall put_TargetRect(winrt::Windows::Foundation::Rect) noexcept = 0;
            virtual int32_t __stdcall get_HorizontalAlignmentRatio(double*) noexcept = 0;
            virtual int32_t __stdcall get_VerticalAlignmentRatio(double*) noexcept = 0;
            virtual int32_t __stdcall get_HorizontalOffset(double*) noexcept = 0;
            virtual int32_t __stdcall put_HorizontalOffset(double) noexcept = 0;
            virtual int32_t __stdcall get_VerticalOffset(double*) noexcept = 0;
            virtual int32_t __stdcall put_VerticalOffset(double) noexcept = 0;
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IBrushTransition>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Duration(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_Duration(int64_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IBrushTransitionFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IColorPaletteResources>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_AltHigh(void**) noexcept = 0;
            virtual int32_t __stdcall put_AltHigh(void*) noexcept = 0;
            virtual int32_t __stdcall get_AltLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_AltLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_AltMedium(void**) noexcept = 0;
            virtual int32_t __stdcall put_AltMedium(void*) noexcept = 0;
            virtual int32_t __stdcall get_AltMediumHigh(void**) noexcept = 0;
            virtual int32_t __stdcall put_AltMediumHigh(void*) noexcept = 0;
            virtual int32_t __stdcall get_AltMediumLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_AltMediumLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_BaseHigh(void**) noexcept = 0;
            virtual int32_t __stdcall put_BaseHigh(void*) noexcept = 0;
            virtual int32_t __stdcall get_BaseLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_BaseLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_BaseMedium(void**) noexcept = 0;
            virtual int32_t __stdcall put_BaseMedium(void*) noexcept = 0;
            virtual int32_t __stdcall get_BaseMediumHigh(void**) noexcept = 0;
            virtual int32_t __stdcall put_BaseMediumHigh(void*) noexcept = 0;
            virtual int32_t __stdcall get_BaseMediumLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_BaseMediumLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeAltLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeAltLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeBlackHigh(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeBlackHigh(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeBlackLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeBlackLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeBlackMediumLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeBlackMediumLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeBlackMedium(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeBlackMedium(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeDisabledHigh(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeDisabledHigh(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeDisabledLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeDisabledLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeHigh(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeHigh(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeMedium(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeMedium(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeMediumLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeMediumLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeWhite(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeWhite(void*) noexcept = 0;
            virtual int32_t __stdcall get_ChromeGray(void**) noexcept = 0;
            virtual int32_t __stdcall put_ChromeGray(void*) noexcept = 0;
            virtual int32_t __stdcall get_ListLow(void**) noexcept = 0;
            virtual int32_t __stdcall put_ListLow(void*) noexcept = 0;
            virtual int32_t __stdcall get_ListMedium(void**) noexcept = 0;
            virtual int32_t __stdcall put_ListMedium(void*) noexcept = 0;
            virtual int32_t __stdcall get_ErrorText(void**) noexcept = 0;
            virtual int32_t __stdcall put_ErrorText(void*) noexcept = 0;
            virtual int32_t __stdcall get_Accent(void**) noexcept = 0;
            virtual int32_t __stdcall put_Accent(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IColorPaletteResourcesFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ICornerRadiusHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ICornerRadiusHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall FromRadii(double, double, double, double, struct struct_Microsoft_UI_Xaml_CornerRadius*) noexcept = 0;
            virtual int32_t __stdcall FromUniformRadius(double, struct struct_Microsoft_UI_Xaml_CornerRadius*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDataContextChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_NewValue(void**) noexcept = 0;
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDataTemplate>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall LoadContent(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDataTemplateExtension>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall ResetTemplate() noexcept = 0;
            virtual int32_t __stdcall ProcessBinding(uint32_t, bool*) noexcept = 0;
            virtual int32_t __stdcall ProcessBindings(void*, int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDataTemplateFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDataTemplateKey>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DataType(void**) noexcept = 0;
            virtual int32_t __stdcall put_DataType(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDataTemplateKeyFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
            virtual int32_t __stdcall CreateInstanceWithType(void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDataTemplateStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ExtensionInstanceProperty(void**) noexcept = 0;
            virtual int32_t __stdcall GetExtensionInstance(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetExtensionInstance(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDebugSettings>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_EnableFrameRateCounter(bool*) noexcept = 0;
            virtual int32_t __stdcall put_EnableFrameRateCounter(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsBindingTracingEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsBindingTracingEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsTextPerformanceVisualizationEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsTextPerformanceVisualizationEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_FailFastOnErrors(bool*) noexcept = 0;
            virtual int32_t __stdcall put_FailFastOnErrors(bool) noexcept = 0;
            virtual int32_t __stdcall add_BindingFailed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_BindingFailed(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDebugSettings2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsXamlResourceReferenceTracingEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsXamlResourceReferenceTracingEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall add_XamlResourceReferenceFailed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_XamlResourceReferenceFailed(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDebugSettings3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_LayoutCycleTracingLevel(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_LayoutCycleTracingLevel(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_LayoutCycleDebugBreakLevel(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_LayoutCycleDebugBreakLevel(int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDependencyObject>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetValue(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetValue(void*, void*) noexcept = 0;
            virtual int32_t __stdcall ClearValue(void*) noexcept = 0;
            virtual int32_t __stdcall ReadLocalValue(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetAnimationBaseValue(void*, void**) noexcept = 0;
            virtual int32_t __stdcall RegisterPropertyChangedCallback(void*, void*, int64_t*) noexcept = 0;
            virtual int32_t __stdcall UnregisterPropertyChangedCallback(void*, int64_t) noexcept = 0;
            virtual int32_t __stdcall get_Dispatcher(void**) noexcept = 0;
            virtual int32_t __stdcall get_DispatcherQueue(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDependencyObjectCollectionFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDependencyObjectFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDependencyProperty>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetMetadata(struct struct_Windows_UI_Xaml_Interop_TypeName, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDependencyPropertyChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Property(void**) noexcept = 0;
            virtual int32_t __stdcall get_OldValue(void**) noexcept = 0;
            virtual int32_t __stdcall get_NewValue(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDependencyPropertyStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_UnsetValue(void**) noexcept = 0;
            virtual int32_t __stdcall Register(void*, struct struct_Windows_UI_Xaml_Interop_TypeName, struct struct_Windows_UI_Xaml_Interop_TypeName, void*, void**) noexcept = 0;
            virtual int32_t __stdcall RegisterAttached(void*, struct struct_Windows_UI_Xaml_Interop_TypeName, struct struct_Windows_UI_Xaml_Interop_TypeName, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDispatcherTimer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Interval(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_Interval(int64_t) noexcept = 0;
            virtual int32_t __stdcall get_IsEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall add_Tick(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Tick(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall Start() noexcept = 0;
            virtual int32_t __stdcall Stop() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDispatcherTimerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDragEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
            virtual int32_t __stdcall get_Data(void**) noexcept = 0;
            virtual int32_t __stdcall put_Data(void*) noexcept = 0;
            virtual int32_t __stdcall get_DataView(void**) noexcept = 0;
            virtual int32_t __stdcall get_DragUIOverride(void**) noexcept = 0;
            virtual int32_t __stdcall get_Modifiers(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall get_AcceptedOperation(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_AcceptedOperation(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_AllowedOperations(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
            virtual int32_t __stdcall GetPosition(void*, winrt::Windows::Foundation::Point*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDragOperationDeferral>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall Complete() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDragStartingEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Cancel(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Cancel(bool) noexcept = 0;
            virtual int32_t __stdcall get_Data(void**) noexcept = 0;
            virtual int32_t __stdcall get_DragUI(void**) noexcept = 0;
            virtual int32_t __stdcall get_AllowedOperations(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_AllowedOperations(uint32_t) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
            virtual int32_t __stdcall GetPosition(void*, winrt::Windows::Foundation::Point*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDragUI>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall SetContentFromBitmapImage(void*) noexcept = 0;
            virtual int32_t __stdcall SetContentFromBitmapImageWithAnchorPoint(void*, winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall SetContentFromSoftwareBitmap(void*) noexcept = 0;
            virtual int32_t __stdcall SetContentFromSoftwareBitmapWithAnchorPoint(void*, winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall SetContentFromDataPackage() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDragUIOverride>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Caption(void**) noexcept = 0;
            virtual int32_t __stdcall put_Caption(void*) noexcept = 0;
            virtual int32_t __stdcall get_IsContentVisible(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsContentVisible(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsCaptionVisible(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsCaptionVisible(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsGlyphVisible(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsGlyphVisible(bool) noexcept = 0;
            virtual int32_t __stdcall Clear() noexcept = 0;
            virtual int32_t __stdcall SetContentFromBitmapImage(void*) noexcept = 0;
            virtual int32_t __stdcall SetContentFromBitmapImageWithAnchorPoint(void*, winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall SetContentFromSoftwareBitmap(void*) noexcept = 0;
            virtual int32_t __stdcall SetContentFromSoftwareBitmapWithAnchorPoint(void*, winrt::Windows::Foundation::Point) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDropCompletedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DropResult(uint32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDurationHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IDurationHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Automatic(struct struct_Microsoft_UI_Xaml_Duration*) noexcept = 0;
            virtual int32_t __stdcall get_Forever(struct struct_Microsoft_UI_Xaml_Duration*) noexcept = 0;
            virtual int32_t __stdcall Compare(struct struct_Microsoft_UI_Xaml_Duration, struct struct_Microsoft_UI_Xaml_Duration, int32_t*) noexcept = 0;
            virtual int32_t __stdcall FromTimeSpan(int64_t, struct struct_Microsoft_UI_Xaml_Duration*) noexcept = 0;
            virtual int32_t __stdcall GetHasTimeSpan(struct struct_Microsoft_UI_Xaml_Duration, bool*) noexcept = 0;
            virtual int32_t __stdcall Add(struct struct_Microsoft_UI_Xaml_Duration, struct struct_Microsoft_UI_Xaml_Duration, struct struct_Microsoft_UI_Xaml_Duration*) noexcept = 0;
            virtual int32_t __stdcall Equals(struct struct_Microsoft_UI_Xaml_Duration, struct struct_Microsoft_UI_Xaml_Duration, bool*) noexcept = 0;
            virtual int32_t __stdcall Subtract(struct struct_Microsoft_UI_Xaml_Duration, struct struct_Microsoft_UI_Xaml_Duration, struct struct_Microsoft_UI_Xaml_Duration*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IEffectiveViewportChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_EffectiveViewport(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall get_MaxViewport(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall get_BringIntoViewDistanceX(double*) noexcept = 0;
            virtual int32_t __stdcall get_BringIntoViewDistanceY(double*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IElementFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetElement(void*, void**) noexcept = 0;
            virtual int32_t __stdcall RecycleElement(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Data(void**) noexcept = 0;
            virtual int32_t __stdcall put_Data(void*) noexcept = 0;
            virtual int32_t __stdcall get_Parent(void**) noexcept = 0;
            virtual int32_t __stdcall put_Parent(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgsFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Element(void**) noexcept = 0;
            virtual int32_t __stdcall put_Element(void*) noexcept = 0;
            virtual int32_t __stdcall get_Parent(void**) noexcept = 0;
            virtual int32_t __stdcall put_Parent(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgsFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IElementSoundPlayer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IElementSoundPlayerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Volume(double*) noexcept = 0;
            virtual int32_t __stdcall put_Volume(double) noexcept = 0;
            virtual int32_t __stdcall get_State(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_State(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_SpatialAudioMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_SpatialAudioMode(int32_t) noexcept = 0;
            virtual int32_t __stdcall Play(int32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IEventTrigger>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_RoutedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall put_RoutedEvent(void*) noexcept = 0;
            virtual int32_t __stdcall get_Actions(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ErrorMessage(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgsFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IFrameworkElement>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Triggers(void**) noexcept = 0;
            virtual int32_t __stdcall get_Resources(void**) noexcept = 0;
            virtual int32_t __stdcall put_Resources(void*) noexcept = 0;
            virtual int32_t __stdcall get_Tag(void**) noexcept = 0;
            virtual int32_t __stdcall put_Tag(void*) noexcept = 0;
            virtual int32_t __stdcall get_Language(void**) noexcept = 0;
            virtual int32_t __stdcall put_Language(void*) noexcept = 0;
            virtual int32_t __stdcall get_ActualWidth(double*) noexcept = 0;
            virtual int32_t __stdcall get_ActualHeight(double*) noexcept = 0;
            virtual int32_t __stdcall get_Width(double*) noexcept = 0;
            virtual int32_t __stdcall put_Width(double) noexcept = 0;
            virtual int32_t __stdcall get_Height(double*) noexcept = 0;
            virtual int32_t __stdcall put_Height(double) noexcept = 0;
            virtual int32_t __stdcall get_MinWidth(double*) noexcept = 0;
            virtual int32_t __stdcall put_MinWidth(double) noexcept = 0;
            virtual int32_t __stdcall get_MaxWidth(double*) noexcept = 0;
            virtual int32_t __stdcall put_MaxWidth(double) noexcept = 0;
            virtual int32_t __stdcall get_MinHeight(double*) noexcept = 0;
            virtual int32_t __stdcall put_MinHeight(double) noexcept = 0;
            virtual int32_t __stdcall get_MaxHeight(double*) noexcept = 0;
            virtual int32_t __stdcall put_MaxHeight(double) noexcept = 0;
            virtual int32_t __stdcall get_HorizontalAlignment(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_HorizontalAlignment(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_VerticalAlignment(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_VerticalAlignment(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Margin(struct struct_Microsoft_UI_Xaml_Thickness*) noexcept = 0;
            virtual int32_t __stdcall put_Margin(struct struct_Microsoft_UI_Xaml_Thickness) noexcept = 0;
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall put_Name(void*) noexcept = 0;
            virtual int32_t __stdcall get_BaseUri(void**) noexcept = 0;
            virtual int32_t __stdcall get_DataContext(void**) noexcept = 0;
            virtual int32_t __stdcall put_DataContext(void*) noexcept = 0;
            virtual int32_t __stdcall get_AllowFocusOnInteraction(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowFocusOnInteraction(bool) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualMargin(struct struct_Microsoft_UI_Xaml_Thickness*) noexcept = 0;
            virtual int32_t __stdcall put_FocusVisualMargin(struct struct_Microsoft_UI_Xaml_Thickness) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualSecondaryThickness(struct struct_Microsoft_UI_Xaml_Thickness*) noexcept = 0;
            virtual int32_t __stdcall put_FocusVisualSecondaryThickness(struct struct_Microsoft_UI_Xaml_Thickness) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualPrimaryThickness(struct struct_Microsoft_UI_Xaml_Thickness*) noexcept = 0;
            virtual int32_t __stdcall put_FocusVisualPrimaryThickness(struct struct_Microsoft_UI_Xaml_Thickness) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualSecondaryBrush(void**) noexcept = 0;
            virtual int32_t __stdcall put_FocusVisualSecondaryBrush(void*) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualPrimaryBrush(void**) noexcept = 0;
            virtual int32_t __stdcall put_FocusVisualPrimaryBrush(void*) noexcept = 0;
            virtual int32_t __stdcall get_AllowFocusWhenDisabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowFocusWhenDisabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_Style(void**) noexcept = 0;
            virtual int32_t __stdcall put_Style(void*) noexcept = 0;
            virtual int32_t __stdcall get_Parent(void**) noexcept = 0;
            virtual int32_t __stdcall get_FlowDirection(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_FlowDirection(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_RequestedTheme(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_RequestedTheme(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_IsLoaded(bool*) noexcept = 0;
            virtual int32_t __stdcall get_ActualTheme(int32_t*) noexcept = 0;
            virtual int32_t __stdcall add_Loaded(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Loaded(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Unloaded(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Unloaded(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DataContextChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DataContextChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_SizeChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_SizeChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_LayoutUpdated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_LayoutUpdated(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Loading(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Loading(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ActualThemeChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ActualThemeChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_EffectiveViewportChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_EffectiveViewportChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall FindName(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetBinding(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GetBindingExpression(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IFrameworkElementFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IFrameworkElementOverrides>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall MeasureOverride(winrt::Windows::Foundation::Size, winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall ArrangeOverride(winrt::Windows::Foundation::Size, winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall OnApplyTemplate() noexcept = 0;
            virtual int32_t __stdcall GoToElementStateCore(void*, bool, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IFrameworkElementProtected>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall InvalidateViewport() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IFrameworkElementStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_TagProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_LanguageProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ActualWidthProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ActualHeightProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_WidthProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_HeightProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MinWidthProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MaxWidthProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MinHeightProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MaxHeightProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_HorizontalAlignmentProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_VerticalAlignmentProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_MarginProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_NameProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_DataContextProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_AllowFocusOnInteractionProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualMarginProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualSecondaryThicknessProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualPrimaryThicknessProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualSecondaryBrushProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_FocusVisualPrimaryBrushProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_AllowFocusWhenDisabledProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_StyleProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_FlowDirectionProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_RequestedThemeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ActualThemeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall DeferTree(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IFrameworkTemplate>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IFrameworkTemplateFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IFrameworkView>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IFrameworkViewSource>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IGridLengthHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IGridLengthHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Auto(struct struct_Microsoft_UI_Xaml_GridLength*) noexcept = 0;
            virtual int32_t __stdcall FromPixels(double, struct struct_Microsoft_UI_Xaml_GridLength*) noexcept = 0;
            virtual int32_t __stdcall FromValueAndType(double, int32_t, struct struct_Microsoft_UI_Xaml_GridLength*) noexcept = 0;
            virtual int32_t __stdcall GetIsAbsolute(struct struct_Microsoft_UI_Xaml_GridLength, bool*) noexcept = 0;
            virtual int32_t __stdcall GetIsAuto(struct struct_Microsoft_UI_Xaml_GridLength, bool*) noexcept = 0;
            virtual int32_t __stdcall GetIsStar(struct struct_Microsoft_UI_Xaml_GridLength, bool*) noexcept = 0;
            virtual int32_t __stdcall Equals(struct struct_Microsoft_UI_Xaml_GridLength, struct struct_Microsoft_UI_Xaml_GridLength, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ILaunchActivatedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Arguments(void**) noexcept = 0;
            virtual int32_t __stdcall get_UWPLaunchActivatedEventArgs(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IMediaFailedRoutedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ErrorTrace(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IPointHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IPointHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall FromCoordinates(float, float, winrt::Windows::Foundation::Point*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IPropertyMetadata>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DefaultValue(void**) noexcept = 0;
            virtual int32_t __stdcall get_CreateDefaultValueCallback(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IPropertyMetadataFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstanceWithDefaultValue(void*, void*, void**, void**) noexcept = 0;
            virtual int32_t __stdcall CreateInstanceWithDefaultValueAndCallback(void*, void*, void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IPropertyMetadataStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateWithDefaultValue(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateWithDefaultValueAndCallback(void*, void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateWithFactory(void*, void**) noexcept = 0;
            virtual int32_t __stdcall CreateWithFactoryAndCallback(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IPropertyPath>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Path(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IPropertyPathFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IRectHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IRectHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Empty(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall FromCoordinatesAndDimensions(float, float, float, float, winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall FromPoints(winrt::Windows::Foundation::Point, winrt::Windows::Foundation::Point, winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall FromLocationAndSize(winrt::Windows::Foundation::Point, winrt::Windows::Foundation::Size, winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall GetIsEmpty(winrt::Windows::Foundation::Rect, bool*) noexcept = 0;
            virtual int32_t __stdcall GetBottom(winrt::Windows::Foundation::Rect, float*) noexcept = 0;
            virtual int32_t __stdcall GetLeft(winrt::Windows::Foundation::Rect, float*) noexcept = 0;
            virtual int32_t __stdcall GetRight(winrt::Windows::Foundation::Rect, float*) noexcept = 0;
            virtual int32_t __stdcall GetTop(winrt::Windows::Foundation::Rect, float*) noexcept = 0;
            virtual int32_t __stdcall Contains(winrt::Windows::Foundation::Rect, winrt::Windows::Foundation::Point, bool*) noexcept = 0;
            virtual int32_t __stdcall Equals(winrt::Windows::Foundation::Rect, winrt::Windows::Foundation::Rect, bool*) noexcept = 0;
            virtual int32_t __stdcall Intersect(winrt::Windows::Foundation::Rect, winrt::Windows::Foundation::Rect, winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall UnionWithPoint(winrt::Windows::Foundation::Rect, winrt::Windows::Foundation::Point, winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall UnionWithRect(winrt::Windows::Foundation::Rect, winrt::Windows::Foundation::Rect, winrt::Windows::Foundation::Rect*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IResourceDictionary>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Source(void**) noexcept = 0;
            virtual int32_t __stdcall put_Source(void*) noexcept = 0;
            virtual int32_t __stdcall get_MergedDictionaries(void**) noexcept = 0;
            virtual int32_t __stdcall get_ThemeDictionaries(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IResourceDictionaryFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IResourceManagerRequestedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_CustomResourceManager(void**) noexcept = 0;
            virtual int32_t __stdcall put_CustomResourceManager(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IRoutedEvent>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IRoutedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_OriginalSource(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IRoutedEventArgsFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IScalarTransition>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Duration(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_Duration(int64_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IScalarTransitionFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ISetter>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Property(void**) noexcept = 0;
            virtual int32_t __stdcall put_Property(void*) noexcept = 0;
            virtual int32_t __stdcall get_Value(void**) noexcept = 0;
            virtual int32_t __stdcall put_Value(void*) noexcept = 0;
            virtual int32_t __stdcall get_Target(void**) noexcept = 0;
            virtual int32_t __stdcall put_Target(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ISetterBase>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsSealed(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ISetterBaseCollection>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsSealed(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ISetterBaseFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ISetterFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ISizeChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_PreviousSize(winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall get_NewSize(winrt::Windows::Foundation::Size*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ISizeHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ISizeHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Empty(winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall FromDimensions(float, float, winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall GetIsEmpty(winrt::Windows::Foundation::Size, bool*) noexcept = 0;
            virtual int32_t __stdcall Equals(winrt::Windows::Foundation::Size, winrt::Windows::Foundation::Size, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IStateTrigger>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsActive(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsActive(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IStateTriggerBase>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IStateTriggerBaseFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IStateTriggerBaseProtected>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall SetActive(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IStateTriggerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsActiveProperty(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IStyle>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsSealed(bool*) noexcept = 0;
            virtual int32_t __stdcall get_Setters(void**) noexcept = 0;
            virtual int32_t __stdcall get_TargetType(struct struct_Windows_UI_Xaml_Interop_TypeName*) noexcept = 0;
            virtual int32_t __stdcall put_TargetType(struct struct_Windows_UI_Xaml_Interop_TypeName) noexcept = 0;
            virtual int32_t __stdcall get_BasedOn(void**) noexcept = 0;
            virtual int32_t __stdcall put_BasedOn(void*) noexcept = 0;
            virtual int32_t __stdcall Seal() noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IStyleFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(struct struct_Windows_UI_Xaml_Interop_TypeName, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ITargetPropertyPath>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Path(void**) noexcept = 0;
            virtual int32_t __stdcall put_Path(void*) noexcept = 0;
            virtual int32_t __stdcall get_Target(void**) noexcept = 0;
            virtual int32_t __stdcall put_Target(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ITargetPropertyPathFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IThicknessHelper>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IThicknessHelperStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall FromLengths(double, double, double, double, struct struct_Microsoft_UI_Xaml_Thickness*) noexcept = 0;
            virtual int32_t __stdcall FromUniformLength(double, struct struct_Microsoft_UI_Xaml_Thickness*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ITriggerAction>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ITriggerActionFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ITriggerBase>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ITriggerBaseFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IUIElement>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_DesiredSize(winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall get_AllowDrop(bool*) noexcept = 0;
            virtual int32_t __stdcall put_AllowDrop(bool) noexcept = 0;
            virtual int32_t __stdcall get_Opacity(double*) noexcept = 0;
            virtual int32_t __stdcall put_Opacity(double) noexcept = 0;
            virtual int32_t __stdcall get_Clip(void**) noexcept = 0;
            virtual int32_t __stdcall put_Clip(void*) noexcept = 0;
            virtual int32_t __stdcall get_RenderTransform(void**) noexcept = 0;
            virtual int32_t __stdcall put_RenderTransform(void*) noexcept = 0;
            virtual int32_t __stdcall get_Projection(void**) noexcept = 0;
            virtual int32_t __stdcall put_Projection(void*) noexcept = 0;
            virtual int32_t __stdcall get_Transform3D(void**) noexcept = 0;
            virtual int32_t __stdcall put_Transform3D(void*) noexcept = 0;
            virtual int32_t __stdcall get_RenderTransformOrigin(winrt::Windows::Foundation::Point*) noexcept = 0;
            virtual int32_t __stdcall put_RenderTransformOrigin(winrt::Windows::Foundation::Point) noexcept = 0;
            virtual int32_t __stdcall get_IsHitTestVisible(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsHitTestVisible(bool) noexcept = 0;
            virtual int32_t __stdcall get_Visibility(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Visibility(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_RenderSize(winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall get_UseLayoutRounding(bool*) noexcept = 0;
            virtual int32_t __stdcall put_UseLayoutRounding(bool) noexcept = 0;
            virtual int32_t __stdcall get_Transitions(void**) noexcept = 0;
            virtual int32_t __stdcall put_Transitions(void*) noexcept = 0;
            virtual int32_t __stdcall get_CacheMode(void**) noexcept = 0;
            virtual int32_t __stdcall put_CacheMode(void*) noexcept = 0;
            virtual int32_t __stdcall get_IsTapEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsTapEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsDoubleTapEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsDoubleTapEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_CanDrag(bool*) noexcept = 0;
            virtual int32_t __stdcall put_CanDrag(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsRightTapEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsRightTapEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsHoldingEnabled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsHoldingEnabled(bool) noexcept = 0;
            virtual int32_t __stdcall get_ManipulationMode(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_ManipulationMode(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_PointerCaptures(void**) noexcept = 0;
            virtual int32_t __stdcall get_ContextFlyout(void**) noexcept = 0;
            virtual int32_t __stdcall put_ContextFlyout(void*) noexcept = 0;
            virtual int32_t __stdcall get_CompositeMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_CompositeMode(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_Lights(void**) noexcept = 0;
            virtual int32_t __stdcall get_CanBeScrollAnchor(bool*) noexcept = 0;
            virtual int32_t __stdcall put_CanBeScrollAnchor(bool) noexcept = 0;
            virtual int32_t __stdcall get_ExitDisplayModeOnAccessKeyInvoked(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ExitDisplayModeOnAccessKeyInvoked(bool) noexcept = 0;
            virtual int32_t __stdcall get_IsAccessKeyScope(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsAccessKeyScope(bool) noexcept = 0;
            virtual int32_t __stdcall get_AccessKeyScopeOwner(void**) noexcept = 0;
            virtual int32_t __stdcall put_AccessKeyScopeOwner(void*) noexcept = 0;
            virtual int32_t __stdcall get_AccessKey(void**) noexcept = 0;
            virtual int32_t __stdcall put_AccessKey(void*) noexcept = 0;
            virtual int32_t __stdcall get_KeyTipPlacementMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_KeyTipPlacementMode(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_KeyTipHorizontalOffset(double*) noexcept = 0;
            virtual int32_t __stdcall put_KeyTipHorizontalOffset(double) noexcept = 0;
            virtual int32_t __stdcall get_KeyTipVerticalOffset(double*) noexcept = 0;
            virtual int32_t __stdcall put_KeyTipVerticalOffset(double) noexcept = 0;
            virtual int32_t __stdcall get_KeyTipTarget(void**) noexcept = 0;
            virtual int32_t __stdcall put_KeyTipTarget(void*) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusKeyboardNavigation(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_XYFocusKeyboardNavigation(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusUpNavigationStrategy(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_XYFocusUpNavigationStrategy(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusDownNavigationStrategy(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_XYFocusDownNavigationStrategy(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusLeftNavigationStrategy(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_XYFocusLeftNavigationStrategy(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusRightNavigationStrategy(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_XYFocusRightNavigationStrategy(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_KeyboardAccelerators(void**) noexcept = 0;
            virtual int32_t __stdcall get_KeyboardAcceleratorPlacementTarget(void**) noexcept = 0;
            virtual int32_t __stdcall put_KeyboardAcceleratorPlacementTarget(void*) noexcept = 0;
            virtual int32_t __stdcall get_KeyboardAcceleratorPlacementMode(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_KeyboardAcceleratorPlacementMode(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_HighContrastAdjustment(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_HighContrastAdjustment(uint32_t) noexcept = 0;
            virtual int32_t __stdcall get_TabFocusNavigation(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_TabFocusNavigation(int32_t) noexcept = 0;
            virtual int32_t __stdcall get_OpacityTransition(void**) noexcept = 0;
            virtual int32_t __stdcall put_OpacityTransition(void*) noexcept = 0;
            virtual int32_t __stdcall get_Translation(winrt::Windows::Foundation::Numerics::float3*) noexcept = 0;
            virtual int32_t __stdcall put_Translation(winrt::Windows::Foundation::Numerics::float3) noexcept = 0;
            virtual int32_t __stdcall get_TranslationTransition(void**) noexcept = 0;
            virtual int32_t __stdcall put_TranslationTransition(void*) noexcept = 0;
            virtual int32_t __stdcall get_Rotation(float*) noexcept = 0;
            virtual int32_t __stdcall put_Rotation(float) noexcept = 0;
            virtual int32_t __stdcall get_RotationTransition(void**) noexcept = 0;
            virtual int32_t __stdcall put_RotationTransition(void*) noexcept = 0;
            virtual int32_t __stdcall get_Scale(winrt::Windows::Foundation::Numerics::float3*) noexcept = 0;
            virtual int32_t __stdcall put_Scale(winrt::Windows::Foundation::Numerics::float3) noexcept = 0;
            virtual int32_t __stdcall get_ScaleTransition(void**) noexcept = 0;
            virtual int32_t __stdcall put_ScaleTransition(void*) noexcept = 0;
            virtual int32_t __stdcall get_TransformMatrix(winrt::Windows::Foundation::Numerics::float4x4*) noexcept = 0;
            virtual int32_t __stdcall put_TransformMatrix(winrt::Windows::Foundation::Numerics::float4x4) noexcept = 0;
            virtual int32_t __stdcall get_CenterPoint(winrt::Windows::Foundation::Numerics::float3*) noexcept = 0;
            virtual int32_t __stdcall put_CenterPoint(winrt::Windows::Foundation::Numerics::float3) noexcept = 0;
            virtual int32_t __stdcall get_RotationAxis(winrt::Windows::Foundation::Numerics::float3*) noexcept = 0;
            virtual int32_t __stdcall put_RotationAxis(winrt::Windows::Foundation::Numerics::float3) noexcept = 0;
            virtual int32_t __stdcall get_ActualOffset(winrt::Windows::Foundation::Numerics::float3*) noexcept = 0;
            virtual int32_t __stdcall get_ActualSize(winrt::Windows::Foundation::Numerics::float2*) noexcept = 0;
            virtual int32_t __stdcall get_XamlRoot(void**) noexcept = 0;
            virtual int32_t __stdcall put_XamlRoot(void*) noexcept = 0;
            virtual int32_t __stdcall get_Shadow(void**) noexcept = 0;
            virtual int32_t __stdcall put_Shadow(void*) noexcept = 0;
            virtual int32_t __stdcall get_RasterizationScale(double*) noexcept = 0;
            virtual int32_t __stdcall put_RasterizationScale(double) noexcept = 0;
            virtual int32_t __stdcall get_FocusState(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_UseSystemFocusVisuals(bool*) noexcept = 0;
            virtual int32_t __stdcall put_UseSystemFocusVisuals(bool) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusLeft(void**) noexcept = 0;
            virtual int32_t __stdcall put_XYFocusLeft(void*) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusRight(void**) noexcept = 0;
            virtual int32_t __stdcall put_XYFocusRight(void*) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusUp(void**) noexcept = 0;
            virtual int32_t __stdcall put_XYFocusUp(void*) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusDown(void**) noexcept = 0;
            virtual int32_t __stdcall put_XYFocusDown(void*) noexcept = 0;
            virtual int32_t __stdcall get_IsTabStop(bool*) noexcept = 0;
            virtual int32_t __stdcall put_IsTabStop(bool) noexcept = 0;
            virtual int32_t __stdcall get_TabIndex(int32_t*) noexcept = 0;
            virtual int32_t __stdcall put_TabIndex(int32_t) noexcept = 0;
            virtual int32_t __stdcall add_KeyUp(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_KeyUp(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_KeyDown(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_KeyDown(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_GotFocus(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_GotFocus(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_LostFocus(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_LostFocus(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DragStarting(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DragStarting(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DropCompleted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DropCompleted(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_CharacterReceived(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_CharacterReceived(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DragEnter(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DragEnter(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DragLeave(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DragLeave(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DragOver(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DragOver(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Drop(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Drop(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PointerPressed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PointerPressed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PointerMoved(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PointerMoved(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PointerReleased(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PointerReleased(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PointerEntered(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PointerEntered(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PointerExited(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PointerExited(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PointerCaptureLost(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PointerCaptureLost(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PointerCanceled(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PointerCanceled(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PointerWheelChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PointerWheelChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Tapped(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Tapped(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_DoubleTapped(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_DoubleTapped(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Holding(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Holding(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ContextRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ContextRequested(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ContextCanceled(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ContextCanceled(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_RightTapped(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_RightTapped(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ManipulationStarting(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ManipulationStarting(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ManipulationInertiaStarting(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ManipulationInertiaStarting(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ManipulationStarted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ManipulationStarted(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ManipulationDelta(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ManipulationDelta(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ManipulationCompleted(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ManipulationCompleted(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_AccessKeyDisplayRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AccessKeyDisplayRequested(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_AccessKeyDisplayDismissed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AccessKeyDisplayDismissed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_AccessKeyInvoked(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_AccessKeyInvoked(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_ProcessKeyboardAccelerators(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_ProcessKeyboardAccelerators(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_GettingFocus(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_GettingFocus(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_LosingFocus(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_LosingFocus(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_NoFocusCandidateFound(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_NoFocusCandidateFound(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PreviewKeyDown(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PreviewKeyDown(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_PreviewKeyUp(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_PreviewKeyUp(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_BringIntoViewRequested(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_BringIntoViewRequested(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall Measure(winrt::Windows::Foundation::Size) noexcept = 0;
            virtual int32_t __stdcall Arrange(winrt::Windows::Foundation::Rect) noexcept = 0;
            virtual int32_t __stdcall CapturePointer(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall ReleasePointerCapture(void*) noexcept = 0;
            virtual int32_t __stdcall ReleasePointerCaptures() noexcept = 0;
            virtual int32_t __stdcall AddHandler(void*, void*, bool) noexcept = 0;
            virtual int32_t __stdcall RemoveHandler(void*, void*) noexcept = 0;
            virtual int32_t __stdcall TransformToVisual(void*, void**) noexcept = 0;
            virtual int32_t __stdcall InvalidateMeasure() noexcept = 0;
            virtual int32_t __stdcall InvalidateArrange() noexcept = 0;
            virtual int32_t __stdcall UpdateLayout() noexcept = 0;
            virtual int32_t __stdcall CancelDirectManipulations(bool*) noexcept = 0;
            virtual int32_t __stdcall StartDragAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall StartBringIntoView() noexcept = 0;
            virtual int32_t __stdcall StartBringIntoViewWithOptions(void*) noexcept = 0;
            virtual int32_t __stdcall TryInvokeKeyboardAccelerator(void*) noexcept = 0;
            virtual int32_t __stdcall Focus(int32_t, bool*) noexcept = 0;
            virtual int32_t __stdcall StartAnimation(void*) noexcept = 0;
            virtual int32_t __stdcall StopAnimation(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IUIElementFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IUIElementOverrides>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnCreateAutomationPeer(void**) noexcept = 0;
            virtual int32_t __stdcall OnDisconnectVisualChildren() noexcept = 0;
            virtual int32_t __stdcall FindSubElementsForTouchTargeting(winrt::Windows::Foundation::Point, winrt::Windows::Foundation::Rect, void**) noexcept = 0;
            virtual int32_t __stdcall GetChildrenInTabFocusOrder(void**) noexcept = 0;
            virtual int32_t __stdcall OnKeyboardAcceleratorInvoked(void*) noexcept = 0;
            virtual int32_t __stdcall OnProcessKeyboardAccelerators(void*) noexcept = 0;
            virtual int32_t __stdcall OnBringIntoViewRequested(void*) noexcept = 0;
            virtual int32_t __stdcall PopulatePropertyInfoOverride(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IUIElementProtected>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ProtectedCursor(void**) noexcept = 0;
            virtual int32_t __stdcall put_ProtectedCursor(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IUIElementStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_KeyDownEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_KeyUpEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PointerEnteredEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PointerPressedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PointerMovedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PointerReleasedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PointerExitedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PointerCaptureLostEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PointerCanceledEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PointerWheelChangedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_TappedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_DoubleTappedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_HoldingEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_RightTappedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_ManipulationStartingEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_ManipulationInertiaStartingEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_ManipulationStartedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_ManipulationDeltaEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_ManipulationCompletedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_DragEnterEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_DragLeaveEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_DragOverEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_DropEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_GettingFocusEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_LosingFocusEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_NoFocusCandidateFoundEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PreviewKeyDownEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_CharacterReceivedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_PreviewKeyUpEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_BringIntoViewRequestedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_ContextRequestedEvent(void**) noexcept = 0;
            virtual int32_t __stdcall get_AllowDropProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_OpacityProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ClipProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_RenderTransformProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ProjectionProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_Transform3DProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_RenderTransformOriginProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsHitTestVisibleProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_VisibilityProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_UseLayoutRoundingProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_TransitionsProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_CacheModeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsTapEnabledProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsDoubleTapEnabledProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_CanDragProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsRightTapEnabledProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsHoldingEnabledProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ManipulationModeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_PointerCapturesProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ContextFlyoutProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_CompositeModeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_LightsProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_CanBeScrollAnchorProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ExitDisplayModeOnAccessKeyInvokedProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsAccessKeyScopeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_AccessKeyScopeOwnerProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_AccessKeyProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_KeyTipPlacementModeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_KeyTipHorizontalOffsetProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_KeyTipVerticalOffsetProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_KeyTipTargetProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusKeyboardNavigationProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusUpNavigationStrategyProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusDownNavigationStrategyProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusLeftNavigationStrategyProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusRightNavigationStrategyProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_KeyboardAcceleratorPlacementTargetProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_KeyboardAcceleratorPlacementModeProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_HighContrastAdjustmentProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_TabFocusNavigationProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_ShadowProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_FocusStateProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_UseSystemFocusVisualsProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusLeftProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusRightProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusUpProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_XYFocusDownProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsTabStopProperty(void**) noexcept = 0;
            virtual int32_t __stdcall get_TabIndexProperty(void**) noexcept = 0;
            virtual int32_t __stdcall TryStartDirectManipulation(void*, bool*) noexcept = 0;
            virtual int32_t __stdcall RegisterAsScrollPort(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IUIElementWeakCollectionFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IUnhandledExceptionEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Exception(winrt::hresult*) noexcept = 0;
            virtual int32_t __stdcall get_Message(void**) noexcept = 0;
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVector3Transition>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Duration(int64_t*) noexcept = 0;
            virtual int32_t __stdcall put_Duration(int64_t) noexcept = 0;
            virtual int32_t __stdcall get_Components(uint32_t*) noexcept = 0;
            virtual int32_t __stdcall put_Components(uint32_t) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVector3TransitionFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualState>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_Storyboard(void**) noexcept = 0;
            virtual int32_t __stdcall put_Storyboard(void*) noexcept = 0;
            virtual int32_t __stdcall get_Setters(void**) noexcept = 0;
            virtual int32_t __stdcall get_StateTriggers(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualStateChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_OldState(void**) noexcept = 0;
            virtual int32_t __stdcall put_OldState(void*) noexcept = 0;
            virtual int32_t __stdcall get_NewState(void**) noexcept = 0;
            virtual int32_t __stdcall put_NewState(void*) noexcept = 0;
            virtual int32_t __stdcall get_Control(void**) noexcept = 0;
            virtual int32_t __stdcall put_Control(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualStateGroup>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_Transitions(void**) noexcept = 0;
            virtual int32_t __stdcall get_States(void**) noexcept = 0;
            virtual int32_t __stdcall get_CurrentState(void**) noexcept = 0;
            virtual int32_t __stdcall add_CurrentStateChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_CurrentStateChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_CurrentStateChanging(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_CurrentStateChanging(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualStateManager>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualStateManagerFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualStateManagerOverrides>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GoToStateCore(void*, void*, void*, void*, void*, bool, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualStateManagerProtected>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall RaiseCurrentStateChanging(void*, void*, void*, void*) noexcept = 0;
            virtual int32_t __stdcall RaiseCurrentStateChanged(void*, void*, void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualStateManagerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetVisualStateGroups(void*, void**) noexcept = 0;
            virtual int32_t __stdcall get_CustomVisualStateManagerProperty(void**) noexcept = 0;
            virtual int32_t __stdcall GetCustomVisualStateManager(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetCustomVisualStateManager(void*, void*) noexcept = 0;
            virtual int32_t __stdcall GoToState(void*, void*, bool, bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualTransition>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_GeneratedDuration(struct struct_Microsoft_UI_Xaml_Duration*) noexcept = 0;
            virtual int32_t __stdcall put_GeneratedDuration(struct struct_Microsoft_UI_Xaml_Duration) noexcept = 0;
            virtual int32_t __stdcall get_GeneratedEasingFunction(void**) noexcept = 0;
            virtual int32_t __stdcall put_GeneratedEasingFunction(void*) noexcept = 0;
            virtual int32_t __stdcall get_To(void**) noexcept = 0;
            virtual int32_t __stdcall put_To(void*) noexcept = 0;
            virtual int32_t __stdcall get_From(void**) noexcept = 0;
            virtual int32_t __stdcall put_From(void*) noexcept = 0;
            virtual int32_t __stdcall get_Storyboard(void**) noexcept = 0;
            virtual int32_t __stdcall put_Storyboard(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IVisualTransitionFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IWindow>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Bounds(winrt::Windows::Foundation::Rect*) noexcept = 0;
            virtual int32_t __stdcall get_Visible(bool*) noexcept = 0;
            virtual int32_t __stdcall get_Content(void**) noexcept = 0;
            virtual int32_t __stdcall put_Content(void*) noexcept = 0;
            virtual int32_t __stdcall get_CoreWindow(void**) noexcept = 0;
            virtual int32_t __stdcall get_Compositor(void**) noexcept = 0;
            virtual int32_t __stdcall get_Dispatcher(void**) noexcept = 0;
            virtual int32_t __stdcall get_DispatcherQueue(void**) noexcept = 0;
            virtual int32_t __stdcall get_Title(void**) noexcept = 0;
            virtual int32_t __stdcall put_Title(void*) noexcept = 0;
            virtual int32_t __stdcall get_ExtendsContentIntoTitleBar(bool*) noexcept = 0;
            virtual int32_t __stdcall put_ExtendsContentIntoTitleBar(bool) noexcept = 0;
            virtual int32_t __stdcall add_Activated(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Activated(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_Closed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Closed(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_SizeChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_SizeChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall add_VisibilityChanged(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_VisibilityChanged(winrt::event_token) noexcept = 0;
            virtual int32_t __stdcall Activate() noexcept = 0;
            virtual int32_t __stdcall Close() noexcept = 0;
            virtual int32_t __stdcall SetTitleBar(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IWindow2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_SystemBackdrop(void**) noexcept = 0;
            virtual int32_t __stdcall put_SystemBackdrop(void*) noexcept = 0;
            virtual int32_t __stdcall get_AppWindow(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IWindowActivatedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
            virtual int32_t __stdcall get_WindowActivationState(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IWindowEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IWindowFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IWindowSizeChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
            virtual int32_t __stdcall get_Size(winrt::Windows::Foundation::Size*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IWindowStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Current(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IWindowVisibilityChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Handled(bool*) noexcept = 0;
            virtual int32_t __stdcall put_Handled(bool) noexcept = 0;
            virtual int32_t __stdcall get_Visible(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IXamlIsland>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Content(void**) noexcept = 0;
            virtual int32_t __stdcall put_Content(void*) noexcept = 0;
            virtual int32_t __stdcall get_ContentIsland(void**) noexcept = 0;
            virtual int32_t __stdcall get_SystemBackdrop(void**) noexcept = 0;
            virtual int32_t __stdcall put_SystemBackdrop(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IXamlIslandFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IXamlResourceReferenceFailedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Message(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IXamlRoot>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Content(void**) noexcept = 0;
            virtual int32_t __stdcall get_Size(winrt::Windows::Foundation::Size*) noexcept = 0;
            virtual int32_t __stdcall get_RasterizationScale(double*) noexcept = 0;
            virtual int32_t __stdcall get_IsHostVisible(bool*) noexcept = 0;
            virtual int32_t __stdcall add_Changed(void*, winrt::event_token*) noexcept = 0;
            virtual int32_t __stdcall remove_Changed(winrt::event_token) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IXamlRoot2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ContentIslandEnvironment(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IXamlRoot3>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_CoordinateConverter(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IXamlRoot4>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_ContentIsland(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IXamlRootChangedEventArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::IXamlServiceProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetService(struct struct_Windows_UI_Xaml_Interop_TypeName, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ApplicationInitializationCallback>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::BindingFailedEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::CreateDefaultValueCallback>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedCallback>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::DependencyPropertyChangedEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::DragEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::EnteredBackgroundEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::ExceptionRoutedEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::LeavingBackgroundEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::PropertyChangedCallback>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::RoutedEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::SizeChangedEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::SuspendingEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::UnhandledExceptionEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::UI::Xaml::VisualStateChangedEventHandler>
    {
        struct WINRT_IMPL_NOVTABLE type : unknown_abi
        {
            virtual int32_t __stdcall Invoke(void*, void*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IAdaptiveTrigger
    {
        [[nodiscard]] auto MinWindowWidth() const;
        auto MinWindowWidth(double value) const;
        [[nodiscard]] auto MinWindowHeight() const;
        auto MinWindowHeight(double value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IAdaptiveTrigger>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IAdaptiveTrigger<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IAdaptiveTriggerFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IAdaptiveTriggerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IAdaptiveTriggerStatics
    {
        [[nodiscard]] auto MinWindowWidthProperty() const;
        [[nodiscard]] auto MinWindowHeightProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IAdaptiveTriggerStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IAdaptiveTriggerStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IApplication
    {
        [[nodiscard]] auto Resources() const;
        auto Resources(winrt::Microsoft::UI::Xaml::ResourceDictionary const& value) const;
        [[nodiscard]] auto DebugSettings() const;
        [[nodiscard]] auto RequestedTheme() const;
        auto RequestedTheme(winrt::Microsoft::UI::Xaml::ApplicationTheme const& value) const;
        [[nodiscard]] auto FocusVisualKind() const;
        auto FocusVisualKind(winrt::Microsoft::UI::Xaml::FocusVisualKind const& value) const;
        [[nodiscard]] auto HighContrastAdjustment() const;
        auto HighContrastAdjustment(winrt::Microsoft::UI::Xaml::ApplicationHighContrastAdjustment const& value) const;
        auto UnhandledException(winrt::Microsoft::UI::Xaml::UnhandledExceptionEventHandler const& handler) const;
        using UnhandledException_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IApplication, &impl::abi_t<winrt::Microsoft::UI::Xaml::IApplication>::remove_UnhandledException>;
        [[nodiscard]] auto UnhandledException(auto_revoke_t, winrt::Microsoft::UI::Xaml::UnhandledExceptionEventHandler const& handler) const;
        auto UnhandledException(winrt::event_token const& token) const noexcept;
        auto Exit() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IApplication>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IApplication<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IApplication2
    {
        auto ResourceManagerRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::ResourceManagerRequestedEventArgs> const& handler) const;
        using ResourceManagerRequested_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IApplication2, &impl::abi_t<winrt::Microsoft::UI::Xaml::IApplication2>::remove_ResourceManagerRequested>;
        [[nodiscard]] auto ResourceManagerRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::ResourceManagerRequestedEventArgs> const& handler) const;
        auto ResourceManagerRequested(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IApplication2>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IApplication2<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IApplication3
    {
        [[nodiscard]] auto DispatcherShutdownMode() const;
        auto DispatcherShutdownMode(winrt::Microsoft::UI::Xaml::DispatcherShutdownMode const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IApplication3>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IApplication3<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IApplicationFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IApplicationFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IApplicationFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IApplicationInitializationCallbackParams
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IApplicationInitializationCallbackParams>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IApplicationInitializationCallbackParams<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IApplicationOverrides
    {
        auto OnLaunched(winrt::Microsoft::UI::Xaml::LaunchActivatedEventArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IApplicationOverrides>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IApplicationOverrides<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IApplicationStatics
    {
        [[nodiscard]] auto Current() const;
        auto Start(winrt::Microsoft::UI::Xaml::ApplicationInitializationCallback const& callback) const;
        auto LoadComponent(winrt::Windows::Foundation::IInspectable const& component, winrt::Windows::Foundation::Uri const& resourceLocator) const;
        auto LoadComponent(winrt::Windows::Foundation::IInspectable const& component, winrt::Windows::Foundation::Uri const& resourceLocator, winrt::Microsoft::UI::Xaml::Controls::Primitives::ComponentResourceLocation const& componentResourceLocation) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IApplicationStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IApplicationStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IBindingFailedEventArgs
    {
        [[nodiscard]] auto Message() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IBindingFailedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IBindingFailedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IBringIntoViewOptions
    {
        [[nodiscard]] auto AnimationDesired() const;
        auto AnimationDesired(bool value) const;
        [[nodiscard]] auto TargetRect() const;
        auto TargetRect(winrt::Windows::Foundation::IReference<winrt::Windows::Foundation::Rect> const& value) const;
        [[nodiscard]] auto HorizontalAlignmentRatio() const;
        auto HorizontalAlignmentRatio(double value) const;
        [[nodiscard]] auto VerticalAlignmentRatio() const;
        auto VerticalAlignmentRatio(double value) const;
        [[nodiscard]] auto HorizontalOffset() const;
        auto HorizontalOffset(double value) const;
        [[nodiscard]] auto VerticalOffset() const;
        auto VerticalOffset(double value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IBringIntoViewOptions>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IBringIntoViewOptions<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IBringIntoViewRequestedEventArgs
    {
        [[nodiscard]] auto TargetElement() const;
        auto TargetElement(winrt::Microsoft::UI::Xaml::UIElement const& value) const;
        [[nodiscard]] auto AnimationDesired() const;
        auto AnimationDesired(bool value) const;
        [[nodiscard]] auto TargetRect() const;
        auto TargetRect(winrt::Windows::Foundation::Rect const& value) const;
        [[nodiscard]] auto HorizontalAlignmentRatio() const;
        [[nodiscard]] auto VerticalAlignmentRatio() const;
        [[nodiscard]] auto HorizontalOffset() const;
        auto HorizontalOffset(double value) const;
        [[nodiscard]] auto VerticalOffset() const;
        auto VerticalOffset(double value) const;
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IBringIntoViewRequestedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IBringIntoViewRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IBrushTransition
    {
        [[nodiscard]] auto Duration() const;
        auto Duration(winrt::Windows::Foundation::TimeSpan const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IBrushTransition>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IBrushTransition<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IBrushTransitionFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IBrushTransitionFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IBrushTransitionFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IColorPaletteResources
    {
        [[nodiscard]] auto AltHigh() const;
        auto AltHigh(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto AltLow() const;
        auto AltLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto AltMedium() const;
        auto AltMedium(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto AltMediumHigh() const;
        auto AltMediumHigh(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto AltMediumLow() const;
        auto AltMediumLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto BaseHigh() const;
        auto BaseHigh(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto BaseLow() const;
        auto BaseLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto BaseMedium() const;
        auto BaseMedium(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto BaseMediumHigh() const;
        auto BaseMediumHigh(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto BaseMediumLow() const;
        auto BaseMediumLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeAltLow() const;
        auto ChromeAltLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeBlackHigh() const;
        auto ChromeBlackHigh(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeBlackLow() const;
        auto ChromeBlackLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeBlackMediumLow() const;
        auto ChromeBlackMediumLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeBlackMedium() const;
        auto ChromeBlackMedium(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeDisabledHigh() const;
        auto ChromeDisabledHigh(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeDisabledLow() const;
        auto ChromeDisabledLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeHigh() const;
        auto ChromeHigh(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeLow() const;
        auto ChromeLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeMedium() const;
        auto ChromeMedium(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeMediumLow() const;
        auto ChromeMediumLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeWhite() const;
        auto ChromeWhite(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ChromeGray() const;
        auto ChromeGray(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ListLow() const;
        auto ListLow(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ListMedium() const;
        auto ListMedium(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto ErrorText() const;
        auto ErrorText(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
        [[nodiscard]] auto Accent() const;
        auto Accent(winrt::Windows::Foundation::IReference<winrt::Windows::UI::Color> const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IColorPaletteResources>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IColorPaletteResources<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IColorPaletteResourcesFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IColorPaletteResourcesFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IColorPaletteResourcesFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ICornerRadiusHelper
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ICornerRadiusHelper>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ICornerRadiusHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ICornerRadiusHelperStatics
    {
        auto FromRadii(double topLeft, double topRight, double bottomRight, double bottomLeft) const;
        auto FromUniformRadius(double uniformRadius) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ICornerRadiusHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ICornerRadiusHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDataContextChangedEventArgs
    {
        [[nodiscard]] auto NewValue() const;
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDataContextChangedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDataContextChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDataTemplate
    {
        auto LoadContent() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDataTemplate>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDataTemplate<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDataTemplateExtension
    {
        auto ResetTemplate() const;
        auto ProcessBinding(uint32_t phase) const;
        auto ProcessBindings(winrt::Microsoft::UI::Xaml::Controls::ContainerContentChangingEventArgs const& arg) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDataTemplateExtension>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDataTemplateExtension<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDataTemplateFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDataTemplateFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDataTemplateFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDataTemplateKey
    {
        [[nodiscard]] auto DataType() const;
        auto DataType(winrt::Windows::Foundation::IInspectable const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDataTemplateKey>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDataTemplateKey<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDataTemplateKeyFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
        auto CreateInstanceWithType(winrt::Windows::Foundation::IInspectable const& dataType, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDataTemplateKeyFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDataTemplateKeyFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDataTemplateStatics
    {
        [[nodiscard]] auto ExtensionInstanceProperty() const;
        auto GetExtensionInstance(winrt::Microsoft::UI::Xaml::FrameworkElement const& element) const;
        auto SetExtensionInstance(winrt::Microsoft::UI::Xaml::FrameworkElement const& element, winrt::Microsoft::UI::Xaml::IDataTemplateExtension const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDataTemplateStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDataTemplateStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDebugSettings
    {
        [[nodiscard]] auto EnableFrameRateCounter() const;
        auto EnableFrameRateCounter(bool value) const;
        [[nodiscard]] auto IsBindingTracingEnabled() const;
        auto IsBindingTracingEnabled(bool value) const;
        [[nodiscard]] auto IsTextPerformanceVisualizationEnabled() const;
        auto IsTextPerformanceVisualizationEnabled(bool value) const;
        [[nodiscard]] auto FailFastOnErrors() const;
        auto FailFastOnErrors(bool value) const;
        auto BindingFailed(winrt::Microsoft::UI::Xaml::BindingFailedEventHandler const& handler) const;
        using BindingFailed_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IDebugSettings, &impl::abi_t<winrt::Microsoft::UI::Xaml::IDebugSettings>::remove_BindingFailed>;
        [[nodiscard]] auto BindingFailed(auto_revoke_t, winrt::Microsoft::UI::Xaml::BindingFailedEventHandler const& handler) const;
        auto BindingFailed(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDebugSettings>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDebugSettings<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDebugSettings2
    {
        [[nodiscard]] auto IsXamlResourceReferenceTracingEnabled() const;
        auto IsXamlResourceReferenceTracingEnabled(bool value) const;
        auto XamlResourceReferenceFailed(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::DebugSettings, winrt::Microsoft::UI::Xaml::XamlResourceReferenceFailedEventArgs> const& handler) const;
        using XamlResourceReferenceFailed_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IDebugSettings2, &impl::abi_t<winrt::Microsoft::UI::Xaml::IDebugSettings2>::remove_XamlResourceReferenceFailed>;
        [[nodiscard]] auto XamlResourceReferenceFailed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::DebugSettings, winrt::Microsoft::UI::Xaml::XamlResourceReferenceFailedEventArgs> const& handler) const;
        auto XamlResourceReferenceFailed(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDebugSettings2>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDebugSettings2<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDebugSettings3
    {
        [[nodiscard]] auto LayoutCycleTracingLevel() const;
        auto LayoutCycleTracingLevel(winrt::Microsoft::UI::Xaml::LayoutCycleTracingLevel const& value) const;
        [[nodiscard]] auto LayoutCycleDebugBreakLevel() const;
        auto LayoutCycleDebugBreakLevel(winrt::Microsoft::UI::Xaml::LayoutCycleDebugBreakLevel const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDebugSettings3>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDebugSettings3<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDependencyObject
    {
        auto GetValue(winrt::Microsoft::UI::Xaml::DependencyProperty const& dp) const;
        auto SetValue(winrt::Microsoft::UI::Xaml::DependencyProperty const& dp, winrt::Windows::Foundation::IInspectable const& value) const;
        auto ClearValue(winrt::Microsoft::UI::Xaml::DependencyProperty const& dp) const;
        auto ReadLocalValue(winrt::Microsoft::UI::Xaml::DependencyProperty const& dp) const;
        auto GetAnimationBaseValue(winrt::Microsoft::UI::Xaml::DependencyProperty const& dp) const;
        auto RegisterPropertyChangedCallback(winrt::Microsoft::UI::Xaml::DependencyProperty const& dp, winrt::Microsoft::UI::Xaml::DependencyPropertyChangedCallback const& callback) const;
        auto UnregisterPropertyChangedCallback(winrt::Microsoft::UI::Xaml::DependencyProperty const& dp, int64_t token) const;
        [[nodiscard]] auto Dispatcher() const;
        [[nodiscard]] auto DispatcherQueue() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDependencyObject>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDependencyObject<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDependencyObjectCollectionFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDependencyObjectCollectionFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDependencyObjectCollectionFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDependencyObjectFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDependencyObjectFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDependencyObjectFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDependencyProperty
    {
        auto GetMetadata(winrt::Windows::UI::Xaml::Interop::TypeName const& forType) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDependencyProperty>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDependencyProperty<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDependencyPropertyChangedEventArgs
    {
        [[nodiscard]] auto Property() const;
        [[nodiscard]] auto OldValue() const;
        [[nodiscard]] auto NewValue() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDependencyPropertyChangedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDependencyPropertyChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDependencyPropertyStatics
    {
        [[nodiscard]] auto UnsetValue() const;
        auto Register(param::hstring const& name, winrt::Windows::UI::Xaml::Interop::TypeName const& propertyType, winrt::Windows::UI::Xaml::Interop::TypeName const& ownerType, winrt::Microsoft::UI::Xaml::PropertyMetadata const& typeMetadata) const;
        auto RegisterAttached(param::hstring const& name, winrt::Windows::UI::Xaml::Interop::TypeName const& propertyType, winrt::Windows::UI::Xaml::Interop::TypeName const& ownerType, winrt::Microsoft::UI::Xaml::PropertyMetadata const& defaultMetadata) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDependencyPropertyStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDependencyPropertyStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDispatcherTimer
    {
        [[nodiscard]] auto Interval() const;
        auto Interval(winrt::Windows::Foundation::TimeSpan const& value) const;
        [[nodiscard]] auto IsEnabled() const;
        auto Tick(winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Tick_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IDispatcherTimer, &impl::abi_t<winrt::Microsoft::UI::Xaml::IDispatcherTimer>::remove_Tick>;
        [[nodiscard]] auto Tick(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto Tick(winrt::event_token const& token) const noexcept;
        auto Start() const;
        auto Stop() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDispatcherTimer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDispatcherTimer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDispatcherTimerFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDispatcherTimerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDispatcherTimerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDragEventArgs
    {
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
        [[nodiscard]] auto Data() const;
        auto Data(winrt::Windows::ApplicationModel::DataTransfer::DataPackage const& value) const;
        [[nodiscard]] auto DataView() const;
        [[nodiscard]] auto DragUIOverride() const;
        [[nodiscard]] auto Modifiers() const;
        [[nodiscard]] auto AcceptedOperation() const;
        auto AcceptedOperation(winrt::Windows::ApplicationModel::DataTransfer::DataPackageOperation const& value) const;
        [[nodiscard]] auto AllowedOperations() const;
        auto GetDeferral() const;
        auto GetPosition(winrt::Microsoft::UI::Xaml::UIElement const& relativeTo) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDragEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDragEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDragOperationDeferral
    {
        auto Complete() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDragOperationDeferral>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDragOperationDeferral<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDragStartingEventArgs
    {
        [[nodiscard]] auto Cancel() const;
        auto Cancel(bool value) const;
        [[nodiscard]] auto Data() const;
        [[nodiscard]] auto DragUI() const;
        [[nodiscard]] auto AllowedOperations() const;
        auto AllowedOperations(winrt::Windows::ApplicationModel::DataTransfer::DataPackageOperation const& value) const;
        auto GetDeferral() const;
        auto GetPosition(winrt::Microsoft::UI::Xaml::UIElement const& relativeTo) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDragStartingEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDragStartingEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDragUI
    {
        auto SetContentFromBitmapImage(winrt::Microsoft::UI::Xaml::Media::Imaging::BitmapImage const& bitmapImage) const;
        auto SetContentFromBitmapImage(winrt::Microsoft::UI::Xaml::Media::Imaging::BitmapImage const& bitmapImage, winrt::Windows::Foundation::Point const& anchorPoint) const;
        auto SetContentFromSoftwareBitmap(winrt::Windows::Graphics::Imaging::SoftwareBitmap const& softwareBitmap) const;
        auto SetContentFromSoftwareBitmap(winrt::Windows::Graphics::Imaging::SoftwareBitmap const& softwareBitmap, winrt::Windows::Foundation::Point const& anchorPoint) const;
        auto SetContentFromDataPackage() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDragUI>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDragUI<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDragUIOverride
    {
        [[nodiscard]] auto Caption() const;
        auto Caption(param::hstring const& value) const;
        [[nodiscard]] auto IsContentVisible() const;
        auto IsContentVisible(bool value) const;
        [[nodiscard]] auto IsCaptionVisible() const;
        auto IsCaptionVisible(bool value) const;
        [[nodiscard]] auto IsGlyphVisible() const;
        auto IsGlyphVisible(bool value) const;
        auto Clear() const;
        auto SetContentFromBitmapImage(winrt::Microsoft::UI::Xaml::Media::Imaging::BitmapImage const& bitmapImage) const;
        auto SetContentFromBitmapImage(winrt::Microsoft::UI::Xaml::Media::Imaging::BitmapImage const& bitmapImage, winrt::Windows::Foundation::Point const& anchorPoint) const;
        auto SetContentFromSoftwareBitmap(winrt::Windows::Graphics::Imaging::SoftwareBitmap const& softwareBitmap) const;
        auto SetContentFromSoftwareBitmap(winrt::Windows::Graphics::Imaging::SoftwareBitmap const& softwareBitmap, winrt::Windows::Foundation::Point const& anchorPoint) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDragUIOverride>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDragUIOverride<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDropCompletedEventArgs
    {
        [[nodiscard]] auto DropResult() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDropCompletedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDropCompletedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDurationHelper
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDurationHelper>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDurationHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IDurationHelperStatics
    {
        [[nodiscard]] auto Automatic() const;
        [[nodiscard]] auto Forever() const;
        auto Compare(winrt::Microsoft::UI::Xaml::Duration const& duration1, winrt::Microsoft::UI::Xaml::Duration const& duration2) const;
        auto FromTimeSpan(winrt::Windows::Foundation::TimeSpan const& timeSpan) const;
        auto GetHasTimeSpan(winrt::Microsoft::UI::Xaml::Duration const& target) const;
        auto Add(winrt::Microsoft::UI::Xaml::Duration const& target, winrt::Microsoft::UI::Xaml::Duration const& duration) const;
        auto Equals(winrt::Microsoft::UI::Xaml::Duration const& target, winrt::Microsoft::UI::Xaml::Duration const& value) const;
        auto Subtract(winrt::Microsoft::UI::Xaml::Duration const& target, winrt::Microsoft::UI::Xaml::Duration const& duration) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IDurationHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IDurationHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IEffectiveViewportChangedEventArgs
    {
        [[nodiscard]] auto EffectiveViewport() const;
        [[nodiscard]] auto MaxViewport() const;
        [[nodiscard]] auto BringIntoViewDistanceX() const;
        [[nodiscard]] auto BringIntoViewDistanceY() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IEffectiveViewportChangedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IEffectiveViewportChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IElementFactory
    {
        auto GetElement(winrt::Microsoft::UI::Xaml::ElementFactoryGetArgs const& args) const;
        auto RecycleElement(winrt::Microsoft::UI::Xaml::ElementFactoryRecycleArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IElementFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IElementFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IElementFactoryGetArgs
    {
        [[nodiscard]] auto Data() const;
        auto Data(winrt::Windows::Foundation::IInspectable const& value) const;
        [[nodiscard]] auto Parent() const;
        auto Parent(winrt::Microsoft::UI::Xaml::UIElement const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IElementFactoryGetArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IElementFactoryGetArgsFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IElementFactoryGetArgsFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IElementFactoryGetArgsFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IElementFactoryRecycleArgs
    {
        [[nodiscard]] auto Element() const;
        auto Element(winrt::Microsoft::UI::Xaml::UIElement const& value) const;
        [[nodiscard]] auto Parent() const;
        auto Parent(winrt::Microsoft::UI::Xaml::UIElement const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IElementFactoryRecycleArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IElementFactoryRecycleArgsFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IElementFactoryRecycleArgsFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IElementFactoryRecycleArgsFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IElementSoundPlayer
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IElementSoundPlayer>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IElementSoundPlayer<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IElementSoundPlayerStatics
    {
        [[nodiscard]] auto Volume() const;
        auto Volume(double value) const;
        [[nodiscard]] auto State() const;
        auto State(winrt::Microsoft::UI::Xaml::ElementSoundPlayerState const& value) const;
        [[nodiscard]] auto SpatialAudioMode() const;
        auto SpatialAudioMode(winrt::Microsoft::UI::Xaml::ElementSpatialAudioMode const& value) const;
        auto Play(winrt::Microsoft::UI::Xaml::ElementSoundKind const& sound) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IElementSoundPlayerStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IElementSoundPlayerStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IEventTrigger
    {
        [[nodiscard]] auto RoutedEvent() const;
        auto RoutedEvent(winrt::Microsoft::UI::Xaml::RoutedEvent const& value) const;
        [[nodiscard]] auto Actions() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IEventTrigger>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IEventTrigger<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IExceptionRoutedEventArgs
    {
        [[nodiscard]] auto ErrorMessage() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IExceptionRoutedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IExceptionRoutedEventArgsFactory
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IExceptionRoutedEventArgsFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IExceptionRoutedEventArgsFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IFrameworkElement
    {
        [[nodiscard]] auto Triggers() const;
        [[nodiscard]] auto Resources() const;
        auto Resources(winrt::Microsoft::UI::Xaml::ResourceDictionary const& value) const;
        [[nodiscard]] auto Tag() const;
        auto Tag(winrt::Windows::Foundation::IInspectable const& value) const;
        [[nodiscard]] auto Language() const;
        auto Language(param::hstring const& value) const;
        [[nodiscard]] auto ActualWidth() const;
        [[nodiscard]] auto ActualHeight() const;
        [[nodiscard]] auto Width() const;
        auto Width(double value) const;
        [[nodiscard]] auto Height() const;
        auto Height(double value) const;
        [[nodiscard]] auto MinWidth() const;
        auto MinWidth(double value) const;
        [[nodiscard]] auto MaxWidth() const;
        auto MaxWidth(double value) const;
        [[nodiscard]] auto MinHeight() const;
        auto MinHeight(double value) const;
        [[nodiscard]] auto MaxHeight() const;
        auto MaxHeight(double value) const;
        [[nodiscard]] auto HorizontalAlignment() const;
        auto HorizontalAlignment(winrt::Microsoft::UI::Xaml::HorizontalAlignment const& value) const;
        [[nodiscard]] auto VerticalAlignment() const;
        auto VerticalAlignment(winrt::Microsoft::UI::Xaml::VerticalAlignment const& value) const;
        [[nodiscard]] auto Margin() const;
        auto Margin(winrt::Microsoft::UI::Xaml::Thickness const& value) const;
        [[nodiscard]] auto Name() const;
        auto Name(param::hstring const& value) const;
        [[nodiscard]] auto BaseUri() const;
        [[nodiscard]] auto DataContext() const;
        auto DataContext(winrt::Windows::Foundation::IInspectable const& value) const;
        [[nodiscard]] auto AllowFocusOnInteraction() const;
        auto AllowFocusOnInteraction(bool value) const;
        [[nodiscard]] auto FocusVisualMargin() const;
        auto FocusVisualMargin(winrt::Microsoft::UI::Xaml::Thickness const& value) const;
        [[nodiscard]] auto FocusVisualSecondaryThickness() const;
        auto FocusVisualSecondaryThickness(winrt::Microsoft::UI::Xaml::Thickness const& value) const;
        [[nodiscard]] auto FocusVisualPrimaryThickness() const;
        auto FocusVisualPrimaryThickness(winrt::Microsoft::UI::Xaml::Thickness const& value) const;
        [[nodiscard]] auto FocusVisualSecondaryBrush() const;
        auto FocusVisualSecondaryBrush(winrt::Microsoft::UI::Xaml::Media::Brush const& value) const;
        [[nodiscard]] auto FocusVisualPrimaryBrush() const;
        auto FocusVisualPrimaryBrush(winrt::Microsoft::UI::Xaml::Media::Brush const& value) const;
        [[nodiscard]] auto AllowFocusWhenDisabled() const;
        auto AllowFocusWhenDisabled(bool value) const;
        [[nodiscard]] auto Style() const;
        auto Style(winrt::Microsoft::UI::Xaml::Style const& value) const;
        [[nodiscard]] auto Parent() const;
        [[nodiscard]] auto FlowDirection() const;
        auto FlowDirection(winrt::Microsoft::UI::Xaml::FlowDirection const& value) const;
        [[nodiscard]] auto RequestedTheme() const;
        auto RequestedTheme(winrt::Microsoft::UI::Xaml::ElementTheme const& value) const;
        [[nodiscard]] auto IsLoaded() const;
        [[nodiscard]] auto ActualTheme() const;
        auto Loaded(winrt::Microsoft::UI::Xaml::RoutedEventHandler const& handler) const;
        using Loaded_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IFrameworkElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IFrameworkElement>::remove_Loaded>;
        [[nodiscard]] auto Loaded(auto_revoke_t, winrt::Microsoft::UI::Xaml::RoutedEventHandler const& handler) const;
        auto Loaded(winrt::event_token const& token) const noexcept;
        auto Unloaded(winrt::Microsoft::UI::Xaml::RoutedEventHandler const& handler) const;
        using Unloaded_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IFrameworkElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IFrameworkElement>::remove_Unloaded>;
        [[nodiscard]] auto Unloaded(auto_revoke_t, winrt::Microsoft::UI::Xaml::RoutedEventHandler const& handler) const;
        auto Unloaded(winrt::event_token const& token) const noexcept;
        auto DataContextChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::FrameworkElement, winrt::Microsoft::UI::Xaml::DataContextChangedEventArgs> const& handler) const;
        using DataContextChanged_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IFrameworkElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IFrameworkElement>::remove_DataContextChanged>;
        [[nodiscard]] auto DataContextChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::FrameworkElement, winrt::Microsoft::UI::Xaml::DataContextChangedEventArgs> const& handler) const;
        auto DataContextChanged(winrt::event_token const& token) const noexcept;
        auto SizeChanged(winrt::Microsoft::UI::Xaml::SizeChangedEventHandler const& handler) const;
        using SizeChanged_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IFrameworkElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IFrameworkElement>::remove_SizeChanged>;
        [[nodiscard]] auto SizeChanged(auto_revoke_t, winrt::Microsoft::UI::Xaml::SizeChangedEventHandler const& handler) const;
        auto SizeChanged(winrt::event_token const& token) const noexcept;
        auto LayoutUpdated(winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        using LayoutUpdated_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IFrameworkElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IFrameworkElement>::remove_LayoutUpdated>;
        [[nodiscard]] auto LayoutUpdated(auto_revoke_t, winrt::Windows::Foundation::EventHandler<winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto LayoutUpdated(winrt::event_token const& token) const noexcept;
        auto Loading(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::FrameworkElement, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using Loading_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IFrameworkElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IFrameworkElement>::remove_Loading>;
        [[nodiscard]] auto Loading(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::FrameworkElement, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto Loading(winrt::event_token const& token) const noexcept;
        auto ActualThemeChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::FrameworkElement, winrt::Windows::Foundation::IInspectable> const& handler) const;
        using ActualThemeChanged_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IFrameworkElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IFrameworkElement>::remove_ActualThemeChanged>;
        [[nodiscard]] auto ActualThemeChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::FrameworkElement, winrt::Windows::Foundation::IInspectable> const& handler) const;
        auto ActualThemeChanged(winrt::event_token const& token) const noexcept;
        auto EffectiveViewportChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::FrameworkElement, winrt::Microsoft::UI::Xaml::EffectiveViewportChangedEventArgs> const& handler) const;
        using EffectiveViewportChanged_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IFrameworkElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IFrameworkElement>::remove_EffectiveViewportChanged>;
        [[nodiscard]] auto EffectiveViewportChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::FrameworkElement, winrt::Microsoft::UI::Xaml::EffectiveViewportChangedEventArgs> const& handler) const;
        auto EffectiveViewportChanged(winrt::event_token const& token) const noexcept;
        auto FindName(param::hstring const& name) const;
        auto SetBinding(winrt::Microsoft::UI::Xaml::DependencyProperty const& dp, winrt::Microsoft::UI::Xaml::Data::BindingBase const& binding) const;
        auto GetBindingExpression(winrt::Microsoft::UI::Xaml::DependencyProperty const& dp) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IFrameworkElement>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IFrameworkElement<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IFrameworkElementFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IFrameworkElementFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IFrameworkElementFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IFrameworkElementOverrides
    {
        auto MeasureOverride(winrt::Windows::Foundation::Size const& availableSize) const;
        auto ArrangeOverride(winrt::Windows::Foundation::Size const& finalSize) const;
        auto OnApplyTemplate() const;
        auto GoToElementStateCore(param::hstring const& stateName, bool useTransitions) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IFrameworkElementOverrides>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IFrameworkElementOverrides<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IFrameworkElementProtected
    {
        auto InvalidateViewport() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IFrameworkElementProtected>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IFrameworkElementProtected<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IFrameworkElementStatics
    {
        [[nodiscard]] auto TagProperty() const;
        [[nodiscard]] auto LanguageProperty() const;
        [[nodiscard]] auto ActualWidthProperty() const;
        [[nodiscard]] auto ActualHeightProperty() const;
        [[nodiscard]] auto WidthProperty() const;
        [[nodiscard]] auto HeightProperty() const;
        [[nodiscard]] auto MinWidthProperty() const;
        [[nodiscard]] auto MaxWidthProperty() const;
        [[nodiscard]] auto MinHeightProperty() const;
        [[nodiscard]] auto MaxHeightProperty() const;
        [[nodiscard]] auto HorizontalAlignmentProperty() const;
        [[nodiscard]] auto VerticalAlignmentProperty() const;
        [[nodiscard]] auto MarginProperty() const;
        [[nodiscard]] auto NameProperty() const;
        [[nodiscard]] auto DataContextProperty() const;
        [[nodiscard]] auto AllowFocusOnInteractionProperty() const;
        [[nodiscard]] auto FocusVisualMarginProperty() const;
        [[nodiscard]] auto FocusVisualSecondaryThicknessProperty() const;
        [[nodiscard]] auto FocusVisualPrimaryThicknessProperty() const;
        [[nodiscard]] auto FocusVisualSecondaryBrushProperty() const;
        [[nodiscard]] auto FocusVisualPrimaryBrushProperty() const;
        [[nodiscard]] auto AllowFocusWhenDisabledProperty() const;
        [[nodiscard]] auto StyleProperty() const;
        [[nodiscard]] auto FlowDirectionProperty() const;
        [[nodiscard]] auto RequestedThemeProperty() const;
        [[nodiscard]] auto ActualThemeProperty() const;
        auto DeferTree(winrt::Microsoft::UI::Xaml::DependencyObject const& element) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IFrameworkElementStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IFrameworkElementStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IFrameworkTemplate
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IFrameworkTemplate>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IFrameworkTemplate<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IFrameworkTemplateFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IFrameworkTemplateFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IFrameworkTemplateFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IFrameworkView
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IFrameworkView>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IFrameworkView<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IFrameworkViewSource
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IFrameworkViewSource>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IFrameworkViewSource<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IGridLengthHelper
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IGridLengthHelper>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IGridLengthHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IGridLengthHelperStatics
    {
        [[nodiscard]] auto Auto() const;
        auto FromPixels(double pixels) const;
        auto FromValueAndType(double value, winrt::Microsoft::UI::Xaml::GridUnitType const& type) const;
        auto GetIsAbsolute(winrt::Microsoft::UI::Xaml::GridLength const& target) const;
        auto GetIsAuto(winrt::Microsoft::UI::Xaml::GridLength const& target) const;
        auto GetIsStar(winrt::Microsoft::UI::Xaml::GridLength const& target) const;
        auto Equals(winrt::Microsoft::UI::Xaml::GridLength const& target, winrt::Microsoft::UI::Xaml::GridLength const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IGridLengthHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IGridLengthHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ILaunchActivatedEventArgs
    {
        [[nodiscard]] auto Arguments() const;
        [[nodiscard]] auto UWPLaunchActivatedEventArgs() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ILaunchActivatedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ILaunchActivatedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IMediaFailedRoutedEventArgs
    {
        [[nodiscard]] auto ErrorTrace() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IMediaFailedRoutedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IMediaFailedRoutedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IPointHelper
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IPointHelper>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IPointHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IPointHelperStatics
    {
        auto FromCoordinates(float x, float y) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IPointHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IPointHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IPropertyMetadata
    {
        [[nodiscard]] auto DefaultValue() const;
        [[nodiscard]] auto CreateDefaultValueCallback() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IPropertyMetadata>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IPropertyMetadata<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IPropertyMetadataFactory
    {
        auto CreateInstanceWithDefaultValue(winrt::Windows::Foundation::IInspectable const& defaultValue, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
        auto CreateInstanceWithDefaultValueAndCallback(winrt::Windows::Foundation::IInspectable const& defaultValue, winrt::Microsoft::UI::Xaml::PropertyChangedCallback const& propertyChangedCallback, winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IPropertyMetadataFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IPropertyMetadataFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IPropertyMetadataStatics
    {
        auto Create(winrt::Windows::Foundation::IInspectable const& defaultValue) const;
        auto Create(winrt::Windows::Foundation::IInspectable const& defaultValue, winrt::Microsoft::UI::Xaml::PropertyChangedCallback const& propertyChangedCallback) const;
        auto Create(winrt::Microsoft::UI::Xaml::CreateDefaultValueCallback const& createDefaultValueCallback) const;
        auto Create(winrt::Microsoft::UI::Xaml::CreateDefaultValueCallback const& createDefaultValueCallback, winrt::Microsoft::UI::Xaml::PropertyChangedCallback const& propertyChangedCallback) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IPropertyMetadataStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IPropertyMetadataStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IPropertyPath
    {
        [[nodiscard]] auto Path() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IPropertyPath>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IPropertyPath<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IPropertyPathFactory
    {
        auto CreateInstance(param::hstring const& path) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IPropertyPathFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IPropertyPathFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IRectHelper
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IRectHelper>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IRectHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IRectHelperStatics
    {
        [[nodiscard]] auto Empty() const;
        auto FromCoordinatesAndDimensions(float x, float y, float width, float height) const;
        auto FromPoints(winrt::Windows::Foundation::Point const& point1, winrt::Windows::Foundation::Point const& point2) const;
        auto FromLocationAndSize(winrt::Windows::Foundation::Point const& location, winrt::Windows::Foundation::Size const& size) const;
        auto GetIsEmpty(winrt::Windows::Foundation::Rect const& target) const;
        auto GetBottom(winrt::Windows::Foundation::Rect const& target) const;
        auto GetLeft(winrt::Windows::Foundation::Rect const& target) const;
        auto GetRight(winrt::Windows::Foundation::Rect const& target) const;
        auto GetTop(winrt::Windows::Foundation::Rect const& target) const;
        auto Contains(winrt::Windows::Foundation::Rect const& target, winrt::Windows::Foundation::Point const& point) const;
        auto Equals(winrt::Windows::Foundation::Rect const& target, winrt::Windows::Foundation::Rect const& value) const;
        auto Intersect(winrt::Windows::Foundation::Rect const& target, winrt::Windows::Foundation::Rect const& rect) const;
        auto Union(winrt::Windows::Foundation::Rect const& target, winrt::Windows::Foundation::Point const& point) const;
        auto Union(winrt::Windows::Foundation::Rect const& target, winrt::Windows::Foundation::Rect const& rect) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IRectHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IRectHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IResourceDictionary
    {
        [[nodiscard]] auto Source() const;
        auto Source(winrt::Windows::Foundation::Uri const& value) const;
        [[nodiscard]] auto MergedDictionaries() const;
        [[nodiscard]] auto ThemeDictionaries() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IResourceDictionary>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IResourceDictionary<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IResourceDictionaryFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IResourceDictionaryFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IResourceDictionaryFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IResourceManagerRequestedEventArgs
    {
        [[nodiscard]] auto CustomResourceManager() const;
        auto CustomResourceManager(winrt::Microsoft::Windows::ApplicationModel::Resources::IResourceManager const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IResourceManagerRequestedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IResourceManagerRequestedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IRoutedEvent
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IRoutedEvent>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IRoutedEvent<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IRoutedEventArgs
    {
        [[nodiscard]] auto OriginalSource() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IRoutedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IRoutedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IRoutedEventArgsFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IRoutedEventArgsFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IRoutedEventArgsFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IScalarTransition
    {
        [[nodiscard]] auto Duration() const;
        auto Duration(winrt::Windows::Foundation::TimeSpan const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IScalarTransition>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IScalarTransition<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IScalarTransitionFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IScalarTransitionFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IScalarTransitionFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ISetter
    {
        [[nodiscard]] auto Property() const;
        auto Property(winrt::Microsoft::UI::Xaml::DependencyProperty const& value) const;
        [[nodiscard]] auto Value() const;
        auto Value(winrt::Windows::Foundation::IInspectable const& value) const;
        [[nodiscard]] auto Target() const;
        auto Target(winrt::Microsoft::UI::Xaml::TargetPropertyPath const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ISetter>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ISetter<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ISetterBase
    {
        [[nodiscard]] auto IsSealed() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ISetterBase>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ISetterBase<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ISetterBaseCollection
    {
        [[nodiscard]] auto IsSealed() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ISetterBaseCollection>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ISetterBaseCollection<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ISetterBaseFactory
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ISetterBaseFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ISetterBaseFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ISetterFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::DependencyProperty const& targetProperty, winrt::Windows::Foundation::IInspectable const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ISetterFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ISetterFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ISizeChangedEventArgs
    {
        [[nodiscard]] auto PreviousSize() const;
        [[nodiscard]] auto NewSize() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ISizeChangedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ISizeChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ISizeHelper
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ISizeHelper>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ISizeHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ISizeHelperStatics
    {
        [[nodiscard]] auto Empty() const;
        auto FromDimensions(float width, float height) const;
        auto GetIsEmpty(winrt::Windows::Foundation::Size const& target) const;
        auto Equals(winrt::Windows::Foundation::Size const& target, winrt::Windows::Foundation::Size const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ISizeHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ISizeHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IStateTrigger
    {
        [[nodiscard]] auto IsActive() const;
        auto IsActive(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IStateTrigger>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IStateTrigger<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IStateTriggerBase
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IStateTriggerBase>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IStateTriggerBase<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IStateTriggerBaseFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IStateTriggerBaseFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IStateTriggerBaseFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IStateTriggerBaseProtected
    {
        auto SetActive(bool IsActive) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IStateTriggerBaseProtected>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IStateTriggerBaseProtected<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IStateTriggerStatics
    {
        [[nodiscard]] auto IsActiveProperty() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IStateTriggerStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IStateTriggerStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IStyle
    {
        [[nodiscard]] auto IsSealed() const;
        [[nodiscard]] auto Setters() const;
        [[nodiscard]] auto TargetType() const;
        auto TargetType(winrt::Windows::UI::Xaml::Interop::TypeName const& value) const;
        [[nodiscard]] auto BasedOn() const;
        auto BasedOn(winrt::Microsoft::UI::Xaml::Style const& value) const;
        auto Seal() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IStyle>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IStyle<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IStyleFactory
    {
        auto CreateInstance(winrt::Windows::UI::Xaml::Interop::TypeName const& targetType) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IStyleFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IStyleFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ITargetPropertyPath
    {
        [[nodiscard]] auto Path() const;
        auto Path(winrt::Microsoft::UI::Xaml::PropertyPath const& value) const;
        [[nodiscard]] auto Target() const;
        auto Target(winrt::Windows::Foundation::IInspectable const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ITargetPropertyPath>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ITargetPropertyPath<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ITargetPropertyPathFactory
    {
        auto CreateInstance(winrt::Microsoft::UI::Xaml::DependencyProperty const& targetProperty) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ITargetPropertyPathFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ITargetPropertyPathFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IThicknessHelper
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IThicknessHelper>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IThicknessHelper<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IThicknessHelperStatics
    {
        auto FromLengths(double left, double top, double right, double bottom) const;
        auto FromUniformLength(double uniformLength) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IThicknessHelperStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IThicknessHelperStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ITriggerAction
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ITriggerAction>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ITriggerAction<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ITriggerActionFactory
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ITriggerActionFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ITriggerActionFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ITriggerBase
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ITriggerBase>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ITriggerBase<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_ITriggerBaseFactory
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::ITriggerBaseFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_ITriggerBaseFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IUIElement
    {
        [[nodiscard]] auto DesiredSize() const;
        [[nodiscard]] auto AllowDrop() const;
        auto AllowDrop(bool value) const;
        [[nodiscard]] auto Opacity() const;
        auto Opacity(double value) const;
        [[nodiscard]] auto Clip() const;
        auto Clip(winrt::Microsoft::UI::Xaml::Media::RectangleGeometry const& value) const;
        [[nodiscard]] auto RenderTransform() const;
        auto RenderTransform(winrt::Microsoft::UI::Xaml::Media::Transform const& value) const;
        [[nodiscard]] auto Projection() const;
        auto Projection(winrt::Microsoft::UI::Xaml::Media::Projection const& value) const;
        [[nodiscard]] auto Transform3D() const;
        auto Transform3D(winrt::Microsoft::UI::Xaml::Media::Media3D::Transform3D const& value) const;
        [[nodiscard]] auto RenderTransformOrigin() const;
        auto RenderTransformOrigin(winrt::Windows::Foundation::Point const& value) const;
        [[nodiscard]] auto IsHitTestVisible() const;
        auto IsHitTestVisible(bool value) const;
        [[nodiscard]] auto Visibility() const;
        auto Visibility(winrt::Microsoft::UI::Xaml::Visibility const& value) const;
        [[nodiscard]] auto RenderSize() const;
        [[nodiscard]] auto UseLayoutRounding() const;
        auto UseLayoutRounding(bool value) const;
        [[nodiscard]] auto Transitions() const;
        auto Transitions(winrt::Microsoft::UI::Xaml::Media::Animation::TransitionCollection const& value) const;
        [[nodiscard]] auto CacheMode() const;
        auto CacheMode(winrt::Microsoft::UI::Xaml::Media::CacheMode const& value) const;
        [[nodiscard]] auto IsTapEnabled() const;
        auto IsTapEnabled(bool value) const;
        [[nodiscard]] auto IsDoubleTapEnabled() const;
        auto IsDoubleTapEnabled(bool value) const;
        [[nodiscard]] auto CanDrag() const;
        auto CanDrag(bool value) const;
        [[nodiscard]] auto IsRightTapEnabled() const;
        auto IsRightTapEnabled(bool value) const;
        [[nodiscard]] auto IsHoldingEnabled() const;
        auto IsHoldingEnabled(bool value) const;
        [[nodiscard]] auto ManipulationMode() const;
        auto ManipulationMode(winrt::Microsoft::UI::Xaml::Input::ManipulationModes const& value) const;
        [[nodiscard]] auto PointerCaptures() const;
        [[nodiscard]] auto ContextFlyout() const;
        auto ContextFlyout(winrt::Microsoft::UI::Xaml::Controls::Primitives::FlyoutBase const& value) const;
        [[nodiscard]] auto CompositeMode() const;
        auto CompositeMode(winrt::Microsoft::UI::Xaml::Media::ElementCompositeMode const& value) const;
        [[nodiscard]] auto Lights() const;
        [[nodiscard]] auto CanBeScrollAnchor() const;
        auto CanBeScrollAnchor(bool value) const;
        [[nodiscard]] auto ExitDisplayModeOnAccessKeyInvoked() const;
        auto ExitDisplayModeOnAccessKeyInvoked(bool value) const;
        [[nodiscard]] auto IsAccessKeyScope() const;
        auto IsAccessKeyScope(bool value) const;
        [[nodiscard]] auto AccessKeyScopeOwner() const;
        auto AccessKeyScopeOwner(winrt::Microsoft::UI::Xaml::DependencyObject const& value) const;
        [[nodiscard]] auto AccessKey() const;
        auto AccessKey(param::hstring const& value) const;
        [[nodiscard]] auto KeyTipPlacementMode() const;
        auto KeyTipPlacementMode(winrt::Microsoft::UI::Xaml::Input::KeyTipPlacementMode const& value) const;
        [[nodiscard]] auto KeyTipHorizontalOffset() const;
        auto KeyTipHorizontalOffset(double value) const;
        [[nodiscard]] auto KeyTipVerticalOffset() const;
        auto KeyTipVerticalOffset(double value) const;
        [[nodiscard]] auto KeyTipTarget() const;
        auto KeyTipTarget(winrt::Microsoft::UI::Xaml::DependencyObject const& value) const;
        [[nodiscard]] auto XYFocusKeyboardNavigation() const;
        auto XYFocusKeyboardNavigation(winrt::Microsoft::UI::Xaml::Input::XYFocusKeyboardNavigationMode const& value) const;
        [[nodiscard]] auto XYFocusUpNavigationStrategy() const;
        auto XYFocusUpNavigationStrategy(winrt::Microsoft::UI::Xaml::Input::XYFocusNavigationStrategy const& value) const;
        [[nodiscard]] auto XYFocusDownNavigationStrategy() const;
        auto XYFocusDownNavigationStrategy(winrt::Microsoft::UI::Xaml::Input::XYFocusNavigationStrategy const& value) const;
        [[nodiscard]] auto XYFocusLeftNavigationStrategy() const;
        auto XYFocusLeftNavigationStrategy(winrt::Microsoft::UI::Xaml::Input::XYFocusNavigationStrategy const& value) const;
        [[nodiscard]] auto XYFocusRightNavigationStrategy() const;
        auto XYFocusRightNavigationStrategy(winrt::Microsoft::UI::Xaml::Input::XYFocusNavigationStrategy const& value) const;
        [[nodiscard]] auto KeyboardAccelerators() const;
        [[nodiscard]] auto KeyboardAcceleratorPlacementTarget() const;
        auto KeyboardAcceleratorPlacementTarget(winrt::Microsoft::UI::Xaml::DependencyObject const& value) const;
        [[nodiscard]] auto KeyboardAcceleratorPlacementMode() const;
        auto KeyboardAcceleratorPlacementMode(winrt::Microsoft::UI::Xaml::Input::KeyboardAcceleratorPlacementMode const& value) const;
        [[nodiscard]] auto HighContrastAdjustment() const;
        auto HighContrastAdjustment(winrt::Microsoft::UI::Xaml::ElementHighContrastAdjustment const& value) const;
        [[nodiscard]] auto TabFocusNavigation() const;
        auto TabFocusNavigation(winrt::Microsoft::UI::Xaml::Input::KeyboardNavigationMode const& value) const;
        [[nodiscard]] auto OpacityTransition() const;
        auto OpacityTransition(winrt::Microsoft::UI::Xaml::ScalarTransition const& value) const;
        [[nodiscard]] auto Translation() const;
        auto Translation(winrt::Windows::Foundation::Numerics::float3 const& value) const;
        [[nodiscard]] auto TranslationTransition() const;
        auto TranslationTransition(winrt::Microsoft::UI::Xaml::Vector3Transition const& value) const;
        [[nodiscard]] auto Rotation() const;
        auto Rotation(float value) const;
        [[nodiscard]] auto RotationTransition() const;
        auto RotationTransition(winrt::Microsoft::UI::Xaml::ScalarTransition const& value) const;
        [[nodiscard]] auto Scale() const;
        auto Scale(winrt::Windows::Foundation::Numerics::float3 const& value) const;
        [[nodiscard]] auto ScaleTransition() const;
        auto ScaleTransition(winrt::Microsoft::UI::Xaml::Vector3Transition const& value) const;
        [[nodiscard]] auto TransformMatrix() const;
        auto TransformMatrix(winrt::Windows::Foundation::Numerics::float4x4 const& value) const;
        [[nodiscard]] auto CenterPoint() const;
        auto CenterPoint(winrt::Windows::Foundation::Numerics::float3 const& value) const;
        [[nodiscard]] auto RotationAxis() const;
        auto RotationAxis(winrt::Windows::Foundation::Numerics::float3 const& value) const;
        [[nodiscard]] auto ActualOffset() const;
        [[nodiscard]] auto ActualSize() const;
        [[nodiscard]] auto XamlRoot() const;
        auto XamlRoot(winrt::Microsoft::UI::Xaml::XamlRoot const& value) const;
        [[nodiscard]] auto Shadow() const;
        auto Shadow(winrt::Microsoft::UI::Xaml::Media::Shadow const& value) const;
        [[nodiscard]] auto RasterizationScale() const;
        auto RasterizationScale(double value) const;
        [[nodiscard]] auto FocusState() const;
        [[nodiscard]] auto UseSystemFocusVisuals() const;
        auto UseSystemFocusVisuals(bool value) const;
        [[nodiscard]] auto XYFocusLeft() const;
        auto XYFocusLeft(winrt::Microsoft::UI::Xaml::DependencyObject const& value) const;
        [[nodiscard]] auto XYFocusRight() const;
        auto XYFocusRight(winrt::Microsoft::UI::Xaml::DependencyObject const& value) const;
        [[nodiscard]] auto XYFocusUp() const;
        auto XYFocusUp(winrt::Microsoft::UI::Xaml::DependencyObject const& value) const;
        [[nodiscard]] auto XYFocusDown() const;
        auto XYFocusDown(winrt::Microsoft::UI::Xaml::DependencyObject const& value) const;
        [[nodiscard]] auto IsTabStop() const;
        auto IsTabStop(bool value) const;
        [[nodiscard]] auto TabIndex() const;
        auto TabIndex(int32_t value) const;
        auto KeyUp(winrt::Microsoft::UI::Xaml::Input::KeyEventHandler const& handler) const;
        using KeyUp_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_KeyUp>;
        [[nodiscard]] auto KeyUp(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::KeyEventHandler const& handler) const;
        auto KeyUp(winrt::event_token const& token) const noexcept;
        auto KeyDown(winrt::Microsoft::UI::Xaml::Input::KeyEventHandler const& handler) const;
        using KeyDown_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_KeyDown>;
        [[nodiscard]] auto KeyDown(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::KeyEventHandler const& handler) const;
        auto KeyDown(winrt::event_token const& token) const noexcept;
        auto GotFocus(winrt::Microsoft::UI::Xaml::RoutedEventHandler const& handler) const;
        using GotFocus_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_GotFocus>;
        [[nodiscard]] auto GotFocus(auto_revoke_t, winrt::Microsoft::UI::Xaml::RoutedEventHandler const& handler) const;
        auto GotFocus(winrt::event_token const& token) const noexcept;
        auto LostFocus(winrt::Microsoft::UI::Xaml::RoutedEventHandler const& handler) const;
        using LostFocus_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_LostFocus>;
        [[nodiscard]] auto LostFocus(auto_revoke_t, winrt::Microsoft::UI::Xaml::RoutedEventHandler const& handler) const;
        auto LostFocus(winrt::event_token const& token) const noexcept;
        auto DragStarting(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::DragStartingEventArgs> const& handler) const;
        using DragStarting_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_DragStarting>;
        [[nodiscard]] auto DragStarting(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::DragStartingEventArgs> const& handler) const;
        auto DragStarting(winrt::event_token const& token) const noexcept;
        auto DropCompleted(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::DropCompletedEventArgs> const& handler) const;
        using DropCompleted_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_DropCompleted>;
        [[nodiscard]] auto DropCompleted(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::DropCompletedEventArgs> const& handler) const;
        auto DropCompleted(winrt::event_token const& token) const noexcept;
        auto CharacterReceived(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::CharacterReceivedRoutedEventArgs> const& handler) const;
        using CharacterReceived_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_CharacterReceived>;
        [[nodiscard]] auto CharacterReceived(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::CharacterReceivedRoutedEventArgs> const& handler) const;
        auto CharacterReceived(winrt::event_token const& token) const noexcept;
        auto DragEnter(winrt::Microsoft::UI::Xaml::DragEventHandler const& handler) const;
        using DragEnter_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_DragEnter>;
        [[nodiscard]] auto DragEnter(auto_revoke_t, winrt::Microsoft::UI::Xaml::DragEventHandler const& handler) const;
        auto DragEnter(winrt::event_token const& token) const noexcept;
        auto DragLeave(winrt::Microsoft::UI::Xaml::DragEventHandler const& handler) const;
        using DragLeave_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_DragLeave>;
        [[nodiscard]] auto DragLeave(auto_revoke_t, winrt::Microsoft::UI::Xaml::DragEventHandler const& handler) const;
        auto DragLeave(winrt::event_token const& token) const noexcept;
        auto DragOver(winrt::Microsoft::UI::Xaml::DragEventHandler const& handler) const;
        using DragOver_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_DragOver>;
        [[nodiscard]] auto DragOver(auto_revoke_t, winrt::Microsoft::UI::Xaml::DragEventHandler const& handler) const;
        auto DragOver(winrt::event_token const& token) const noexcept;
        auto Drop(winrt::Microsoft::UI::Xaml::DragEventHandler const& handler) const;
        using Drop_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_Drop>;
        [[nodiscard]] auto Drop(auto_revoke_t, winrt::Microsoft::UI::Xaml::DragEventHandler const& handler) const;
        auto Drop(winrt::event_token const& token) const noexcept;
        auto PointerPressed(winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        using PointerPressed_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PointerPressed>;
        [[nodiscard]] auto PointerPressed(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        auto PointerPressed(winrt::event_token const& token) const noexcept;
        auto PointerMoved(winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        using PointerMoved_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PointerMoved>;
        [[nodiscard]] auto PointerMoved(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        auto PointerMoved(winrt::event_token const& token) const noexcept;
        auto PointerReleased(winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        using PointerReleased_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PointerReleased>;
        [[nodiscard]] auto PointerReleased(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        auto PointerReleased(winrt::event_token const& token) const noexcept;
        auto PointerEntered(winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        using PointerEntered_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PointerEntered>;
        [[nodiscard]] auto PointerEntered(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        auto PointerEntered(winrt::event_token const& token) const noexcept;
        auto PointerExited(winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        using PointerExited_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PointerExited>;
        [[nodiscard]] auto PointerExited(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        auto PointerExited(winrt::event_token const& token) const noexcept;
        auto PointerCaptureLost(winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        using PointerCaptureLost_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PointerCaptureLost>;
        [[nodiscard]] auto PointerCaptureLost(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        auto PointerCaptureLost(winrt::event_token const& token) const noexcept;
        auto PointerCanceled(winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        using PointerCanceled_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PointerCanceled>;
        [[nodiscard]] auto PointerCanceled(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        auto PointerCanceled(winrt::event_token const& token) const noexcept;
        auto PointerWheelChanged(winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        using PointerWheelChanged_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PointerWheelChanged>;
        [[nodiscard]] auto PointerWheelChanged(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::PointerEventHandler const& handler) const;
        auto PointerWheelChanged(winrt::event_token const& token) const noexcept;
        auto Tapped(winrt::Microsoft::UI::Xaml::Input::TappedEventHandler const& handler) const;
        using Tapped_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_Tapped>;
        [[nodiscard]] auto Tapped(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::TappedEventHandler const& handler) const;
        auto Tapped(winrt::event_token const& token) const noexcept;
        auto DoubleTapped(winrt::Microsoft::UI::Xaml::Input::DoubleTappedEventHandler const& handler) const;
        using DoubleTapped_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_DoubleTapped>;
        [[nodiscard]] auto DoubleTapped(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::DoubleTappedEventHandler const& handler) const;
        auto DoubleTapped(winrt::event_token const& token) const noexcept;
        auto Holding(winrt::Microsoft::UI::Xaml::Input::HoldingEventHandler const& handler) const;
        using Holding_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_Holding>;
        [[nodiscard]] auto Holding(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::HoldingEventHandler const& handler) const;
        auto Holding(winrt::event_token const& token) const noexcept;
        auto ContextRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::ContextRequestedEventArgs> const& handler) const;
        using ContextRequested_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_ContextRequested>;
        [[nodiscard]] auto ContextRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::ContextRequestedEventArgs> const& handler) const;
        auto ContextRequested(winrt::event_token const& token) const noexcept;
        auto ContextCanceled(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::RoutedEventArgs> const& handler) const;
        using ContextCanceled_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_ContextCanceled>;
        [[nodiscard]] auto ContextCanceled(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::RoutedEventArgs> const& handler) const;
        auto ContextCanceled(winrt::event_token const& token) const noexcept;
        auto RightTapped(winrt::Microsoft::UI::Xaml::Input::RightTappedEventHandler const& handler) const;
        using RightTapped_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_RightTapped>;
        [[nodiscard]] auto RightTapped(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::RightTappedEventHandler const& handler) const;
        auto RightTapped(winrt::event_token const& token) const noexcept;
        auto ManipulationStarting(winrt::Microsoft::UI::Xaml::Input::ManipulationStartingEventHandler const& handler) const;
        using ManipulationStarting_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_ManipulationStarting>;
        [[nodiscard]] auto ManipulationStarting(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::ManipulationStartingEventHandler const& handler) const;
        auto ManipulationStarting(winrt::event_token const& token) const noexcept;
        auto ManipulationInertiaStarting(winrt::Microsoft::UI::Xaml::Input::ManipulationInertiaStartingEventHandler const& handler) const;
        using ManipulationInertiaStarting_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_ManipulationInertiaStarting>;
        [[nodiscard]] auto ManipulationInertiaStarting(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::ManipulationInertiaStartingEventHandler const& handler) const;
        auto ManipulationInertiaStarting(winrt::event_token const& token) const noexcept;
        auto ManipulationStarted(winrt::Microsoft::UI::Xaml::Input::ManipulationStartedEventHandler const& handler) const;
        using ManipulationStarted_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_ManipulationStarted>;
        [[nodiscard]] auto ManipulationStarted(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::ManipulationStartedEventHandler const& handler) const;
        auto ManipulationStarted(winrt::event_token const& token) const noexcept;
        auto ManipulationDelta(winrt::Microsoft::UI::Xaml::Input::ManipulationDeltaEventHandler const& handler) const;
        using ManipulationDelta_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_ManipulationDelta>;
        [[nodiscard]] auto ManipulationDelta(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::ManipulationDeltaEventHandler const& handler) const;
        auto ManipulationDelta(winrt::event_token const& token) const noexcept;
        auto ManipulationCompleted(winrt::Microsoft::UI::Xaml::Input::ManipulationCompletedEventHandler const& handler) const;
        using ManipulationCompleted_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_ManipulationCompleted>;
        [[nodiscard]] auto ManipulationCompleted(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::ManipulationCompletedEventHandler const& handler) const;
        auto ManipulationCompleted(winrt::event_token const& token) const noexcept;
        auto AccessKeyDisplayRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::AccessKeyDisplayRequestedEventArgs> const& handler) const;
        using AccessKeyDisplayRequested_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_AccessKeyDisplayRequested>;
        [[nodiscard]] auto AccessKeyDisplayRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::AccessKeyDisplayRequestedEventArgs> const& handler) const;
        auto AccessKeyDisplayRequested(winrt::event_token const& token) const noexcept;
        auto AccessKeyDisplayDismissed(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::AccessKeyDisplayDismissedEventArgs> const& handler) const;
        using AccessKeyDisplayDismissed_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_AccessKeyDisplayDismissed>;
        [[nodiscard]] auto AccessKeyDisplayDismissed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::AccessKeyDisplayDismissedEventArgs> const& handler) const;
        auto AccessKeyDisplayDismissed(winrt::event_token const& token) const noexcept;
        auto AccessKeyInvoked(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::AccessKeyInvokedEventArgs> const& handler) const;
        using AccessKeyInvoked_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_AccessKeyInvoked>;
        [[nodiscard]] auto AccessKeyInvoked(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::AccessKeyInvokedEventArgs> const& handler) const;
        auto AccessKeyInvoked(winrt::event_token const& token) const noexcept;
        auto ProcessKeyboardAccelerators(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::ProcessKeyboardAcceleratorEventArgs> const& handler) const;
        using ProcessKeyboardAccelerators_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_ProcessKeyboardAccelerators>;
        [[nodiscard]] auto ProcessKeyboardAccelerators(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::ProcessKeyboardAcceleratorEventArgs> const& handler) const;
        auto ProcessKeyboardAccelerators(winrt::event_token const& token) const noexcept;
        auto GettingFocus(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::GettingFocusEventArgs> const& handler) const;
        using GettingFocus_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_GettingFocus>;
        [[nodiscard]] auto GettingFocus(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::GettingFocusEventArgs> const& handler) const;
        auto GettingFocus(winrt::event_token const& token) const noexcept;
        auto LosingFocus(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::LosingFocusEventArgs> const& handler) const;
        using LosingFocus_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_LosingFocus>;
        [[nodiscard]] auto LosingFocus(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::LosingFocusEventArgs> const& handler) const;
        auto LosingFocus(winrt::event_token const& token) const noexcept;
        auto NoFocusCandidateFound(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::NoFocusCandidateFoundEventArgs> const& handler) const;
        using NoFocusCandidateFound_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_NoFocusCandidateFound>;
        [[nodiscard]] auto NoFocusCandidateFound(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::Input::NoFocusCandidateFoundEventArgs> const& handler) const;
        auto NoFocusCandidateFound(winrt::event_token const& token) const noexcept;
        auto PreviewKeyDown(winrt::Microsoft::UI::Xaml::Input::KeyEventHandler const& handler) const;
        using PreviewKeyDown_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PreviewKeyDown>;
        [[nodiscard]] auto PreviewKeyDown(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::KeyEventHandler const& handler) const;
        auto PreviewKeyDown(winrt::event_token const& token) const noexcept;
        auto PreviewKeyUp(winrt::Microsoft::UI::Xaml::Input::KeyEventHandler const& handler) const;
        using PreviewKeyUp_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_PreviewKeyUp>;
        [[nodiscard]] auto PreviewKeyUp(auto_revoke_t, winrt::Microsoft::UI::Xaml::Input::KeyEventHandler const& handler) const;
        auto PreviewKeyUp(winrt::event_token const& token) const noexcept;
        auto BringIntoViewRequested(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::BringIntoViewRequestedEventArgs> const& handler) const;
        using BringIntoViewRequested_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IUIElement, &impl::abi_t<winrt::Microsoft::UI::Xaml::IUIElement>::remove_BringIntoViewRequested>;
        [[nodiscard]] auto BringIntoViewRequested(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::UIElement, winrt::Microsoft::UI::Xaml::BringIntoViewRequestedEventArgs> const& handler) const;
        auto BringIntoViewRequested(winrt::event_token const& token) const noexcept;
        auto Measure(winrt::Windows::Foundation::Size const& availableSize) const;
        auto Arrange(winrt::Windows::Foundation::Rect const& finalRect) const;
        auto CapturePointer(winrt::Microsoft::UI::Xaml::Input::Pointer const& value) const;
        auto ReleasePointerCapture(winrt::Microsoft::UI::Xaml::Input::Pointer const& value) const;
        auto ReleasePointerCaptures() const;
        auto AddHandler(winrt::Microsoft::UI::Xaml::RoutedEvent const& routedEvent, winrt::Windows::Foundation::IInspectable const& handler, bool handledEventsToo) const;
        auto RemoveHandler(winrt::Microsoft::UI::Xaml::RoutedEvent const& routedEvent, winrt::Windows::Foundation::IInspectable const& handler) const;
        auto TransformToVisual(winrt::Microsoft::UI::Xaml::UIElement const& visual) const;
        auto InvalidateMeasure() const;
        auto InvalidateArrange() const;
        auto UpdateLayout() const;
        auto CancelDirectManipulations() const;
        auto StartDragAsync(winrt::Microsoft::UI::Input::PointerPoint const& pointerPoint) const;
        auto StartBringIntoView() const;
        auto StartBringIntoView(winrt::Microsoft::UI::Xaml::BringIntoViewOptions const& options) const;
        auto TryInvokeKeyboardAccelerator(winrt::Microsoft::UI::Xaml::Input::ProcessKeyboardAcceleratorEventArgs const& args) const;
        auto Focus(winrt::Microsoft::UI::Xaml::FocusState const& value) const;
        auto StartAnimation(winrt::Microsoft::UI::Composition::ICompositionAnimationBase const& animation) const;
        auto StopAnimation(winrt::Microsoft::UI::Composition::ICompositionAnimationBase const& animation) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IUIElement>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IUIElement<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IUIElementFactory
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IUIElementFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IUIElementFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IUIElementOverrides
    {
        auto OnCreateAutomationPeer() const;
        auto OnDisconnectVisualChildren() const;
        auto FindSubElementsForTouchTargeting(winrt::Windows::Foundation::Point const& point, winrt::Windows::Foundation::Rect const& boundingRect) const;
        auto GetChildrenInTabFocusOrder() const;
        auto OnKeyboardAcceleratorInvoked(winrt::Microsoft::UI::Xaml::Input::KeyboardAcceleratorInvokedEventArgs const& args) const;
        auto OnProcessKeyboardAccelerators(winrt::Microsoft::UI::Xaml::Input::ProcessKeyboardAcceleratorEventArgs const& args) const;
        auto OnBringIntoViewRequested(winrt::Microsoft::UI::Xaml::BringIntoViewRequestedEventArgs const& e) const;
        auto PopulatePropertyInfoOverride(param::hstring const& propertyName, winrt::Microsoft::UI::Composition::AnimationPropertyInfo const& animationPropertyInfo) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IUIElementOverrides>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IUIElementOverrides<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IUIElementProtected
    {
        [[nodiscard]] auto ProtectedCursor() const;
        auto ProtectedCursor(winrt::Microsoft::UI::Input::InputCursor const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IUIElementProtected>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IUIElementProtected<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IUIElementStatics
    {
        [[nodiscard]] auto KeyDownEvent() const;
        [[nodiscard]] auto KeyUpEvent() const;
        [[nodiscard]] auto PointerEnteredEvent() const;
        [[nodiscard]] auto PointerPressedEvent() const;
        [[nodiscard]] auto PointerMovedEvent() const;
        [[nodiscard]] auto PointerReleasedEvent() const;
        [[nodiscard]] auto PointerExitedEvent() const;
        [[nodiscard]] auto PointerCaptureLostEvent() const;
        [[nodiscard]] auto PointerCanceledEvent() const;
        [[nodiscard]] auto PointerWheelChangedEvent() const;
        [[nodiscard]] auto TappedEvent() const;
        [[nodiscard]] auto DoubleTappedEvent() const;
        [[nodiscard]] auto HoldingEvent() const;
        [[nodiscard]] auto RightTappedEvent() const;
        [[nodiscard]] auto ManipulationStartingEvent() const;
        [[nodiscard]] auto ManipulationInertiaStartingEvent() const;
        [[nodiscard]] auto ManipulationStartedEvent() const;
        [[nodiscard]] auto ManipulationDeltaEvent() const;
        [[nodiscard]] auto ManipulationCompletedEvent() const;
        [[nodiscard]] auto DragEnterEvent() const;
        [[nodiscard]] auto DragLeaveEvent() const;
        [[nodiscard]] auto DragOverEvent() const;
        [[nodiscard]] auto DropEvent() const;
        [[nodiscard]] auto GettingFocusEvent() const;
        [[nodiscard]] auto LosingFocusEvent() const;
        [[nodiscard]] auto NoFocusCandidateFoundEvent() const;
        [[nodiscard]] auto PreviewKeyDownEvent() const;
        [[nodiscard]] auto CharacterReceivedEvent() const;
        [[nodiscard]] auto PreviewKeyUpEvent() const;
        [[nodiscard]] auto BringIntoViewRequestedEvent() const;
        [[nodiscard]] auto ContextRequestedEvent() const;
        [[nodiscard]] auto AllowDropProperty() const;
        [[nodiscard]] auto OpacityProperty() const;
        [[nodiscard]] auto ClipProperty() const;
        [[nodiscard]] auto RenderTransformProperty() const;
        [[nodiscard]] auto ProjectionProperty() const;
        [[nodiscard]] auto Transform3DProperty() const;
        [[nodiscard]] auto RenderTransformOriginProperty() const;
        [[nodiscard]] auto IsHitTestVisibleProperty() const;
        [[nodiscard]] auto VisibilityProperty() const;
        [[nodiscard]] auto UseLayoutRoundingProperty() const;
        [[nodiscard]] auto TransitionsProperty() const;
        [[nodiscard]] auto CacheModeProperty() const;
        [[nodiscard]] auto IsTapEnabledProperty() const;
        [[nodiscard]] auto IsDoubleTapEnabledProperty() const;
        [[nodiscard]] auto CanDragProperty() const;
        [[nodiscard]] auto IsRightTapEnabledProperty() const;
        [[nodiscard]] auto IsHoldingEnabledProperty() const;
        [[nodiscard]] auto ManipulationModeProperty() const;
        [[nodiscard]] auto PointerCapturesProperty() const;
        [[nodiscard]] auto ContextFlyoutProperty() const;
        [[nodiscard]] auto CompositeModeProperty() const;
        [[nodiscard]] auto LightsProperty() const;
        [[nodiscard]] auto CanBeScrollAnchorProperty() const;
        [[nodiscard]] auto ExitDisplayModeOnAccessKeyInvokedProperty() const;
        [[nodiscard]] auto IsAccessKeyScopeProperty() const;
        [[nodiscard]] auto AccessKeyScopeOwnerProperty() const;
        [[nodiscard]] auto AccessKeyProperty() const;
        [[nodiscard]] auto KeyTipPlacementModeProperty() const;
        [[nodiscard]] auto KeyTipHorizontalOffsetProperty() const;
        [[nodiscard]] auto KeyTipVerticalOffsetProperty() const;
        [[nodiscard]] auto KeyTipTargetProperty() const;
        [[nodiscard]] auto XYFocusKeyboardNavigationProperty() const;
        [[nodiscard]] auto XYFocusUpNavigationStrategyProperty() const;
        [[nodiscard]] auto XYFocusDownNavigationStrategyProperty() const;
        [[nodiscard]] auto XYFocusLeftNavigationStrategyProperty() const;
        [[nodiscard]] auto XYFocusRightNavigationStrategyProperty() const;
        [[nodiscard]] auto KeyboardAcceleratorPlacementTargetProperty() const;
        [[nodiscard]] auto KeyboardAcceleratorPlacementModeProperty() const;
        [[nodiscard]] auto HighContrastAdjustmentProperty() const;
        [[nodiscard]] auto TabFocusNavigationProperty() const;
        [[nodiscard]] auto ShadowProperty() const;
        [[nodiscard]] auto FocusStateProperty() const;
        [[nodiscard]] auto UseSystemFocusVisualsProperty() const;
        [[nodiscard]] auto XYFocusLeftProperty() const;
        [[nodiscard]] auto XYFocusRightProperty() const;
        [[nodiscard]] auto XYFocusUpProperty() const;
        [[nodiscard]] auto XYFocusDownProperty() const;
        [[nodiscard]] auto IsTabStopProperty() const;
        [[nodiscard]] auto TabIndexProperty() const;
        auto TryStartDirectManipulation(winrt::Microsoft::UI::Xaml::Input::Pointer const& value) const;
        auto RegisterAsScrollPort(winrt::Microsoft::UI::Xaml::UIElement const& element) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IUIElementStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IUIElementStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IUIElementWeakCollectionFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IUIElementWeakCollectionFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IUIElementWeakCollectionFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IUnhandledExceptionEventArgs
    {
        [[nodiscard]] auto Exception() const;
        [[nodiscard]] auto Message() const;
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IUnhandledExceptionEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IUnhandledExceptionEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVector3Transition
    {
        [[nodiscard]] auto Duration() const;
        auto Duration(winrt::Windows::Foundation::TimeSpan const& value) const;
        [[nodiscard]] auto Components() const;
        auto Components(winrt::Microsoft::UI::Xaml::Vector3TransitionComponents const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVector3Transition>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVector3Transition<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVector3TransitionFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVector3TransitionFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVector3TransitionFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualState
    {
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto Storyboard() const;
        auto Storyboard(winrt::Microsoft::UI::Xaml::Media::Animation::Storyboard const& value) const;
        [[nodiscard]] auto Setters() const;
        [[nodiscard]] auto StateTriggers() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualState>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualState<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualStateChangedEventArgs
    {
        [[nodiscard]] auto OldState() const;
        auto OldState(winrt::Microsoft::UI::Xaml::VisualState const& value) const;
        [[nodiscard]] auto NewState() const;
        auto NewState(winrt::Microsoft::UI::Xaml::VisualState const& value) const;
        [[nodiscard]] auto Control() const;
        auto Control(winrt::Microsoft::UI::Xaml::Controls::Control const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualStateChangedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualStateChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualStateGroup
    {
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto Transitions() const;
        [[nodiscard]] auto States() const;
        [[nodiscard]] auto CurrentState() const;
        auto CurrentStateChanged(winrt::Microsoft::UI::Xaml::VisualStateChangedEventHandler const& handler) const;
        using CurrentStateChanged_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IVisualStateGroup, &impl::abi_t<winrt::Microsoft::UI::Xaml::IVisualStateGroup>::remove_CurrentStateChanged>;
        [[nodiscard]] auto CurrentStateChanged(auto_revoke_t, winrt::Microsoft::UI::Xaml::VisualStateChangedEventHandler const& handler) const;
        auto CurrentStateChanged(winrt::event_token const& token) const noexcept;
        auto CurrentStateChanging(winrt::Microsoft::UI::Xaml::VisualStateChangedEventHandler const& handler) const;
        using CurrentStateChanging_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IVisualStateGroup, &impl::abi_t<winrt::Microsoft::UI::Xaml::IVisualStateGroup>::remove_CurrentStateChanging>;
        [[nodiscard]] auto CurrentStateChanging(auto_revoke_t, winrt::Microsoft::UI::Xaml::VisualStateChangedEventHandler const& handler) const;
        auto CurrentStateChanging(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualStateGroup>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualStateGroup<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualStateManager
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualStateManager>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualStateManager<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualStateManagerFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualStateManagerFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualStateManagerFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualStateManagerOverrides
    {
        auto GoToStateCore(winrt::Microsoft::UI::Xaml::Controls::Control const& control, winrt::Microsoft::UI::Xaml::FrameworkElement const& templateRoot, param::hstring const& stateName, winrt::Microsoft::UI::Xaml::VisualStateGroup const& group, winrt::Microsoft::UI::Xaml::VisualState const& state, bool useTransitions) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualStateManagerOverrides>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualStateManagerOverrides<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualStateManagerProtected
    {
        auto RaiseCurrentStateChanging(winrt::Microsoft::UI::Xaml::VisualStateGroup const& stateGroup, winrt::Microsoft::UI::Xaml::VisualState const& oldState, winrt::Microsoft::UI::Xaml::VisualState const& newState, winrt::Microsoft::UI::Xaml::Controls::Control const& control) const;
        auto RaiseCurrentStateChanged(winrt::Microsoft::UI::Xaml::VisualStateGroup const& stateGroup, winrt::Microsoft::UI::Xaml::VisualState const& oldState, winrt::Microsoft::UI::Xaml::VisualState const& newState, winrt::Microsoft::UI::Xaml::Controls::Control const& control) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualStateManagerProtected>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualStateManagerProtected<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualStateManagerStatics
    {
        auto GetVisualStateGroups(winrt::Microsoft::UI::Xaml::FrameworkElement const& obj) const;
        [[nodiscard]] auto CustomVisualStateManagerProperty() const;
        auto GetCustomVisualStateManager(winrt::Microsoft::UI::Xaml::FrameworkElement const& obj) const;
        auto SetCustomVisualStateManager(winrt::Microsoft::UI::Xaml::FrameworkElement const& obj, winrt::Microsoft::UI::Xaml::VisualStateManager const& value) const;
        auto GoToState(winrt::Microsoft::UI::Xaml::Controls::Control const& control, param::hstring const& stateName, bool useTransitions) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualStateManagerStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualStateManagerStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualTransition
    {
        [[nodiscard]] auto GeneratedDuration() const;
        auto GeneratedDuration(winrt::Microsoft::UI::Xaml::Duration const& value) const;
        [[nodiscard]] auto GeneratedEasingFunction() const;
        auto GeneratedEasingFunction(winrt::Microsoft::UI::Xaml::Media::Animation::EasingFunctionBase const& value) const;
        [[nodiscard]] auto To() const;
        auto To(param::hstring const& value) const;
        [[nodiscard]] auto From() const;
        auto From(param::hstring const& value) const;
        [[nodiscard]] auto Storyboard() const;
        auto Storyboard(winrt::Microsoft::UI::Xaml::Media::Animation::Storyboard const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualTransition>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualTransition<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IVisualTransitionFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IVisualTransitionFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IVisualTransitionFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IWindow
    {
        [[nodiscard]] auto Bounds() const;
        [[nodiscard]] auto Visible() const;
        [[nodiscard]] auto Content() const;
        auto Content(winrt::Microsoft::UI::Xaml::UIElement const& value) const;
        [[nodiscard]] auto CoreWindow() const;
        [[nodiscard]] auto Compositor() const;
        [[nodiscard]] auto Dispatcher() const;
        [[nodiscard]] auto DispatcherQueue() const;
        [[nodiscard]] auto Title() const;
        auto Title(param::hstring const& value) const;
        [[nodiscard]] auto ExtendsContentIntoTitleBar() const;
        auto ExtendsContentIntoTitleBar(bool value) const;
        auto Activated(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::WindowActivatedEventArgs> const& handler) const;
        using Activated_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IWindow, &impl::abi_t<winrt::Microsoft::UI::Xaml::IWindow>::remove_Activated>;
        [[nodiscard]] auto Activated(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::WindowActivatedEventArgs> const& handler) const;
        auto Activated(winrt::event_token const& token) const noexcept;
        auto Closed(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::WindowEventArgs> const& handler) const;
        using Closed_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IWindow, &impl::abi_t<winrt::Microsoft::UI::Xaml::IWindow>::remove_Closed>;
        [[nodiscard]] auto Closed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::WindowEventArgs> const& handler) const;
        auto Closed(winrt::event_token const& token) const noexcept;
        auto SizeChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::WindowSizeChangedEventArgs> const& handler) const;
        using SizeChanged_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IWindow, &impl::abi_t<winrt::Microsoft::UI::Xaml::IWindow>::remove_SizeChanged>;
        [[nodiscard]] auto SizeChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::WindowSizeChangedEventArgs> const& handler) const;
        auto SizeChanged(winrt::event_token const& token) const noexcept;
        auto VisibilityChanged(winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::WindowVisibilityChangedEventArgs> const& handler) const;
        using VisibilityChanged_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IWindow, &impl::abi_t<winrt::Microsoft::UI::Xaml::IWindow>::remove_VisibilityChanged>;
        [[nodiscard]] auto VisibilityChanged(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Windows::Foundation::IInspectable, winrt::Microsoft::UI::Xaml::WindowVisibilityChangedEventArgs> const& handler) const;
        auto VisibilityChanged(winrt::event_token const& token) const noexcept;
        auto Activate() const;
        auto Close() const;
        auto SetTitleBar(winrt::Microsoft::UI::Xaml::UIElement const& titleBar) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IWindow>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IWindow<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IWindow2
    {
        [[nodiscard]] auto SystemBackdrop() const;
        auto SystemBackdrop(winrt::Microsoft::UI::Xaml::Media::SystemBackdrop const& value) const;
        [[nodiscard]] auto AppWindow() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IWindow2>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IWindow2<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IWindowActivatedEventArgs
    {
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
        [[nodiscard]] auto WindowActivationState() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IWindowActivatedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IWindowActivatedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IWindowEventArgs
    {
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IWindowEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IWindowEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IWindowFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IWindowFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IWindowFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IWindowSizeChangedEventArgs
    {
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
        [[nodiscard]] auto Size() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IWindowSizeChangedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IWindowSizeChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IWindowStatics
    {
        [[nodiscard]] auto Current() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IWindowStatics>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IWindowStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IWindowVisibilityChangedEventArgs
    {
        [[nodiscard]] auto Handled() const;
        auto Handled(bool value) const;
        [[nodiscard]] auto Visible() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IWindowVisibilityChangedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IWindowVisibilityChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IXamlIsland
    {
        [[nodiscard]] auto Content() const;
        auto Content(winrt::Microsoft::UI::Xaml::UIElement const& value) const;
        [[nodiscard]] auto ContentIsland() const;
        [[nodiscard]] auto SystemBackdrop() const;
        auto SystemBackdrop(winrt::Microsoft::UI::Xaml::Media::SystemBackdrop const& value) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IXamlIsland>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IXamlIsland<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IXamlIslandFactory
    {
        auto CreateInstance(winrt::Windows::Foundation::IInspectable const& baseInterface, winrt::Windows::Foundation::IInspectable& innerInterface) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IXamlIslandFactory>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IXamlIslandFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IXamlResourceReferenceFailedEventArgs
    {
        [[nodiscard]] auto Message() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IXamlResourceReferenceFailedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IXamlResourceReferenceFailedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IXamlRoot
    {
        [[nodiscard]] auto Content() const;
        [[nodiscard]] auto Size() const;
        [[nodiscard]] auto RasterizationScale() const;
        [[nodiscard]] auto IsHostVisible() const;
        auto Changed(winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::XamlRoot, winrt::Microsoft::UI::Xaml::XamlRootChangedEventArgs> const& handler) const;
        using Changed_revoker = impl::event_revoker<winrt::Microsoft::UI::Xaml::IXamlRoot, &impl::abi_t<winrt::Microsoft::UI::Xaml::IXamlRoot>::remove_Changed>;
        [[nodiscard]] auto Changed(auto_revoke_t, winrt::Windows::Foundation::TypedEventHandler<winrt::Microsoft::UI::Xaml::XamlRoot, winrt::Microsoft::UI::Xaml::XamlRootChangedEventArgs> const& handler) const;
        auto Changed(winrt::event_token const& token) const noexcept;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IXamlRoot>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IXamlRoot<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IXamlRoot2
    {
        [[nodiscard]] auto ContentIslandEnvironment() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IXamlRoot2>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IXamlRoot2<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IXamlRoot3
    {
        [[nodiscard]] auto CoordinateConverter() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IXamlRoot3>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IXamlRoot3<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IXamlRoot4
    {
        [[nodiscard]] auto ContentIsland() const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IXamlRoot4>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IXamlRoot4<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IXamlRootChangedEventArgs
    {
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IXamlRootChangedEventArgs>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IXamlRootChangedEventArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_UI_Xaml_IXamlServiceProvider
    {
        auto GetService(winrt::Windows::UI::Xaml::Interop::TypeName const& type) const;
    };
    template <> struct consume<winrt::Microsoft::UI::Xaml::IXamlServiceProvider>
    {
        template <typename D> using type = consume_Microsoft_UI_Xaml_IXamlServiceProvider<D>;
    };
    struct struct_Microsoft_UI_Xaml_CornerRadius
    {
        double TopLeft;
        double TopRight;
        double BottomRight;
        double BottomLeft;
    };
    template <> struct abi<Microsoft::UI::Xaml::CornerRadius>
    {
        using type = struct_Microsoft_UI_Xaml_CornerRadius;
    };
    struct struct_Microsoft_UI_Xaml_Duration
    {
        int64_t TimeSpan;
        int32_t Type;
    };
    template <> struct abi<Microsoft::UI::Xaml::Duration>
    {
        using type = struct_Microsoft_UI_Xaml_Duration;
    };
    struct struct_Microsoft_UI_Xaml_GridLength
    {
        double Value;
        int32_t GridUnitType;
    };
    template <> struct abi<Microsoft::UI::Xaml::GridLength>
    {
        using type = struct_Microsoft_UI_Xaml_GridLength;
    };
    struct struct_Microsoft_UI_Xaml_Thickness
    {
        double Left;
        double Top;
        double Right;
        double Bottom;
    };
    template <> struct abi<Microsoft::UI::Xaml::Thickness>
    {
        using type = struct_Microsoft_UI_Xaml_Thickness;
    };
}
#endif
