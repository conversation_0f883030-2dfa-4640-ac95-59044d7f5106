﻿<?xml version="1.0" encoding="utf-8"?>
<assembly manifestVersion="1.0" xmlns="urn:schemas-microsoft-com:asm.v1" xmlns:ms="http://schemas.microsoft.com/appx/manifest/foundation/windows10">
  <file name="Microsoft.UI.Xaml.dll">
    <activatableClass name="Microsoft.UI.Xaml.AdaptiveTrigger" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Application" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.AnnotationPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.AutomationAnnotation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.AutomationElementIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.AutomationProperties" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.DockPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.DragPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.DropTargetPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.ExpandCollapsePatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.GridItemPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.GridPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.MultipleViewPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AppBarAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AppBarButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AppBarToggleButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AutomationPeerAnnotation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AutoSuggestBoxAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ButtonBaseAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.CalendarDatePickerAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.CheckBoxAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ComboBoxAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ComboBoxItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ComboBoxItemDataAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.DatePickerAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FlipViewAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FlipViewItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FlipViewItemDataAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FlyoutPresenterAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.FrameworkElementAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GridViewAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GridViewHeaderItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GridViewItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GridViewItemDataAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.GroupItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.HubAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.HubSectionAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.HyperlinkButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ImageAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ItemsControlAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListBoxAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListBoxItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListBoxItemDataAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewBaseAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewBaseHeaderItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewHeaderItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ListViewItemDataAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MediaPlayerElementAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MediaTransportControlsAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MenuFlyoutItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MenuFlyoutPresenterAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PasswordBoxAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RadioButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RangeBaseAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RepeatButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RichEditBoxAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RichTextBlockAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RichTextBlockOverflowAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ScrollBarAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ScrollViewerAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SelectorAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SelectorItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SemanticZoomAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SliderAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TextBlockAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TextBoxAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ThumbAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TimePickerAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ToggleButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ToggleMenuFlyoutItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ToggleSwitchAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.RangeValuePatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.ScrollPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.SelectionItemPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.SelectionPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.SpreadsheetItemPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.StylesPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.TableItemPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.TablePatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.TogglePatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.TransformPattern2Identifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.TransformPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.ValuePatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.WindowPatternIdentifiers" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.BringIntoViewOptions" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.BrushTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.ColorDisplayNameHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.ColorPaletteResources" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AppBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AppBarButton" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AppBarElementContainer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AppBarSeparator" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AppBarToggleButton" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AutoSuggestBox" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AutoSuggestBoxQuerySubmittedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AutoSuggestBoxSuggestionChosenEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AutoSuggestBoxTextChangedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.BitmapIcon" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.BitmapIconSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Border" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Button" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.CalendarDatePicker" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.CalendarView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.CalendarViewDayItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Canvas" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.CheckBox" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ChoosingGroupHeaderContainerEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ChoosingItemContainerEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ColumnDefinition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ComboBox" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ComboBoxItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.CommandBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.CommandBarOverflowPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.CommandingContainer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ContainerContentChangingEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ContentControl" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ContentDialog" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ContentPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Control" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ControlTemplate" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.DataTemplateSelector" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.DatePicker" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.DragItemsStartingEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.DynamicOverflowItemsChangingEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.FlipView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.FlipViewItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Flyout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.FlyoutPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.FontIcon" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.FontIconSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Frame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Grid" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.GridView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.GridViewHeaderItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.GridViewItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.GroupItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.GroupStyle" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.GroupStyleSelector" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Hub" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.HubSection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.HubSectionHeaderClickEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.HyperlinkButton" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.IconElement" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.IconSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.IconSourceElement" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Image" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.InputValidationCommand" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.InputValidationContext" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.InputValidationError" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemClickEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsControl" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsPanelTemplate" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsStackPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsWrapGrid" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ListBox" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ListBoxItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ListView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ListViewBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ListViewHeaderItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ListViewItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ListViewPersistenceHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MediaPlayerElement" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MediaPlayerPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MediaTransportControls" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MediaTransportControlsHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyoutItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyoutPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyoutSeparator" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MenuFlyoutSubItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Page" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Panel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PasswordBox" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PathIcon" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PathIconSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ButtonBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CalendarPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CarouselPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.DragCompletedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.DragDeltaEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.DragStartedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.FlyoutBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.FlyoutShowOptions" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.GeneratorPositionHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.GridViewItemPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.LayoutInformation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ListViewItemPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.Popup" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.RangeBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.RepeatButton" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.Selector" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.SelectorItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.Thumb" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.TickBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ToggleButton" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RadioButton" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RelativePanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RichEditBox" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RichTextBlock" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RichTextBlockOverflow" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RowDefinition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ScrollContentPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ScrollViewer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ScrollViewerViewChangedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SelectionChangedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SemanticZoom" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SemanticZoomLocation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SemanticZoomViewChangedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Slider" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SplitView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.StackPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.StyleSelector" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SwapChainBackgroundPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SwapChainPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SymbolIcon" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SymbolIconSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TextBlock" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TextBox" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TimePicker" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ToggleMenuFlyoutItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ToggleSwitch" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ToolTip" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ToolTipService" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.UserControl" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.VariableSizedWrapGrid" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Viewbox" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.VirtualizingStackPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.WrapGrid" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.CornerRadiusHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Data.Binding" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Data.BindingBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Data.BindingOperations" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Data.CollectionViewSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Data.CurrentChangingEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Data.DataErrorsChangedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Data.ItemIndexRange" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Data.PropertyChangedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Data.RelativeSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.DataTemplate" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.DataTemplateKey" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.DependencyObject" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.DependencyObjectCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.DependencyProperty" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.DispatcherTimer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Block" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Bold" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Glyphs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Hyperlink" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Inline" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.InlineUIContainer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Italic" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.LineBreak" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Paragraph" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Run" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Span" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.TextElement" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.TextHighlighter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Typography" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Documents.Underline" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.DurationHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.DxamlCoreTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.ElementFactoryGetArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.ElementFactoryRecycleArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.ElementSoundPlayer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.EventTrigger" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.FrameworkElement" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.FrameworkElementEx" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.FrameworkTemplate" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.FrameworkView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.FrameworkViewSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.GridLengthHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Hosting.DesktopWindowXamlSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Hosting.ElementCompositionPreview" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Hosting.WindowsXamlManager" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Hosting.XamlIslandRoot" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Hosting.XamlSourceFocusNavigationRequest" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Hosting.XamlSourceFocusNavigationResult" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.AccessKeyDisplayDismissedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.AccessKeyDisplayRequestedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.AccessKeyInvokedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.AccessKeyManager" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.ContextRequestedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.DoubleTappedRoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.FindNextElementOptions" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.FocusManager" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.HoldingRoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.InputManager" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.InputScope" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.InputScopeName" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.KeyboardAccelerator" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.ManipulationCompletedRoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.ManipulationDeltaRoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.ManipulationInertiaStartingRoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.ManipulationPivot" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.ManipulationStartedRoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.ManipulationStartingRoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.RightTappedRoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.StandardUICommand" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.TappedRoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Input.XamlUICommand" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.InteractionBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Internal.LayoutTransitionElementUtilities" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Internal.SecondaryContentRelationship" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Interop.NotifyCollectionChangedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Markup.MarkupExtension" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Markup.ProvideValueTargetProperty" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Markup.XamlBinaryWriter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Markup.XamlBindingHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Markup.XamlMarkupHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Markup.XamlReader" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.AddDeleteThemeTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.BackEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.BasicConnectedAnimationConfiguration" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.BeginStoryboard" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.BounceEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.CircleEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ColorAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ColorAnimationUsingKeyFrames" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ColorKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ColorKeyFrameCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ConnectedAnimationService" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ContentThemeTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.CubicEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DirectConnectedAnimationConfiguration" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DiscreteColorKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DiscreteDoubleKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DiscreteObjectKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DiscretePointKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DoubleAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DoubleAnimationUsingKeyFrames" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DoubleKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DoubleKeyFrameCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DragItemThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DragOverThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DrillInThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DrillOutThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DropTargetItemThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.EasingColorKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.EasingDoubleKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.EasingFunctionBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.EasingPointKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.EdgeUIThemeTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ElasticEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.EntranceThemeTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ExponentialEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.FadeInThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.FadeOutThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.GravityConnectedAnimationConfiguration" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.KeySpline" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.KeyTimeHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.LinearColorKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.LinearDoubleKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.LinearPointKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.NavigationTransitionInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ObjectAnimationUsingKeyFrames" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ObjectKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ObjectKeyFrameCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PaneThemeTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointAnimationUsingKeyFrames" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointerDownThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointerUpThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PointKeyFrameCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PopInThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PopOutThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PopupThemeTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.PowerEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.QuadraticEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.QuarticEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.QuinticEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ReorderThemeTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.RepeatBehaviorHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.RepositionThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.RepositionThemeTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SineEase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplineColorKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplineDoubleKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplinePointKeyFrame" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplitCloseThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SplitOpenThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.Storyboard" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SwipeBackThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SwipeHintThemeAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ThemeAnimationBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.Timeline" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.TimelineCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.Transition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.TransitionCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.ArcSegment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.BezierSegment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.BitmapCache" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Brush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.BrushCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.CacheMode" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.CompositeTransform" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.CompositionTarget" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.DoubleCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.EllipseGeometry" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.FontFamily" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.GeneralTransform" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Geometry" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.GeometryCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.GeometryGroup" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.GradientBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.GradientStop" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.GradientStopCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.ImageBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Imaging.BitmapImage" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Imaging.BitmapSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Imaging.RenderTargetBitmap" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Imaging.SoftwareBitmapSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Imaging.SurfaceImageSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Imaging.SvgImageSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Imaging.VirtualSurfaceImageSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Imaging.WriteableBitmap" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Imaging.XamlRenderingBackgroundTask" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.LinearGradientBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.LineGeometry" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.LineSegment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.LoadedImageSurface" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Matrix3DProjection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.MatrixHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.MatrixTransform" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Media3D.CompositeTransform3D" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Media3D.Matrix3DHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Media3D.PerspectiveTransform3D" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Media3D.Transform3D" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.PathFigure" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.PathFigureCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.PathGeometry" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.PathSegmentCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.PlaneProjection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.PointCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.PolyBezierSegment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.PolyLineSegment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.PolyQuadraticBezierSegment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Projection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.QuadraticBezierSegment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.RectangleGeometry" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.RotateTransform" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.ScaleTransform" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.SkewTransform" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.SolidColorBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.SystemBackdrop" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.ThemeShadow" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.TileBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.TransformCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.TransformGroup" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.TranslateTransform" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.VisualTreeHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.XamlCompositionBrushBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.XamlLight" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Navigation.FrameNavigationOptions" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Navigation.PageStackEntry" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.PanelEx" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.PointHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Printing.AddPagesEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Printing.GetPreviewPageEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Printing.PaginateEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Printing.PrintDocument" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.PropertyMetadata" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.PropertyPath" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.RectHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.ResourceDictionary" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Resources.CustomXamlResourceLoader" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.RoutedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.ScalarTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Setter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.SetterBaseCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Shapes.Ellipse" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Shapes.Line" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Shapes.Path" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Shapes.Polygon" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Shapes.Polyline" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Shapes.Rectangle" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Shapes.Shape" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.SizeHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.StateTrigger" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.StateTriggerBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Style" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.TargetPropertyPath" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.ThicknessHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.TriggerActionCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.UIElement" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.UIElementWeakCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Vector3Transition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.VisualState" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.VisualStateChangedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.VisualStateGroup" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.VisualStateManager" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.VisualTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Window" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.WindowChrome" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.XamlIsland" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="Microsoft.UI.Xaml.Controls.dll">
    <activatableClass name="Microsoft.UI.Private.Controls.AnimatedIconTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.ButtonInteraction" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.DisplayRegionHelperTestApi" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.ItemsViewTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.LayoutsTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.MUXControlsTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.PullToRefreshHelperTestApi" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.RadioButtonsTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.RepeaterTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.ScrollPresenterTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.ScrollViewerIRefreshInfoProviderAdapter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.ScrollViewTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.SelectorBarTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.SliderInteraction" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.SpectrumBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.SplitButtonTestApi" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.SwipeTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Controls.TeachingTipTestHooks" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Media.AcrylicTestApi" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Media.MaterialHelperTestApi" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Media.RevealBorderLight" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Media.RevealBrushTestApi" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Media.RevealHoverLight" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Media.RevealTestApi" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Private.Media.XamlAmbientLight" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.AnimatedVisualPlayerAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.BreadcrumbBarItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ColorPickerSliderAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ColorSpectrumAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.DropDownButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ExpanderAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.InfoBarAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.InkCanvasAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ItemContainerAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ItemsViewAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MenuBarAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.MenuBarItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.NavigationViewAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.NavigationViewItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.NumberBoxAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PagerControlAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PersonPictureAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PipsPagerAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ProgressBarAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ProgressRingAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RadioButtonsAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RatingControlAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.RepeaterAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ScrollPresenterAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SelectorBarItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.SplitButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TabViewAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TabViewItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TeachingTipAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.ToggleSplitButtonAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TreeViewItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TreeViewItemDataAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.TreeViewListAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.WebView2AutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedIcon" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedIconSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisualPlayer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedAcceptVisualSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedBackVisualSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronDownSmallVisualSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronRightDownSmallVisualSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedChevronUpDownSmallVisualSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedFindVisualSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedGlobalNavigationButtonVisualSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnimatedVisuals.AnimatedSettingsVisualSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnnotatedScrollBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.AnnotatedScrollBarLabel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.BreadcrumbBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.BreadcrumbBarItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ColorPicker" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.CommandBarFlyout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.DropDownButton" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ElementFactory" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Expander" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.FlowLayout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.FlowLayoutState" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ImageIcon" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ImageIconSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.IndexPath" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.InfoBadge" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.InfoBadgeTemplateSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.InfoBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.InfoBarTemplateSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.InkCanvas" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemCollectionTransitionProvider" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemContainer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsRepeater" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsRepeaterScrollHost" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsSourceView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.LayoutPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.LinedFlowLayout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.LinedFlowLayoutItemCollectionTransitionProvider" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MapControl" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MapElementsLayer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MapIcon" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MenuBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MenuBarItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.MenuBarItemFlyout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NavigationView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItemBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItemHeader" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItemInvokedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewItemSeparator" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NavigationViewTemplateSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NonVirtualizingLayout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NonVirtualizingLayoutContext" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.NumberBox" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PagerControl" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PagerControlTemplateSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ParallaxView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PersonPicture" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PipsPager" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.AutoSuggestBoxHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ColorPickerSlider" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ColorSpectrum" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ColumnMajorUniformToLargestGridLayout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ComboBoxHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CommandBarFlyoutCommandBarAutomationProperties" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusFilterConverter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.CornerRadiusToThicknessConverter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.InfoBarPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.MonochromaticOverlayPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.NavigationViewItemPresenterTemplateSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.RepeatedScrollSnapPoint" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.RepeatedZoomSnapPoint" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerAddScrollVelocityRequestedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerPanRequestedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerScrollByRequestedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollControllerScrollToRequestedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ScrollSnapPoint" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.TabViewListView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.ZoomSnapPoint" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ProgressBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ProgressRing" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RadioButtons" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RadioMenuFlyoutItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RatingControl" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RatingItemFontInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RatingItemImageInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RatingItemInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RecyclePool" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RecyclingElementFactory" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RefreshContainer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RefreshVisualizer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.RevealListViewItemPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ScrollingScrollOptions" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ScrollingZoomOptions" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ScrollView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SelectionModel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SelectorBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SelectorBarItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SplitButton" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.StackLayout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.StackLayoutState" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SwipeControl" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SwipeItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.SwipeItems" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TabView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TabViewItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TabViewItemTemplateSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TeachingTip" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TeachingTipTemplateSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TextCommandBarFlyout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TitleBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TitleBarAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TitleBarTemplateSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ToggleSplitButton" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TreeView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TreeViewItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TreeViewItemTemplateSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TreeViewList" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TreeViewNode" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TwoPaneView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.UniformGridLayout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.UniformGridLayoutState" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.VirtualizingLayout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.VirtualizingLayoutContext" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.WebView2" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.XamlControlsResources" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.AcrylicBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.DesktopAcrylicBackdrop" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.MicaBackdrop" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.RadialGradientBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.RevealBackgroundBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.RevealBorderBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.RevealBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="Microsoft.UI.Xaml.Phone.dll">
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PivotAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PivotItemAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Automation.Peers.PivotItemDataAutomationPeer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.DatePickedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.DatePickerFlyout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.DatePickerFlyoutItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.DatePickerFlyoutPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ItemsPickedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.ListPickerFlyout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PickerConfirmedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PickerFlyout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Pivot" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PivotItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.PivotItemEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.JumpListItemBackgroundConverter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.JumpListItemForegroundConverter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.LoopingSelector" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.PickerFlyoutBase" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.PivotHeaderItem" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.PivotHeaderPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.Primitives.PivotPanel" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TimePickedEventArgs" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TimePickerFlyout" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Controls.TimePickerFlyoutPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.CommonNavigationTransitionInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.ContinuumNavigationTransitionInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.DrillInNavigationTransitionInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.EntranceNavigationTransitionInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.NavigationThemeTransition" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SlideNavigationTransitionInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Xaml.Media.Animation.SuppressNavigationTransitionInfo" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="WinUIEdit.dll">
    <activatableClass name="Microsoft.UI.Text.FontWeights" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Text.TextConstants" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="Microsoft.Web.WebView2.Core.dll">
    <activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2CompositionController" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2ControllerWindowReference" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2CustomSchemeRegistration" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2Environment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.Web.WebView2.Core.CoreWebView2EnvironmentOptions" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
</assembly>