// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Storage_0_H
#define WINRT_Microsoft_Windows_Storage_0_H
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct IAsyncAction;
}
WINRT_EXPORT namespace winrt::Windows::Foundation::Collections
{
    struct IPropertySet;
}
WINRT_EXPORT namespace winrt::Windows::Storage
{
    struct StorageFolder;
}
WINRT_EXPORT namespace winrt::Windows::System
{
    struct User;
}
WINRT_EXPORT namespace winrt::Microsoft::Windows::Storage
{
    enum class ApplicationDataCreateDisposition : int32_t
    {
        Always = 0,
        Existing = 1,
    };
    enum class ApplicationDataLocality : int32_t
    {
        Local = 0,
        LocalCache = 3,
        SharedLocal = 4,
        Temporary = 2,
        Machine = 1000,
    };
    struct IApplicationData;
    struct IApplicationDataContainer;
    struct IApplicationDataStatics;
    struct ApplicationData;
    struct ApplicationDataContainer;
    struct ApplicationDataContract;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::Windows::Storage::IApplicationData>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Storage::IApplicationDataContainer>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Storage::IApplicationDataStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Storage::ApplicationData>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Storage::ApplicationDataContainer>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Storage::ApplicationDataCreateDisposition>{ using type = enum_category; };
    template <> struct category<winrt::Microsoft::Windows::Storage::ApplicationDataLocality>{ using type = enum_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Storage::ApplicationData> = L"Microsoft.Windows.Storage.ApplicationData";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Storage::ApplicationDataContainer> = L"Microsoft.Windows.Storage.ApplicationDataContainer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Storage::ApplicationDataCreateDisposition> = L"Microsoft.Windows.Storage.ApplicationDataCreateDisposition";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Storage::ApplicationDataLocality> = L"Microsoft.Windows.Storage.ApplicationDataLocality";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Storage::IApplicationData> = L"Microsoft.Windows.Storage.IApplicationData";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Storage::IApplicationDataContainer> = L"Microsoft.Windows.Storage.IApplicationDataContainer";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Storage::IApplicationDataStatics> = L"Microsoft.Windows.Storage.IApplicationDataStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Storage::ApplicationDataContract> = L"Microsoft.Windows.Storage.ApplicationDataContract";
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Storage::IApplicationData>{ 0xFC073CE2,0x2F7B,0x5214,{ 0x95,0xFA,0x53,0x0A,0x3F,0x9D,0x1E,0xA5 } }; // FC073CE2-2F7B-5214-95FA-530A3F9D1EA5
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Storage::IApplicationDataContainer>{ 0xD1FA9C23,0x2E59,0x55D8,{ 0xBD,0x86,0x88,0xC2,0xFD,0xC9,0xE7,0xC9 } }; // D1FA9C23-2E59-55D8-BD86-88C2FDC9E7C9
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Storage::IApplicationDataStatics>{ 0x6A8B41F8,0x5560,0x56FB,{ 0x86,0xB0,0xD5,0x9E,0x89,0x7D,0x4D,0x95 } }; // 6A8B41F8-5560-56FB-86B0-D59E897D4D95
    template <> struct default_interface<winrt::Microsoft::Windows::Storage::ApplicationData>{ using type = winrt::Microsoft::Windows::Storage::IApplicationData; };
    template <> struct default_interface<winrt::Microsoft::Windows::Storage::ApplicationDataContainer>{ using type = winrt::Microsoft::Windows::Storage::IApplicationDataContainer; };
    template <> struct abi<winrt::Microsoft::Windows::Storage::IApplicationData>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsMachinePathSupported(bool*) noexcept = 0;
            virtual int32_t __stdcall get_LocalCachePath(void**) noexcept = 0;
            virtual int32_t __stdcall get_LocalPath(void**) noexcept = 0;
            virtual int32_t __stdcall get_MachinePath(void**) noexcept = 0;
            virtual int32_t __stdcall get_SharedLocalPath(void**) noexcept = 0;
            virtual int32_t __stdcall get_TemporaryPath(void**) noexcept = 0;
            virtual int32_t __stdcall get_LocalCacheFolder(void**) noexcept = 0;
            virtual int32_t __stdcall get_LocalFolder(void**) noexcept = 0;
            virtual int32_t __stdcall get_MachineFolder(void**) noexcept = 0;
            virtual int32_t __stdcall get_SharedLocalFolder(void**) noexcept = 0;
            virtual int32_t __stdcall get_TemporaryFolder(void**) noexcept = 0;
            virtual int32_t __stdcall get_LocalSettings(void**) noexcept = 0;
            virtual int32_t __stdcall ClearAsync(int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall ClearPublisherCacheFolderAsync(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetPublisherCachePath(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetPublisherCacheFolder(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Storage::IApplicationDataContainer>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Containers(void**) noexcept = 0;
            virtual int32_t __stdcall get_Name(void**) noexcept = 0;
            virtual int32_t __stdcall get_Locality(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_Values(void**) noexcept = 0;
            virtual int32_t __stdcall CreateContainer(void*, int32_t, void**) noexcept = 0;
            virtual int32_t __stdcall DeleteContainer(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Storage::IApplicationDataStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetDefault(void**) noexcept = 0;
            virtual int32_t __stdcall GetForUser(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetForPackageFamily(void*, void**) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_Windows_Storage_IApplicationData
    {
        [[nodiscard]] auto IsMachinePathSupported() const;
        [[nodiscard]] auto LocalCachePath() const;
        [[nodiscard]] auto LocalPath() const;
        [[nodiscard]] auto MachinePath() const;
        [[nodiscard]] auto SharedLocalPath() const;
        [[nodiscard]] auto TemporaryPath() const;
        [[nodiscard]] auto LocalCacheFolder() const;
        [[nodiscard]] auto LocalFolder() const;
        [[nodiscard]] auto MachineFolder() const;
        [[nodiscard]] auto SharedLocalFolder() const;
        [[nodiscard]] auto TemporaryFolder() const;
        [[nodiscard]] auto LocalSettings() const;
        auto ClearAsync(winrt::Microsoft::Windows::Storage::ApplicationDataLocality const& locality) const;
        auto ClearPublisherCacheFolderAsync(param::hstring const& folderName) const;
        auto GetPublisherCachePath(param::hstring const& folderName) const;
        auto GetPublisherCacheFolder(param::hstring const& folderName) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Storage::IApplicationData>
    {
        template <typename D> using type = consume_Microsoft_Windows_Storage_IApplicationData<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Storage_IApplicationDataContainer
    {
        [[nodiscard]] auto Containers() const;
        [[nodiscard]] auto Name() const;
        [[nodiscard]] auto Locality() const;
        [[nodiscard]] auto Values() const;
        auto CreateContainer(param::hstring const& name, winrt::Microsoft::Windows::Storage::ApplicationDataCreateDisposition const& disposition) const;
        auto DeleteContainer(param::hstring const& name) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Storage::IApplicationDataContainer>
    {
        template <typename D> using type = consume_Microsoft_Windows_Storage_IApplicationDataContainer<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Storage_IApplicationDataStatics
    {
        auto GetDefault() const;
        auto GetForUser(winrt::Windows::System::User const& user) const;
        auto GetForPackageFamily(param::hstring const& packageFamilyName) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Storage::IApplicationDataStatics>
    {
        template <typename D> using type = consume_Microsoft_Windows_Storage_IApplicationDataStatics<D>;
    };
}
#endif
