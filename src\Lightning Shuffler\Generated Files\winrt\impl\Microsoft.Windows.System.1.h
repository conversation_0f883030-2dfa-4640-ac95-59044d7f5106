// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_System_1_H
#define WINRT_Microsoft_Windows_System_1_H
#include "winrt/impl/Microsoft.Windows.System.0.h"
WINRT_EXPORT namespace winrt::Microsoft::Windows::System
{
    struct WINRT_IMPL_EMPTY_BASES IEnvironmentManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEnvironmentManager>
    {
        IEnvironmentManager(std::nullptr_t = nullptr) noexcept {}
        IEnvironmentManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEnvironmentManager2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEnvironmentManager2>
    {
        IEnvironmentManager2(std::nullptr_t = nullptr) noexcept {}
        IEnvironmentManager2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IEnvironmentManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IEnvironmentManagerStatics>
    {
        IEnvironmentManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IEnvironmentManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
