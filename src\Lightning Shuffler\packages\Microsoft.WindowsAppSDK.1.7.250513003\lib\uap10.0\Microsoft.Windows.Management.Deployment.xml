<doc>
  <assembly>
    <name>Microsoft.Windows.Management.Deployment</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.Management.Deployment.AddPackageOptions" />
    <member name="M:Microsoft.Windows.Management.Deployment.AddPackageOptions.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.AllowUnsigned" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.DeferRegistrationWhenPackagesAreInUse" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.DependencyPackageUris" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.DeveloperMode" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.ExpectedDigests" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.ExternalLocationUri" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.ForceAppShutdown" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.ForceTargetAppShutdown" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.ForceUpdateFromAnyVersion" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.InstallAllResources" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.IsExpectedDigestsSupported" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.IsLimitToExistingPackagesSupported" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.LimitToExistingPackages" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.OptionalPackageFamilyNames" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.OptionalPackageUris" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.RelatedPackageUris" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.RequiredContentGroupOnly" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.RetainFilesOnFailure" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.StageInPlace" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.StubPackageOption" />
    <member name="P:Microsoft.Windows.Management.Deployment.AddPackageOptions.TargetVolume" />
    <member name="T:Microsoft.Windows.Management.Deployment.EnsureReadyOptions" />
    <member name="M:Microsoft.Windows.Management.Deployment.EnsureReadyOptions.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.EnsureReadyOptions.AddPackageOptions" />
    <member name="P:Microsoft.Windows.Management.Deployment.EnsureReadyOptions.RegisterNewerIfAvailable" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageDeploymentFeature" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentFeature.IsPackageReadyOrNewerAvailable" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentFeature.PackageUriScheme_ms_uup" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentFeature.ProvisionPackage_Framework" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentFeature.RemovePackageByUri" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentFeature.RepairPackage" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentFeature.ResetPackage" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageDeploymentManager" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.AddPackageAsync(System.String,Microsoft.Windows.Management.Deployment.AddPackageOptions)">
      <param name="package">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.AddPackageByUriAsync(Windows.Foundation.Uri,Microsoft.Windows.Management.Deployment.AddPackageOptions)">
      <param name="packageUri">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.AddPackageSetAsync(Microsoft.Windows.Management.Deployment.PackageSet,Microsoft.Windows.Management.Deployment.AddPackageOptions)">
      <param name="packageSet">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.DeprovisionPackageAsync(System.String)">
      <param name="package">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.DeprovisionPackageByUriAsync(Windows.Foundation.Uri)">
      <param name="packageUri">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.DeprovisionPackageSetAsync(Microsoft.Windows.Management.Deployment.PackageSet)">
      <param name="packageSet">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.EnsurePackageReadyAsync(System.String,Microsoft.Windows.Management.Deployment.EnsureReadyOptions)">
      <param name="package">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.EnsurePackageReadyByUriAsync(Windows.Foundation.Uri,Microsoft.Windows.Management.Deployment.EnsureReadyOptions)">
      <param name="packageUri">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.EnsurePackageSetReadyAsync(Microsoft.Windows.Management.Deployment.PackageSet,Microsoft.Windows.Management.Deployment.EnsureReadyOptions)">
      <param name="packageSet">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.GetDefault" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageDeploymentFeatureSupported(Microsoft.Windows.Management.Deployment.PackageDeploymentFeature)">
      <param name="feature">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageProvisioned(System.String)">
      <param name="package">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageProvisionedByUri(Windows.Foundation.Uri)">
      <param name="packageUri">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageReady(System.String)">
      <param name="package">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageReadyByUri(Windows.Foundation.Uri)">
      <param name="packageUri">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageReadyOrNewerAvailable(System.String)">
      <param name="package">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageReadyOrNewerAvailableByUri(Windows.Foundation.Uri)">
      <param name="packageUri">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageRegistrationPending(System.String)">
      <param name="packageFullName">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageRegistrationPendingForUser(System.String,System.String)">
      <param name="userSecurityId">
      </param>
      <param name="packageFullName">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageSetProvisioned(Microsoft.Windows.Management.Deployment.PackageSet)">
      <param name="packageSet">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageSetReady(Microsoft.Windows.Management.Deployment.PackageSet)">
      <param name="packageSet">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.IsPackageSetReadyOrNewerAvailable(Microsoft.Windows.Management.Deployment.PackageSet)">
      <param name="packageSet">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.ProvisionPackageAsync(System.String,Microsoft.Windows.Management.Deployment.ProvisionPackageOptions)">
      <param name="package">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.ProvisionPackageByUriAsync(Windows.Foundation.Uri,Microsoft.Windows.Management.Deployment.ProvisionPackageOptions)">
      <param name="packageUri">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.ProvisionPackageSetAsync(Microsoft.Windows.Management.Deployment.PackageSet,Microsoft.Windows.Management.Deployment.ProvisionPackageOptions)">
      <param name="packageSet">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RegisterPackageAsync(System.String,Microsoft.Windows.Management.Deployment.RegisterPackageOptions)">
      <param name="package">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RegisterPackageByUriAsync(Windows.Foundation.Uri,Microsoft.Windows.Management.Deployment.RegisterPackageOptions)">
      <param name="packageUri">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RegisterPackageSetAsync(Microsoft.Windows.Management.Deployment.PackageSet,Microsoft.Windows.Management.Deployment.RegisterPackageOptions)">
      <param name="packageSet">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RemovePackageAsync(System.String,Microsoft.Windows.Management.Deployment.RemovePackageOptions)">
      <param name="package">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RemovePackageByFamilyNameAsync(System.String,Microsoft.Windows.Management.Deployment.RemovePackageOptions)">
      <param name="packageFamilyName">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RemovePackageByFullNameAsync(System.String,Microsoft.Windows.Management.Deployment.RemovePackageOptions)">
      <param name="packageFullName">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RemovePackageByUriAsync(Windows.Foundation.Uri,Microsoft.Windows.Management.Deployment.RemovePackageOptions)">
      <param name="packageUri">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RemovePackageSetAsync(Microsoft.Windows.Management.Deployment.PackageSet,Microsoft.Windows.Management.Deployment.RemovePackageOptions)">
      <param name="packageSet">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RepairPackageAsync(System.String)">
      <param name="package">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RepairPackageByUriAsync(Windows.Foundation.Uri)">
      <param name="packageUri">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.RepairPackageSetAsync(Microsoft.Windows.Management.Deployment.PackageSet)">
      <param name="packageSet">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.ResetPackageAsync(System.String)">
      <param name="package">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.ResetPackageByUriAsync(Windows.Foundation.Uri)">
      <param name="packageUri">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.ResetPackageSetAsync(Microsoft.Windows.Management.Deployment.PackageSet)">
      <param name="packageSet">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.StagePackageAsync(System.String,Microsoft.Windows.Management.Deployment.StagePackageOptions)">
      <param name="package">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.StagePackageByUriAsync(Windows.Foundation.Uri,Microsoft.Windows.Management.Deployment.StagePackageOptions)">
      <param name="packageUri">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageDeploymentManager.StagePackageSetAsync(Microsoft.Windows.Management.Deployment.PackageSet,Microsoft.Windows.Management.Deployment.StagePackageOptions)">
      <param name="packageSet">
      </param>
      <param name="options">
      </param>
    </member>
    <member name="T:Microsoft.Windows.Management.Deployment.PackageDeploymentProgress" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentProgress.Progress" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentProgress.Status" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageDeploymentProgressStatus" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentProgressStatus.CompletedFailure" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentProgressStatus.CompletedSuccess" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentProgressStatus.InProgress" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentProgressStatus.Queued" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageDeploymentResult" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageDeploymentResult.ActivityId" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageDeploymentResult.Error" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageDeploymentResult.ErrorText" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageDeploymentResult.ExtendedError" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageDeploymentResult.Status" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageDeploymentStatus" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentStatus.CompletedFailure" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentStatus.CompletedSuccess" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageDeploymentStatus.InProgress" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageReadyOrNewerAvailableStatus" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageReadyOrNewerAvailableStatus.NewerAvailable" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageReadyOrNewerAvailableStatus.NotReady" />
    <member name="F:Microsoft.Windows.Management.Deployment.PackageReadyOrNewerAvailableStatus.Ready" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageRuntimeManager" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageRuntimeManager.AddPackageSet(Microsoft.Windows.Management.Deployment.PackageSet,Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions,Microsoft.Windows.ApplicationModel.DynamicDependency.AddPackageDependencyOptions)">
      <param name="packageSet">
      </param>
      <param name="createOptions">
      </param>
      <param name="addOptions">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageRuntimeManager.AddPackageSet(Microsoft.Windows.Management.Deployment.PackageSet)">
      <param name="packageSet">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageRuntimeManager.GetDefault" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageRuntimeManager.RemovePackageSet(Microsoft.Windows.Management.Deployment.PackageSetRuntimeDisposition)">
      <param name="packageSetRuntimeDisposition">
      </param>
    </member>
    <member name="T:Microsoft.Windows.Management.Deployment.PackageSet" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageSet.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSet.Id" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSet.Items" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSet.PackageUri" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageSetItem" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageSetItem.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetItem.Id" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetItem.MinVersion" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetItem.PackageFamilyName" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetItem.PackageUri" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetItem.ProcessorArchitectureFilter" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageSetItemRuntimeDisposition" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageSetItemRuntimeDisposition.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetItemRuntimeDisposition.PackageDependencyContextId" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetItemRuntimeDisposition.PackageDependencyId" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetItemRuntimeDisposition.PackageFullName" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetItemRuntimeDisposition.PackageSetItemId" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageSetRuntimeDisposition" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageSetRuntimeDisposition.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetRuntimeDisposition.PackageSetId" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageSetRuntimeDisposition.PackageSetItemRuntimeDispositions" />
    <member name="T:Microsoft.Windows.Management.Deployment.PackageVolume" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageVolume.FindPackageVolumeByName(System.String)">
      <param name="name">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageVolume.FindPackageVolumeByPath(System.String)">
      <param name="packageStorePath">
      </param>
    </member>
    <member name="M:Microsoft.Windows.Management.Deployment.PackageVolume.FindPackageVolumes" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageVolume.IsRepairNeeded" />
    <member name="M:Microsoft.Windows.Management.Deployment.PackageVolume.Repair" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageVolume.IsAppxInstallSupported" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageVolume.IsFullTrustPackageSupported" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageVolume.IsSystemVolume" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageVolume.MountPoint" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageVolume.Name" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageVolume.PackageStorePath" />
    <member name="P:Microsoft.Windows.Management.Deployment.PackageVolume.SupportsHardLinks" />
    <member name="T:Microsoft.Windows.Management.Deployment.ProvisionPackageOptions" />
    <member name="M:Microsoft.Windows.Management.Deployment.ProvisionPackageOptions.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.ProvisionPackageOptions.IsOptionalPackageFamilyNamesSupported" />
    <member name="P:Microsoft.Windows.Management.Deployment.ProvisionPackageOptions.IsProjectionOrderPackageFamilyNamesSupported" />
    <member name="P:Microsoft.Windows.Management.Deployment.ProvisionPackageOptions.OptionalPackageFamilyNames" />
    <member name="P:Microsoft.Windows.Management.Deployment.ProvisionPackageOptions.ProjectionOrderPackageFamilyNames" />
    <member name="T:Microsoft.Windows.Management.Deployment.RegisterPackageOptions" />
    <member name="M:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.AllowUnsigned" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.AppDataVolume" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.DeferRegistrationWhenPackagesAreInUse" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.DependencyPackageFamilyNames" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.DependencyPackageUris" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.DeveloperMode" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.ExpectedDigests" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.ExternalLocationUri" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.ForceAppShutdown" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.ForceTargetAppShutdown" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.ForceUpdateFromAnyVersion" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.InstallAllResources" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.IsExpectedDigestsSupported" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.OptionalPackageFamilyNames" />
    <member name="P:Microsoft.Windows.Management.Deployment.RegisterPackageOptions.StageInPlace" />
    <member name="T:Microsoft.Windows.Management.Deployment.RemovePackageOptions" />
    <member name="M:Microsoft.Windows.Management.Deployment.RemovePackageOptions.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.RemovePackageOptions.FailIfNotFound" />
    <member name="P:Microsoft.Windows.Management.Deployment.RemovePackageOptions.PreserveApplicationData" />
    <member name="P:Microsoft.Windows.Management.Deployment.RemovePackageOptions.PreserveRoamableApplicationData" />
    <member name="P:Microsoft.Windows.Management.Deployment.RemovePackageOptions.RemoveForAllUsers" />
    <member name="T:Microsoft.Windows.Management.Deployment.StagePackageOptions" />
    <member name="M:Microsoft.Windows.Management.Deployment.StagePackageOptions.#ctor" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.AllowUnsigned" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.DependencyPackageUris" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.DeveloperMode" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.ExpectedDigests" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.ExternalLocationUri" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.ForceUpdateFromAnyVersion" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.InstallAllResources" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.IsExpectedDigestsSupported" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.OptionalPackageFamilyNames" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.OptionalPackageUris" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.RelatedPackageUris" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.RequiredContentGroupOnly" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.StageInPlace" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.StubPackageOption" />
    <member name="P:Microsoft.Windows.Management.Deployment.StagePackageOptions.TargetVolume" />
    <member name="T:Microsoft.Windows.Management.Deployment.StubPackageOption" />
    <member name="F:Microsoft.Windows.Management.Deployment.StubPackageOption.Default" />
    <member name="F:Microsoft.Windows.Management.Deployment.StubPackageOption.InstallFull" />
    <member name="F:Microsoft.Windows.Management.Deployment.StubPackageOption.InstallStub" />
    <member name="F:Microsoft.Windows.Management.Deployment.StubPackageOption.UsePreference" />
  </members>
</doc>