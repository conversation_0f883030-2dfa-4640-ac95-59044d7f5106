// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Widgets_Feeds_Providers_0_H
#define WINRT_Microsoft_Windows_Widgets_Feeds_Providers_0_H
WINRT_EXPORT namespace winrt::Microsoft::Windows::Widgets::Notifications
{
    struct FeedAnnouncement;
    struct FeedAnnouncementInvokedArgs;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct Deferral;
}
WINRT_EXPORT namespace winrt::Windows::Foundation::Collections
{
    template <typename T> struct WINRT_IMPL_EMPTY_BASES IIterable;
    template <typename K, typename V> struct WINRT_IMPL_EMPTY_BASES IKeyValuePair;
    struct StringMap;
}
WINRT_EXPORT namespace winrt::Windows::Storage::Streams
{
    struct IRandomAccessStreamReference;
}
WINRT_EXPORT namespace winrt::Microsoft::Windows::Widgets::Feeds::Providers
{
    struct ICustomQueryParametersRequestedArgs;
    struct ICustomQueryParametersUpdateOptions;
    struct ICustomQueryParametersUpdateOptionsFactory;
    struct IFeedAnalyticsInfoReportedArgs;
    struct IFeedAnnouncementInvokedTarget;
    struct IFeedDisabledArgs;
    struct IFeedEnabledArgs;
    struct IFeedErrorInfoReportedArgs;
    struct IFeedManager;
    struct IFeedManager2;
    struct IFeedManagerStatics;
    struct IFeedMessageReceivedArgs;
    struct IFeedProvider;
    struct IFeedProviderAnalytics;
    struct IFeedProviderDisabledArgs;
    struct IFeedProviderEnabledArgs;
    struct IFeedProviderErrors;
    struct IFeedProviderInfo;
    struct IFeedProviderMessage;
    struct IFeedResourceProvider;
    struct IFeedResourceRequest;
    struct IFeedResourceRequestedArgs;
    struct IFeedResourceResponse;
    struct IFeedResourceResponseFactory;
    struct CustomQueryParametersRequestedArgs;
    struct CustomQueryParametersUpdateOptions;
    struct FeedAnalyticsInfoReportedArgs;
    struct FeedDisabledArgs;
    struct FeedEnabledArgs;
    struct FeedErrorInfoReportedArgs;
    struct FeedManager;
    struct FeedMessageReceivedArgs;
    struct FeedProviderDisabledArgs;
    struct FeedProviderEnabledArgs;
    struct FeedProviderInfo;
    struct FeedResourceRequest;
    struct FeedResourceRequestedArgs;
    struct FeedResourceResponse;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersRequestedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptions>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptionsFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnalyticsInfoReportedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnnouncementInvokedTarget>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedDisabledArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedEnabledArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedErrorInfoReportedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManagerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedMessageReceivedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProvider>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderAnalytics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderDisabledArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderEnabledArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderErrors>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderInfo>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderMessage>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceProvider>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequest>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequestedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponse>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponseFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::CustomQueryParametersRequestedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::CustomQueryParametersUpdateOptions>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedAnalyticsInfoReportedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedDisabledArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedEnabledArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedErrorInfoReportedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedManager>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedMessageReceivedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderDisabledArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderEnabledArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderInfo>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceRequest>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceRequestedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceResponse>{ using type = class_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::CustomQueryParametersRequestedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersRequestedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::CustomQueryParametersUpdateOptions> = L"Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersUpdateOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedAnalyticsInfoReportedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedAnalyticsInfoReportedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedDisabledArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedDisabledArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedEnabledArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedEnabledArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedErrorInfoReportedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedErrorInfoReportedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedManager> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedMessageReceivedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedMessageReceivedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderDisabledArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderDisabledArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderEnabledArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderEnabledArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderInfo> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceRequest> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequest";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceRequestedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequestedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceResponse> = L"Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceResponse";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersRequestedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.ICustomQueryParametersRequestedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptions> = L"Microsoft.Windows.Widgets.Feeds.Providers.ICustomQueryParametersUpdateOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptionsFactory> = L"Microsoft.Windows.Widgets.Feeds.Providers.ICustomQueryParametersUpdateOptionsFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnalyticsInfoReportedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedAnalyticsInfoReportedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnnouncementInvokedTarget> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedAnnouncementInvokedTarget";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedDisabledArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedDisabledArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedEnabledArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedEnabledArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedErrorInfoReportedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedErrorInfoReportedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager2> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedManager2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManagerStatics> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedManagerStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedMessageReceivedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedMessageReceivedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProvider> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderAnalytics> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderAnalytics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderDisabledArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderDisabledArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderEnabledArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderEnabledArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderErrors> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderErrors";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderInfo> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderMessage> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderMessage";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceProvider> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedResourceProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequest> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedResourceRequest";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequestedArgs> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedResourceRequestedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponse> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedResourceResponse";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponseFactory> = L"Microsoft.Windows.Widgets.Feeds.Providers.IFeedResourceResponseFactory";
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersRequestedArgs>{ 0xDC2B0CD8,0x7936,0x5346,{ 0x93,0x71,0xB2,0x14,0x84,0xC7,0xD8,0x59 } }; // DC2B0CD8-**************-B21484C7D859
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptions>{ 0x753F1177,0x4909,0x568A,{ 0xB0,0x70,0x98,0xA3,0x13,0x92,0x05,0xEC } }; // 753F1177-4909-568A-B070-98A3139205EC
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptionsFactory>{ 0x34E318CD,0x3884,0x53C0,{ 0x91,0x1C,0x22,0x5F,0x32,0x22,0x8F,0xAE } }; // 34E318CD-3884-53C0-911C-225F32228FAE
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnalyticsInfoReportedArgs>{ 0x3C0E3D65,0xED47,0x5B8A,{ 0xB6,0x50,0x39,0xA7,0xED,0xF1,0x89,0x42 } }; // 3C0E3D65-ED47-5B8A-B650-39A7EDF18942
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnnouncementInvokedTarget>{ 0x5D44AE2A,0x072C,0x4DF9,{ 0x9F,0xE5,0x34,0xD5,0xD2,0xE9,0xFF,0x63 } }; // 5D44AE2A-072C-4DF9-9FE5-34D5D2E9FF63
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedDisabledArgs>{ 0x95300612,0xACA7,0x53C0,{ 0x9C,0xF6,0xD8,0x03,0x68,0x91,0x32,0xC1 } }; // 95300612-ACA7-53C0-9CF6-D803689132C1
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedEnabledArgs>{ 0xEFF4B2D7,0x7347,0x5969,{ 0xA7,0x7D,0xCA,0xC4,0x33,0xF0,0xFD,0xAE } }; // EFF4B2D7-7347-5969-A77D-CAC433F0FDAE
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedErrorInfoReportedArgs>{ 0x62DE018C,0x161E,0x52D0,{ 0x9D,0xBE,0xAE,0xC1,0x06,0xFB,0x66,0x00 } }; // 62DE018C-161E-52D0-9DBE-AEC106FB6600
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager>{ 0x87DF6A84,0x15AA,0x45CB,{ 0x89,0x11,0x5C,0xAF,0xAB,0x57,0xF7,0x23 } }; // 87DF6A84-15AA-45CB-8911-5CAFAB57F723
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager2>{ 0x5838300A,0xA069,0x455D,{ 0x99,0x43,0xBA,0x07,0x8A,0xDA,0x00,0xD8 } }; // 5838300A-A069-455D-9943-BA078ADA00D8
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManagerStatics>{ 0x4BAF5174,0x77D6,0x5E2A,{ 0x94,0xEA,0x4F,0x14,0xCC,0xDB,0x1F,0x2C } }; // 4BAF5174-77D6-5E2A-94EA-4F14CCDB1F2C
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedMessageReceivedArgs>{ 0x4ED6ECF9,0x4C74,0x5A0B,{ 0xAE,0x04,0xBE,0xF6,0xDD,0x77,0x6F,0x8A } }; // 4ED6ECF9-4C74-5A0B-AE04-BEF6DD776F8A
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProvider>{ 0x7293A12B,0x0329,0x458D,{ 0xAC,0x25,0x53,0x32,0xBE,0x47,0x8F,0xDE } }; // 7293A12B-0329-458D-AC25-5332BE478FDE
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderAnalytics>{ 0xF6885791,0x3085,0x4BD7,{ 0x9C,0xB1,0x4F,0x13,0x54,0xC3,0xA6,0x87 } }; // F6885791-3085-4BD7-9CB1-4F1354C3A687
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderDisabledArgs>{ 0x19B65AEC,0xE01D,0x5E8C,{ 0xAB,0x5F,0x32,0x42,0x12,0xE7,0xCD,0x30 } }; // 19B65AEC-E01D-5E8C-AB5F-324212E7CD30
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderEnabledArgs>{ 0x821FC9AF,0x0DE6,0x5A9B,{ 0x9A,0xE6,0xE1,0x79,0x11,0x7B,0x40,0xE4 } }; // 821FC9AF-0DE6-5A9B-9AE6-E179117B40E4
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderErrors>{ 0x6611E00A,0xD86C,0x49A3,{ 0x93,0x81,0xB7,0xDA,0x67,0xEE,0x62,0xDC } }; // 6611E00A-D86C-49A3-9381-B7DA67EE62DC
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderInfo>{ 0x73C37049,0x3C03,0x5896,{ 0x85,0x32,0xF9,0xDF,0xDA,0xEB,0x72,0x3F } }; // 73C37049-3C03-5896-8532-F9DFDAEB723F
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderMessage>{ 0x60C2442A,0x4C9D,0x4880,{ 0xBA,0x26,0xCA,0xCA,0x9E,0x56,0x7D,0xD4 } }; // 60C2442A-4C9D-4880-BA26-CACA9E567DD4
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceProvider>{ 0xE1B6266D,0x88A0,0x416C,{ 0x94,0x40,0xE3,0x41,0xCB,0x04,0x7C,0xD3 } }; // E1B6266D-88A0-416C-9440-E341CB047CD3
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequest>{ 0xE62E479C,0xE21F,0x5863,{ 0xB4,0xC9,0xDF,0x1B,0xE2,0x22,0x79,0x81 } }; // E62E479C-E21F-5863-B4C9-DF1BE2227981
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequestedArgs>{ 0x360EB709,0x0BC9,0x52C1,{ 0x9C,0x70,0x3C,0x7D,0x41,0x31,0x73,0xD8 } }; // 360EB709-0BC9-52C1-9C70-3C7D413173D8
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponse>{ 0xF831824E,0x7AEF,0x53FC,{ 0xB7,0xEE,0x32,0xAD,0xE6,0x75,0xA3,0xAD } }; // F831824E-7AEF-53FC-B7EE-32ADE675A3AD
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponseFactory>{ 0xDB01690D,0x2547,0x5D7A,{ 0xB6,0x25,0xD1,0x62,0x9F,0x44,0x3C,0x5C } }; // DB01690D-2547-5D7A-B625-D1629F443C5C
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::CustomQueryParametersRequestedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersRequestedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::CustomQueryParametersUpdateOptions>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptions; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedAnalyticsInfoReportedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnalyticsInfoReportedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedDisabledArgs>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedDisabledArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedEnabledArgs>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedEnabledArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedErrorInfoReportedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedErrorInfoReportedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedManager>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedMessageReceivedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedMessageReceivedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderDisabledArgs>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderDisabledArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderEnabledArgs>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderEnabledArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderInfo>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderInfo; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceRequest>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequest; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceRequestedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequestedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceResponse>{ using type = winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponse; };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersRequestedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_CustomQueryParameters(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptionsFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnalyticsInfoReportedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_FeedDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_AnalyticsJson(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnnouncementInvokedTarget>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnAnnouncementInvoked(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedDisabledArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_FeedDefinitionId(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedEnabledArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_FeedDefinitionId(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedErrorInfoReportedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_FeedDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_ErrorJson(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetEnabledFeedProviders(uint32_t* __resultSize, void***) noexcept = 0;
            virtual int32_t __stdcall SetCustomQueryParameters(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall SendMessageToContent(void*, void*, void*) noexcept = 0;
            virtual int32_t __stdcall TryShowAnnouncement(void*, void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManagerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetDefault(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedMessageReceivedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_FeedDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_Message(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnFeedProviderEnabled(void*) noexcept = 0;
            virtual int32_t __stdcall OnFeedProviderDisabled(void*) noexcept = 0;
            virtual int32_t __stdcall OnFeedEnabled(void*) noexcept = 0;
            virtual int32_t __stdcall OnFeedDisabled(void*) noexcept = 0;
            virtual int32_t __stdcall OnCustomQueryParametersRequested(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderAnalytics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnAnalyticsInfoReported(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderDisabledArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderEnabledArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderErrors>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnErrorInfoReported(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderInfo>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_EnabledFeedDefinitionIds(uint32_t* __valueSize, void***) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderMessage>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnMessageReceived(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnResourceRequested(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequest>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Uri(void**) noexcept = 0;
            virtual int32_t __stdcall get_Method(void**) noexcept = 0;
            virtual int32_t __stdcall put_Method(void*) noexcept = 0;
            virtual int32_t __stdcall get_Content(void**) noexcept = 0;
            virtual int32_t __stdcall put_Content(void*) noexcept = 0;
            virtual int32_t __stdcall get_Headers(void**) noexcept = 0;
            virtual int32_t __stdcall put_Headers(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequestedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_FeedProviderDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_FeedDefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_Request(void**) noexcept = 0;
            virtual int32_t __stdcall get_Response(void**) noexcept = 0;
            virtual int32_t __stdcall put_Response(void*) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponse>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Content(void**) noexcept = 0;
            virtual int32_t __stdcall get_Headers(void**) noexcept = 0;
            virtual int32_t __stdcall put_Headers(void*) noexcept = 0;
            virtual int32_t __stdcall get_ReasonPhrase(void**) noexcept = 0;
            virtual int32_t __stdcall get_StatusCode(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponseFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, int32_t, void**) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_ICustomQueryParametersRequestedArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersRequestedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_ICustomQueryParametersRequestedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_ICustomQueryParametersUpdateOptions
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
        [[nodiscard]] auto CustomQueryParameters() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptions>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_ICustomQueryParametersUpdateOptions<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_ICustomQueryParametersUpdateOptionsFactory
    {
        auto CreateInstance(param::hstring const& feedProviderDefinitionId, param::hstring const& customQueryParameters) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptionsFactory>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_ICustomQueryParametersUpdateOptionsFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedAnalyticsInfoReportedArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
        [[nodiscard]] auto FeedDefinitionId() const;
        [[nodiscard]] auto AnalyticsJson() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnalyticsInfoReportedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedAnalyticsInfoReportedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedAnnouncementInvokedTarget
    {
        auto OnAnnouncementInvoked(winrt::Microsoft::Windows::Widgets::Notifications::FeedAnnouncementInvokedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnnouncementInvokedTarget>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedAnnouncementInvokedTarget<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedDisabledArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
        [[nodiscard]] auto FeedDefinitionId() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedDisabledArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedDisabledArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedEnabledArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
        [[nodiscard]] auto FeedDefinitionId() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedEnabledArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedEnabledArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedErrorInfoReportedArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
        [[nodiscard]] auto FeedDefinitionId() const;
        [[nodiscard]] auto ErrorJson() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedErrorInfoReportedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedErrorInfoReportedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedManager
    {
        auto GetEnabledFeedProviders() const;
        auto SetCustomQueryParameters(winrt::Microsoft::Windows::Widgets::Feeds::Providers::CustomQueryParametersUpdateOptions const& options) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedManager<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedManager2
    {
        auto SendMessageToContent(param::hstring const& feedProviderDefinitionId, param::hstring const& feedDefinitionId, param::hstring const& message) const;
        auto TryShowAnnouncement(param::hstring const& feedProviderDefinitionId, param::hstring const& feedDefinitionId, winrt::Microsoft::Windows::Widgets::Notifications::FeedAnnouncement const& announcement) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager2>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedManager2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedManagerStatics
    {
        auto GetDefault() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManagerStatics>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedManagerStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedMessageReceivedArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
        [[nodiscard]] auto FeedDefinitionId() const;
        [[nodiscard]] auto Message() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedMessageReceivedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedMessageReceivedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProvider
    {
        auto OnFeedProviderEnabled(winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderEnabledArgs const& args) const;
        auto OnFeedProviderDisabled(winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedProviderDisabledArgs const& args) const;
        auto OnFeedEnabled(winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedEnabledArgs const& args) const;
        auto OnFeedDisabled(winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedDisabledArgs const& args) const;
        auto OnCustomQueryParametersRequested(winrt::Microsoft::Windows::Widgets::Feeds::Providers::CustomQueryParametersRequestedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProvider>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProvider<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderAnalytics
    {
        auto OnAnalyticsInfoReported(winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedAnalyticsInfoReportedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderAnalytics>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderAnalytics<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderDisabledArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderDisabledArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderDisabledArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderEnabledArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderEnabledArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderEnabledArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderErrors
    {
        auto OnErrorInfoReported(winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedErrorInfoReportedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderErrors>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderErrors<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderInfo
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
        [[nodiscard]] auto EnabledFeedDefinitionIds() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderInfo>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderInfo<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderMessage
    {
        auto OnMessageReceived(winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedMessageReceivedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderMessage>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedProviderMessage<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceProvider
    {
        auto OnResourceRequested(winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceRequestedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceProvider>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceProvider<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceRequest
    {
        [[nodiscard]] auto Uri() const;
        [[nodiscard]] auto Method() const;
        auto Method(param::hstring const& value) const;
        [[nodiscard]] auto Content() const;
        auto Content(winrt::Windows::Storage::Streams::IRandomAccessStreamReference const& value) const;
        [[nodiscard]] auto Headers() const;
        auto Headers(winrt::Windows::Foundation::Collections::StringMap const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequest>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceRequest<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceRequestedArgs
    {
        [[nodiscard]] auto FeedProviderDefinitionId() const;
        [[nodiscard]] auto FeedDefinitionId() const;
        [[nodiscard]] auto Request() const;
        [[nodiscard]] auto Response() const;
        auto Response(winrt::Microsoft::Windows::Widgets::Feeds::Providers::FeedResourceResponse const& value) const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequestedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceRequestedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceResponse
    {
        [[nodiscard]] auto Content() const;
        [[nodiscard]] auto Headers() const;
        auto Headers(param::async_iterable<winrt::Windows::Foundation::Collections::IKeyValuePair<hstring, hstring>> const& value) const;
        [[nodiscard]] auto ReasonPhrase() const;
        [[nodiscard]] auto StatusCode() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponse>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceResponse<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceResponseFactory
    {
        auto CreateInstance(winrt::Windows::Storage::Streams::IRandomAccessStreamReference const& content, param::hstring const& reasonPhrase, int32_t statusCode) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponseFactory>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Feeds_Providers_IFeedResourceResponseFactory<D>;
    };
}
#endif
