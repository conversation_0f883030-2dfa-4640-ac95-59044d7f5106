#pragma once

#include "MainWindow.g.h"
#include <vector>
#include <string>
#include <memory>

namespace winrt::Lightning_Shuffler::implementation
{
    // Forward declarations
    struct VideoItem;
    struct PlaylistItem;

    struct MainWindow : MainWindowT<MainWindow>
    {
        MainWindow()
        {
            // Xaml objects should not call InitializeComponent during construction.
            // See https://github.com/microsoft/cppwinrt/tree/master/nuget#initializecomponent
        }

        // Event handlers for UI elements
        void AddPlaylistButton_Click(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::RoutedEventArgs const &e);
        void CreateMixButton_Click(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::RoutedEventArgs const &e);
        void SearchBox_TextChanged(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::Controls::TextChangedEventArgs const &e);
        void QueueListView_SelectionChanged(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::Controls::SelectionChangedEventArgs const &e);

        // Media control event handlers
        void PlayPauseButton_Click(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::RoutedEventArgs const &e);
        void PreviousButton_Click(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::RoutedEventArgs const &e);
        void NextButton_Click(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::RoutedEventArgs const &e);
        void ShuffleButton_Click(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::RoutedEventArgs const &e);
        void LoopButton_Click(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::RoutedEventArgs const &e);
        void LoopButton_RightTapped(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::Input::RightTappedRoutedEventArgs const &e);
        void VolumeButton_Click(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::RoutedEventArgs const &e);
        void VolumeSlider_ValueChanged(winrt::Windows::Foundation::IInspectable const &sender, winrt::Microsoft::UI::Xaml::Controls::Primitives::RangeBaseValueChangedEventArgs const &e);

    private:
        // Private member variables
        std::vector<std::shared_ptr<VideoItem>> m_currentQueue;
        std::vector<std::shared_ptr<PlaylistItem>> m_playlists;
        int m_currentVideoIndex = -1;
        bool m_isPlaying = false;
        bool m_isShuffled = false;
        int m_loopCount = 0;
        bool m_isMuted = false;
        double m_volume = 50.0;
        std::wstring m_searchText;

        // Private helper methods
        void UpdatePlayPauseButton();
        void UpdateCurrentVideoInfo();
        void UpdateLoopButton();
        void UpdateVolumeIcon();
        void FilterQueue();
        void ShuffleQueue();
        void PlayVideo(int index);
        void PlayNext();
        void PlayPrevious();
    };

    // Data structures for videos and playlists
    struct VideoItem
    {
        std::wstring title;
        std::wstring author;
        std::wstring videoId;
        std::wstring thumbnailUrl;
        std::wstring duration;

        VideoItem(const std::wstring &t, const std::wstring &a, const std::wstring &id, const std::wstring &thumb, const std::wstring &dur)
            : title(t), author(a), videoId(id), thumbnailUrl(thumb), duration(dur) {}
    };

    struct PlaylistItem
    {
        std::wstring title;
        std::wstring playlistId;
        std::wstring thumbnailUrl;
        std::vector<std::shared_ptr<VideoItem>> videos;
        bool isMix = false;

        PlaylistItem(const std::wstring &t, const std::wstring &id, const std::wstring &thumb)
            : title(t), playlistId(id), thumbnailUrl(thumb) {}
    };
}

namespace winrt::Lightning_Shuffler::factory_implementation
{
    struct MainWindow : MainWindowT<MainWindow, implementation::MainWindow>
    {
    };
}
