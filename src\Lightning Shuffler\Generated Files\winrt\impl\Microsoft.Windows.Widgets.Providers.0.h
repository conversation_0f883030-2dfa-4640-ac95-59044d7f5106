// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Widgets_Providers_0_H
#define WINRT_Microsoft_Windows_Widgets_Providers_0_H
WINRT_EXPORT namespace winrt::Microsoft::Windows::Widgets
{
    enum class WidgetSize : int32_t;
}
WINRT_EXPORT namespace winrt::Windows::Foundation
{
    struct Deferral;
    template <typename T> struct WINRT_IMPL_EMPTY_BASES IReference;
}
WINRT_EXPORT namespace winrt::Windows::Storage::Streams
{
    struct IRandomAccessStreamReference;
}
WINRT_EXPORT namespace winrt::Microsoft::Windows::Widgets::Providers
{
    struct IWidgetActionInvokedArgs;
    struct IWidgetAnalyticsInfoReportedArgs;
    struct IWidgetContext;
    struct IWidgetContextChangedArgs;
    struct IWidgetCustomizationRequestedArgs;
    struct IWidgetErrorInfoReportedArgs;
    struct IWidgetInfo;
    struct IWidgetInfo2;
    struct IWidgetManager;
    struct IWidgetManager2;
    struct IWidgetManagerStatics;
    struct IWidgetMessageReceivedArgs;
    struct IWidgetProvider;
    struct IWidgetProvider2;
    struct IWidgetProviderAnalytics;
    struct IWidgetProviderErrors;
    struct IWidgetProviderMessage;
    struct IWidgetResourceProvider;
    struct IWidgetResourceRequest;
    struct IWidgetResourceRequestedArgs;
    struct IWidgetResourceResponse;
    struct IWidgetResourceResponseFactory;
    struct IWidgetUpdateRequestOptions;
    struct IWidgetUpdateRequestOptions2;
    struct IWidgetUpdateRequestOptionsFactory;
    struct IWidgetUpdateRequestOptionsStatics;
    struct WidgetActionInvokedArgs;
    struct WidgetAnalyticsInfoReportedArgs;
    struct WidgetContext;
    struct WidgetContextChangedArgs;
    struct WidgetCustomizationRequestedArgs;
    struct WidgetErrorInfoReportedArgs;
    struct WidgetInfo;
    struct WidgetManager;
    struct WidgetMessageReceivedArgs;
    struct WidgetResourceRequest;
    struct WidgetResourceRequestedArgs;
    struct WidgetResourceResponse;
    struct WidgetUpdateRequestOptions;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetActionInvokedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetAnalyticsInfoReportedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContext>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContextChangedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetCustomizationRequestedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetErrorInfoReportedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManagerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetMessageReceivedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderAnalytics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderErrors>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderMessage>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceProvider>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequest>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequestedArgs>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponse>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponseFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsFactory>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetActionInvokedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetAnalyticsInfoReportedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetContext>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetContextChangedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetCustomizationRequestedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetErrorInfoReportedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetInfo>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetManager>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetMessageReceivedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceRequest>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceRequestedArgs>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceResponse>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Widgets::Providers::WidgetUpdateRequestOptions>{ using type = class_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetActionInvokedArgs> = L"Microsoft.Windows.Widgets.Providers.WidgetActionInvokedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetAnalyticsInfoReportedArgs> = L"Microsoft.Windows.Widgets.Providers.WidgetAnalyticsInfoReportedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetContext> = L"Microsoft.Windows.Widgets.Providers.WidgetContext";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetContextChangedArgs> = L"Microsoft.Windows.Widgets.Providers.WidgetContextChangedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetCustomizationRequestedArgs> = L"Microsoft.Windows.Widgets.Providers.WidgetCustomizationRequestedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetErrorInfoReportedArgs> = L"Microsoft.Windows.Widgets.Providers.WidgetErrorInfoReportedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetInfo> = L"Microsoft.Windows.Widgets.Providers.WidgetInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetManager> = L"Microsoft.Windows.Widgets.Providers.WidgetManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetMessageReceivedArgs> = L"Microsoft.Windows.Widgets.Providers.WidgetMessageReceivedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceRequest> = L"Microsoft.Windows.Widgets.Providers.WidgetResourceRequest";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceRequestedArgs> = L"Microsoft.Windows.Widgets.Providers.WidgetResourceRequestedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceResponse> = L"Microsoft.Windows.Widgets.Providers.WidgetResourceResponse";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::WidgetUpdateRequestOptions> = L"Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetActionInvokedArgs> = L"Microsoft.Windows.Widgets.Providers.IWidgetActionInvokedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetAnalyticsInfoReportedArgs> = L"Microsoft.Windows.Widgets.Providers.IWidgetAnalyticsInfoReportedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContext> = L"Microsoft.Windows.Widgets.Providers.IWidgetContext";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContextChangedArgs> = L"Microsoft.Windows.Widgets.Providers.IWidgetContextChangedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetCustomizationRequestedArgs> = L"Microsoft.Windows.Widgets.Providers.IWidgetCustomizationRequestedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetErrorInfoReportedArgs> = L"Microsoft.Windows.Widgets.Providers.IWidgetErrorInfoReportedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo> = L"Microsoft.Windows.Widgets.Providers.IWidgetInfo";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo2> = L"Microsoft.Windows.Widgets.Providers.IWidgetInfo2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager> = L"Microsoft.Windows.Widgets.Providers.IWidgetManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager2> = L"Microsoft.Windows.Widgets.Providers.IWidgetManager2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManagerStatics> = L"Microsoft.Windows.Widgets.Providers.IWidgetManagerStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetMessageReceivedArgs> = L"Microsoft.Windows.Widgets.Providers.IWidgetMessageReceivedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider> = L"Microsoft.Windows.Widgets.Providers.IWidgetProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider2> = L"Microsoft.Windows.Widgets.Providers.IWidgetProvider2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderAnalytics> = L"Microsoft.Windows.Widgets.Providers.IWidgetProviderAnalytics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderErrors> = L"Microsoft.Windows.Widgets.Providers.IWidgetProviderErrors";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderMessage> = L"Microsoft.Windows.Widgets.Providers.IWidgetProviderMessage";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceProvider> = L"Microsoft.Windows.Widgets.Providers.IWidgetResourceProvider";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequest> = L"Microsoft.Windows.Widgets.Providers.IWidgetResourceRequest";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequestedArgs> = L"Microsoft.Windows.Widgets.Providers.IWidgetResourceRequestedArgs";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponse> = L"Microsoft.Windows.Widgets.Providers.IWidgetResourceResponse";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponseFactory> = L"Microsoft.Windows.Widgets.Providers.IWidgetResourceResponseFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions> = L"Microsoft.Windows.Widgets.Providers.IWidgetUpdateRequestOptions";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions2> = L"Microsoft.Windows.Widgets.Providers.IWidgetUpdateRequestOptions2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsFactory> = L"Microsoft.Windows.Widgets.Providers.IWidgetUpdateRequestOptionsFactory";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsStatics> = L"Microsoft.Windows.Widgets.Providers.IWidgetUpdateRequestOptionsStatics";
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetActionInvokedArgs>{ 0xC593CC57,0x04B9,0x52CA,{ 0x88,0xAD,0x46,0xFE,0xA2,0x1E,0xA3,0x40 } }; // C593CC57-04B9-52CA-88AD-46FEA21EA340
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetAnalyticsInfoReportedArgs>{ 0x1D9E5FB5,0x2BCE,0x5350,{ 0x87,0xB1,0xD6,0x31,0x99,0x52,0x66,0x39 } }; // 1D9E5FB5-2BCE-5350-87B1-D63199526639
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContext>{ 0x903C518B,0x40BC,0x5BC6,{ 0x88,0xF7,0xAF,0x9D,0x81,0xC0,0xCD,0xC1 } }; // 903C518B-40BC-5BC6-88F7-AF9D81C0CDC1
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContextChangedArgs>{ 0x2C226D54,0x2252,0x576B,{ 0xA1,0x97,0x37,0x0B,0x28,0xD2,0x5C,0x2F } }; // 2C226D54-2252-576B-A197-370B28D25C2F
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetCustomizationRequestedArgs>{ 0x41DEA311,0xDD9B,0x5B8B,{ 0xB4,0x93,0x3A,0x30,0x55,0x21,0x16,0xB8 } }; // 41DEA311-DD9B-5B8B-B493-3A30552116B8
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetErrorInfoReportedArgs>{ 0x30EFA627,0xB21F,0x55D5,{ 0xB9,0x1A,0xB2,0x3B,0x4A,0xA1,0x36,0x45 } }; // 30EFA627-B21F-55D5-B91A-B23B4AA13645
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo>{ 0xCEA11F42,0xA020,0x5DB5,{ 0x89,0xE2,0xB7,0xDE,0xCE,0x4A,0xE5,0xCB } }; // CEA11F42-A020-5DB5-89E2-B7DECE4AE5CB
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo2>{ 0x081B0A6F,0xD784,0x5408,{ 0xBB,0x29,0x25,0x2F,0xEF,0x29,0x26,0xD4 } }; // 081B0A6F-D784-5408-BB29-252FEF2926D4
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager>{ 0x71CB10C0,0x671E,0x48E3,{ 0xB9,0x95,0x20,0x79,0x40,0x39,0x71,0x23 } }; // 71CB10C0-671E-48E3-B995-************
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager2>{ 0x55C65A27,0x8845,0x406C,{ 0x9E,0xE1,0x1E,0x79,0xF0,0x55,0x6B,0xEF } }; // 55C65A27-8845-406C-9EE1-1E79F0556BEF
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManagerStatics>{ 0x7F233B06,0x28E5,0x5E2B,{ 0x8C,0x04,0xA4,0xFA,0x74,0x7C,0x28,0xC7 } }; // 7F233B06-28E5-5E2B-8C04-A4FA747C28C7
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetMessageReceivedArgs>{ 0x2261CB2B,0xC741,0x5F96,{ 0x9A,0xDB,0xFB,0x3A,0x76,0x67,0xBC,0xB6 } }; // 2261CB2B-C741-5F96-9ADB-FB3A7667BCB6
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider>{ 0x5C5774CC,0x72A0,0x452D,{ 0xB9,0xED,0x07,0x5C,0x0D,0xD2,0x5E,0xED } }; // 5C5774CC-72A0-452D-B9ED-075C0DD25EED
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider2>{ 0x38C3A963,0xDD93,0x479D,{ 0x92,0x76,0x04,0xBF,0x84,0xEE,0x18,0x16 } }; // 38C3A963-DD93-479D-9276-04BF84EE1816
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderAnalytics>{ 0x661985A5,0xD187,0x482D,{ 0x9E,0xEF,0x6F,0xDA,0x05,0xD2,0x18,0x45 } }; // 661985A5-D187-482D-9EEF-6FDA05D21845
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderErrors>{ 0x90C1B5F0,0x0D3A,0x4AC6,{ 0xAB,0xB7,0xC9,0x7B,0x36,0x7B,0x8F,0xCC } }; // 90C1B5F0-0D3A-4AC6-ABB7-C97B367B8FCC
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderMessage>{ 0xEA4DC186,0x9E24,0x4B35,{ 0xA5,0xEF,0xA9,0xF5,0xDF,0x72,0xD6,0xAC } }; // EA4DC186-9E24-4B35-A5EF-A9F5DF72D6AC
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceProvider>{ 0xDCF328C0,0x012C,0x40F5,{ 0xBB,0x28,0x3A,0x1C,0x71,0x4D,0x02,0x7D } }; // DCF328C0-012C-40F5-BB28-3A1C714D027D
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequest>{ 0x113D249F,0x82D9,0x57CB,{ 0x8C,0xEA,0x9A,0x52,0x91,0xF2,0xFE,0x22 } }; // 113D249F-82D9-57CB-8CEA-9A5291F2FE22
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequestedArgs>{ 0x2BB30F4D,0x0166,0x58E3,{ 0xAA,0xF6,0x31,0xB2,0xAE,0x97,0x0B,0xCD } }; // 2BB30F4D-0166-58E3-AAF6-31B2AE970BCD
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponse>{ 0x03A2D32C,0x2E9E,0x54A3,{ 0xB0,0x84,0x14,0x79,0xD5,0x06,0x0F,0x80 } }; // 03A2D32C-2E9E-54A3-B084-1479D5060F80
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponseFactory>{ 0x08881EF1,0xA78A,0x5804,{ 0xB0,0x70,0x91,0x53,0xA8,0x65,0x7F,0x85 } }; // 08881EF1-A78A-5804-B070-9153A8657F85
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions>{ 0xB09CA8F7,0x7424,0x5687,{ 0xBA,0xAF,0x7D,0xD6,0xFA,0x63,0x96,0x72 } }; // B09CA8F7-7424-5687-BAAF-7DD6FA639672
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions2>{ 0x77C4EFC4,0x38F3,0x57A5,{ 0xAB,0xA1,0xF8,0x3F,0x25,0x7B,0x89,0x9E } }; // 77C4EFC4-38F3-57A5-ABA1-F83F257B899E
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsFactory>{ 0xE0E00AF8,0x1D10,0x57A8,{ 0x94,0x19,0x3F,0x56,0x8E,0x85,0x4D,0xAA } }; // E0E00AF8-1D10-57A8-9419-3F568E854DAA
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsStatics>{ 0x4645B5E3,0xD332,0x5D11,{ 0x82,0xF0,0x36,0x07,0xE5,0xDF,0x60,0x18 } }; // 4645B5E3-D332-5D11-82F0-3607E5DF6018
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetActionInvokedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetActionInvokedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetAnalyticsInfoReportedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetAnalyticsInfoReportedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetContext>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetContext; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetContextChangedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetContextChangedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetCustomizationRequestedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetCustomizationRequestedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetErrorInfoReportedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetErrorInfoReportedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetInfo>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetManager>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetMessageReceivedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetMessageReceivedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceRequest>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequest; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceRequestedArgs>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequestedArgs; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceResponse>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponse; };
    template <> struct default_interface<winrt::Microsoft::Windows::Widgets::Providers::WidgetUpdateRequestOptions>{ using type = winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions; };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetActionInvokedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WidgetContext(void**) noexcept = 0;
            virtual int32_t __stdcall get_Verb(void**) noexcept = 0;
            virtual int32_t __stdcall get_Data(void**) noexcept = 0;
            virtual int32_t __stdcall get_CustomState(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetAnalyticsInfoReportedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WidgetContext(void**) noexcept = 0;
            virtual int32_t __stdcall get_AnalyticsJson(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContext>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Id(void**) noexcept = 0;
            virtual int32_t __stdcall get_DefinitionId(void**) noexcept = 0;
            virtual int32_t __stdcall get_Size(int32_t*) noexcept = 0;
            virtual int32_t __stdcall get_IsActive(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContextChangedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WidgetContext(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetCustomizationRequestedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WidgetContext(void**) noexcept = 0;
            virtual int32_t __stdcall get_CustomState(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetErrorInfoReportedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WidgetContext(void**) noexcept = 0;
            virtual int32_t __stdcall get_ErrorJson(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WidgetContext(void**) noexcept = 0;
            virtual int32_t __stdcall get_Template(void**) noexcept = 0;
            virtual int32_t __stdcall get_Data(void**) noexcept = 0;
            virtual int32_t __stdcall get_CustomState(void**) noexcept = 0;
            virtual int32_t __stdcall get_LastUpdateTime(int64_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsPlaceholderContent(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall UpdateWidget(void*) noexcept = 0;
            virtual int32_t __stdcall GetWidgetIds(uint32_t* __resultSize, void***) noexcept = 0;
            virtual int32_t __stdcall GetWidgetInfo(void*, void**) noexcept = 0;
            virtual int32_t __stdcall GetWidgetInfos(uint32_t* __resultSize, void***) noexcept = 0;
            virtual int32_t __stdcall DeleteWidget(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall SendMessageToContent(void*, void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManagerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetDefault(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetMessageReceivedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WidgetContext(void**) noexcept = 0;
            virtual int32_t __stdcall get_Message(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateWidget(void*) noexcept = 0;
            virtual int32_t __stdcall DeleteWidget(void*, void*) noexcept = 0;
            virtual int32_t __stdcall OnActionInvoked(void*) noexcept = 0;
            virtual int32_t __stdcall OnWidgetContextChanged(void*) noexcept = 0;
            virtual int32_t __stdcall Activate(void*) noexcept = 0;
            virtual int32_t __stdcall Deactivate(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnCustomizationRequested(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderAnalytics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnAnalyticsInfoReported(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderErrors>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnErrorInfoReported(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderMessage>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnMessageReceived(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceProvider>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall OnResourceRequested(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequest>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Uri(void**) noexcept = 0;
            virtual int32_t __stdcall get_Method(void**) noexcept = 0;
            virtual int32_t __stdcall put_Method(void*) noexcept = 0;
            virtual int32_t __stdcall get_Content(void**) noexcept = 0;
            virtual int32_t __stdcall put_Content(void*) noexcept = 0;
            virtual int32_t __stdcall get_Headers(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequestedArgs>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WidgetContext(void**) noexcept = 0;
            virtual int32_t __stdcall get_Request(void**) noexcept = 0;
            virtual int32_t __stdcall get_Response(void**) noexcept = 0;
            virtual int32_t __stdcall put_Response(void*) noexcept = 0;
            virtual int32_t __stdcall GetDeferral(void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponse>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_Content(void**) noexcept = 0;
            virtual int32_t __stdcall get_Headers(void**) noexcept = 0;
            virtual int32_t __stdcall get_ReasonPhrase(void**) noexcept = 0;
            virtual int32_t __stdcall get_StatusCode(int32_t*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponseFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void*, int32_t, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_WidgetId(void**) noexcept = 0;
            virtual int32_t __stdcall get_Template(void**) noexcept = 0;
            virtual int32_t __stdcall put_Template(void*) noexcept = 0;
            virtual int32_t __stdcall get_Data(void**) noexcept = 0;
            virtual int32_t __stdcall put_Data(void*) noexcept = 0;
            virtual int32_t __stdcall get_CustomState(void**) noexcept = 0;
            virtual int32_t __stdcall put_CustomState(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_IsPlaceholderContent(void**) noexcept = 0;
            virtual int32_t __stdcall put_IsPlaceholderContent(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsFactory>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall CreateInstance(void*, void**) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_UnsetValue(void**) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetActionInvokedArgs
    {
        [[nodiscard]] auto WidgetContext() const;
        [[nodiscard]] auto Verb() const;
        [[nodiscard]] auto Data() const;
        [[nodiscard]] auto CustomState() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetActionInvokedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetActionInvokedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetAnalyticsInfoReportedArgs
    {
        [[nodiscard]] auto WidgetContext() const;
        [[nodiscard]] auto AnalyticsJson() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetAnalyticsInfoReportedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetAnalyticsInfoReportedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetContext
    {
        [[nodiscard]] auto Id() const;
        [[nodiscard]] auto DefinitionId() const;
        [[nodiscard]] auto Size() const;
        [[nodiscard]] auto IsActive() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContext>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetContext<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetContextChangedArgs
    {
        [[nodiscard]] auto WidgetContext() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetContextChangedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetContextChangedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetCustomizationRequestedArgs
    {
        [[nodiscard]] auto WidgetContext() const;
        [[nodiscard]] auto CustomState() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetCustomizationRequestedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetCustomizationRequestedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetErrorInfoReportedArgs
    {
        [[nodiscard]] auto WidgetContext() const;
        [[nodiscard]] auto ErrorJson() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetErrorInfoReportedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetErrorInfoReportedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetInfo
    {
        [[nodiscard]] auto WidgetContext() const;
        [[nodiscard]] auto Template() const;
        [[nodiscard]] auto Data() const;
        [[nodiscard]] auto CustomState() const;
        [[nodiscard]] auto LastUpdateTime() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetInfo<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetInfo2
    {
        [[nodiscard]] auto IsPlaceholderContent() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetInfo2>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetInfo2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetManager
    {
        auto UpdateWidget(winrt::Microsoft::Windows::Widgets::Providers::WidgetUpdateRequestOptions const& widgetUpdateRequestOptions) const;
        auto GetWidgetIds() const;
        auto GetWidgetInfo(param::hstring const& widgetId) const;
        auto GetWidgetInfos() const;
        auto DeleteWidget(param::hstring const& widgetId) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetManager<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetManager2
    {
        auto SendMessageToContent(param::hstring const& widgetId, param::hstring const& message) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManager2>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetManager2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetManagerStatics
    {
        auto GetDefault() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetManagerStatics>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetManagerStatics<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetMessageReceivedArgs
    {
        [[nodiscard]] auto WidgetContext() const;
        [[nodiscard]] auto Message() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetMessageReceivedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetMessageReceivedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetProvider
    {
        auto CreateWidget(winrt::Microsoft::Windows::Widgets::Providers::WidgetContext const& widgetContext) const;
        auto DeleteWidget(param::hstring const& widgetId, param::hstring const& customState) const;
        auto OnActionInvoked(winrt::Microsoft::Windows::Widgets::Providers::WidgetActionInvokedArgs const& actionInvokedArgs) const;
        auto OnWidgetContextChanged(winrt::Microsoft::Windows::Widgets::Providers::WidgetContextChangedArgs const& contextChangedArgs) const;
        auto Activate(winrt::Microsoft::Windows::Widgets::Providers::WidgetContext const& widgetContext) const;
        auto Deactivate(param::hstring const& widgetId) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetProvider<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetProvider2
    {
        auto OnCustomizationRequested(winrt::Microsoft::Windows::Widgets::Providers::WidgetCustomizationRequestedArgs const& customizationRequestedArgs) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProvider2>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetProvider2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetProviderAnalytics
    {
        auto OnAnalyticsInfoReported(winrt::Microsoft::Windows::Widgets::Providers::WidgetAnalyticsInfoReportedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderAnalytics>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetProviderAnalytics<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetProviderErrors
    {
        auto OnErrorInfoReported(winrt::Microsoft::Windows::Widgets::Providers::WidgetErrorInfoReportedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderErrors>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetProviderErrors<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetProviderMessage
    {
        auto OnMessageReceived(winrt::Microsoft::Windows::Widgets::Providers::WidgetMessageReceivedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetProviderMessage>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetProviderMessage<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceProvider
    {
        auto OnResourceRequested(winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceRequestedArgs const& args) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceProvider>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceProvider<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceRequest
    {
        [[nodiscard]] auto Uri() const;
        [[nodiscard]] auto Method() const;
        auto Method(param::hstring const& value) const;
        [[nodiscard]] auto Content() const;
        auto Content(winrt::Windows::Storage::Streams::IRandomAccessStreamReference const& value) const;
        [[nodiscard]] auto Headers() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequest>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceRequest<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceRequestedArgs
    {
        [[nodiscard]] auto WidgetContext() const;
        [[nodiscard]] auto Request() const;
        [[nodiscard]] auto Response() const;
        auto Response(winrt::Microsoft::Windows::Widgets::Providers::WidgetResourceResponse const& value) const;
        auto GetDeferral() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceRequestedArgs>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceRequestedArgs<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceResponse
    {
        [[nodiscard]] auto Content() const;
        [[nodiscard]] auto Headers() const;
        [[nodiscard]] auto ReasonPhrase() const;
        [[nodiscard]] auto StatusCode() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponse>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceResponse<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceResponseFactory
    {
        auto CreateInstance(winrt::Windows::Storage::Streams::IRandomAccessStreamReference const& content, param::hstring const& reasonPhrase, int32_t statusCode) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetResourceResponseFactory>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetResourceResponseFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetUpdateRequestOptions
    {
        [[nodiscard]] auto WidgetId() const;
        [[nodiscard]] auto Template() const;
        auto Template(param::hstring const& value) const;
        [[nodiscard]] auto Data() const;
        auto Data(param::hstring const& value) const;
        [[nodiscard]] auto CustomState() const;
        auto CustomState(param::hstring const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetUpdateRequestOptions<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetUpdateRequestOptions2
    {
        [[nodiscard]] auto IsPlaceholderContent() const;
        auto IsPlaceholderContent(winrt::Windows::Foundation::IReference<bool> const& value) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptions2>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetUpdateRequestOptions2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetUpdateRequestOptionsFactory
    {
        auto CreateInstance(param::hstring const& widgetId) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsFactory>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetUpdateRequestOptionsFactory<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_Widgets_Providers_IWidgetUpdateRequestOptionsStatics
    {
        [[nodiscard]] auto UnsetValue() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Widgets::Providers::IWidgetUpdateRequestOptionsStatics>
    {
        template <typename D> using type = consume_Microsoft_Windows_Widgets_Providers_IWidgetUpdateRequestOptionsStatics<D>;
    };
}
#endif
