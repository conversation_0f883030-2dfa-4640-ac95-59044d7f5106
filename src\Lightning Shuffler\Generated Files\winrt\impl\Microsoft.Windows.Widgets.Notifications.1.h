// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Widgets_Notifications_1_H
#define WINRT_Microsoft_Windows_Widgets_Notifications_1_H
#include "winrt/impl/Microsoft.Windows.Widgets.Notifications.0.h"
WINRT_EXPORT namespace winrt::Microsoft::Windows::Widgets::Notifications
{
    struct WINRT_IMPL_EMPTY_BASES IFeedAnnouncement :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFeedAnnouncement>
    {
        IFeedAnnouncement(std::nullptr_t = nullptr) noexcept {}
        IFeedAnnouncement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFeedAnnouncementFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFeedAnnouncementFactory>
    {
        IFeedAnnouncementFactory(std::nullptr_t = nullptr) noexcept {}
        IFeedAnnouncementFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IFeedAnnouncementInvokedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFeedAnnouncementInvokedArgs>
    {
        IFeedAnnouncementInvokedArgs(std::nullptr_t = nullptr) noexcept {}
        IFeedAnnouncementInvokedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
