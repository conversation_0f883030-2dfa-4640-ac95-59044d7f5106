<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <WidgetService-Platform Condition="'$(Platform)' == 'Win32'">x86</WidgetService-Platform>
        <WidgetService-Platform Condition="'$(Platform)' != 'Win32'">$(Platform)</WidgetService-Platform>
    </PropertyGroup>
    <ItemGroup>
        <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.Widgets.winmd">
            <Implementation>Microsoft.Windows.Widgets.dll</Implementation>
            <Private>false</Private>
        </Reference>
    </ItemGroup>
</Project>
