<doc>
  <assembly>
    <name>Microsoft.Windows.System.Projection</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.System.EnvironmentManager">
      <summary>A class for reading and writing environment variables.</summary>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.AddExecutableFileExtension(System.String)">
      <summary>Adds the specified file extension to the end of the PATHEXT environment variable.</summary>
      <param name="pathExt">The file extension to be added to the PATHEXT environment variable.</param>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.AppendToPath(System.String)">
      <summary>Adds the specified path to the end of the PATH environment variable.</summary>
      <param name="path">The path to be appended to the PATH environment variable.</param>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.GetEnvironmentVariable(System.String)">
      <summary>Gets the value of the specified environment variable at the scope of the current EnvironmentManager.</summary>
      <param name="name">The name of the environment variable to fetch.</param>
      <returns>The value for the specified environment variable.</returns>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.GetEnvironmentVariables">
      <summary>Gets a collection of environment variables at the scope of the current EnvironmentManager.</summary>
      <returns>A dictionary of environment variable key/value pairs.</returns>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.GetForMachine">
      <summary>Gets an EnvironmentManager at system-wide scope.</summary>
      <returns>An EnvironmentManager at system-wide scope.</returns>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.GetForProcess">
      <summary>Gets an EnvironmentManager scoped to the current process.</summary>
      <returns>An EnvironmentManager scoped to the current process.</returns>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.GetForUser">
      <summary>Gets an EnvironmentManager scoped to the current user.</summary>
      <returns>An EnvironmentManager scoped to the current user.</returns>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.RemoveExecutableFileExtension(System.String)">
      <summary>Removes the specified file extension from the end of the PATHEXT environment variable.</summary>
      <param name="pathExt">The file extension to be removed from the PATHEXT environment variable.</param>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.RemoveFromPath(System.String)">
      <summary>Removes the specified path from the end of the PATH environment variable.</summary>
      <param name="path">The path to be removed from the PATH environment variable.</param>
    </member>
    <member name="M:Microsoft.Windows.System.EnvironmentManager.SetEnvironmentVariable(System.String,System.String)">
      <summary>Sets the value of the specified environment variable at the scope of the current EnvironmentManager.</summary>
      <param name="name">The name of the environment variable to set.</param>
      <param name="value">The new value to set for the specified environment variable.</param>
    </member>
    <member name="P:Microsoft.Windows.System.EnvironmentManager.AreChangesTracked">
      <summary>Gets a value indicating whether environment variable changes are tracked.</summary>
      <returns>true if environment variable changes are tracked; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Windows.System.EnvironmentManager.IsSupported">
      <summary>Gets a value that indicates whether EnvironmentManager is supported on the current device/OS version.</summary>
      <returns>true if EnvironmentManager is supported; otherwise, false.</returns>
    </member>
  </members>
</doc>