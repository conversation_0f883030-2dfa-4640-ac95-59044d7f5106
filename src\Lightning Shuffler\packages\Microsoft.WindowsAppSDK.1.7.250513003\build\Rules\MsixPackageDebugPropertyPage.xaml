﻿<?xml version="1.0" encoding="utf-8"?><!--
     Defines properties that will be used by the .NET Project System to build a custom
     Launch Profile UI for configuring our launch settings.
     https://github.com/dotnet/project-system/blob/main/docs/repo/property-pages/how-to-add-a-new-launch-profile-kind.md
 --><Rule Name="MsixPackageDebugPropertyPage" Description="Properties associated with launching and debugging a packaged .NET app." DisplayName="MSIX Package" PageTemplate="commandNameBasedDebugger" xmlns="http://schemas.microsoft.com/build/2009/properties" xmlns:sys="clr-namespace:System;assembly=mscorlib" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    

    <Rule.Metadata>
        <sys:String x:Key="CommandName">MsixPackage</sys:String>

        <!-- KnownImageIds.ImageCatalogGuid -->
        <sys:Guid x:Key="ImageMonikerGuid">AE27A6B0-E345-4288-96DF-5EAF394EE369</sys:Guid>

        <!-- KnownImageIds.Package -->
        <sys:Int32 x:Key="ImageMonikerId">2209</sys:Int32>
    </Rule.Metadata>

    <Rule.DataSource>
        <DataSource Persistence="LaunchProfile" HasConfigurationCondition="False" ItemType="LaunchProfile" />
    </Rule.DataSource>

    <!-- 'Virtual' launch profile property that is persisted to .user file; needs to be accessible to MSBuild -->
    <StringProperty Name="LayoutDir" DisplayName="Layout folder path" Description="Path to the directory where the package layout is copied to disk when the app is built." Subtype="folder">
        
        
    </StringProperty>

    <StringProperty Name="CommandLineArguments" DisplayName="Command line arguments" Description="Command line arguments to pass to the app.">
        
        
    </StringProperty>

    <BoolProperty Name="DoNotLaunchApp" DisplayName="Do not launch app" Description="Do not launch the app, but debug my code when it starts.">
        
        
    </BoolProperty>

    <BoolProperty Name="AllowLocalNetworkLoopback" DisplayName="Allow local network loopback" Description="Allow the app to make network calls to the device it is installed on." Default="true">
        
        
    </BoolProperty>

    <BoolProperty Name="AlwaysReinstallApp" DisplayName="Always reinstall app" Description="Uninstall and then reinstall the app. All information about the app state is deleted.">
        
        
    </BoolProperty>

    <!-- 'Virtual' launch profile property that is persisted to .user file; needs to be accessible to MSBuild -->
    <BoolProperty Name="DeployOptionalPackages" DisplayName="Deploy optional packages" Description="Deploy optional packages used by the app.">
      
      
    </BoolProperty>

    <BoolProperty Name="NativeDebugging" DisplayName="Enable native code debugging" Description="Enable debugging for managed and native code together, also known as mixed-mode debugging.">
        
        
    </BoolProperty>

    <BoolProperty Name="RemoteDebugEnabled" DisplayName="Use remote machine" Description="Indicates that the debugger should attach to a process on a remote machine.">
        
        
    </BoolProperty>

    <StringProperty Name="RemoteDebugMachine" DisplayName="Remote machine name" Description="The name of the remote machine.">
          
          
          <StringProperty.Metadata>
              <NameValuePair Name="VisibilityCondition">
                <NameValuePair.Value>(has-evaluated-value "MsixPackage" "RemoteDebugEnabled" true)</NameValuePair.Value>
              </NameValuePair>
          </StringProperty.Metadata>
    </StringProperty>

    <DynamicEnumProperty Name="AuthenticationMode" DisplayName="Authentication mode" Description="The authentication scheme to use when connecting to the remote machine." EnumProvider="AuthenticationModeEnumProvider">
        
        
        <DynamicEnumProperty.Metadata>
            <NameValuePair Name="VisibilityCondition">
                <NameValuePair.Value>(has-evaluated-value "MsixPackage" "RemoteDebugEnabled" true)</NameValuePair.Value>
            </NameValuePair>
        </DynamicEnumProperty.Metadata>
    </DynamicEnumProperty>

    <!-- 'Virtual' launch profile property that is persisted to .user file; needs to be accessible to MSBuild -->
    <EnumProperty Name="RemoteDeploymentType" DisplayName="Deployment type" Description="How the app should be deployed to the remote machine.">
        
        
        <EnumProperty.Metadata>
            <NameValuePair Name="VisibilityCondition">
                <NameValuePair.Value>(has-evaluated-value "MsixPackage" "RemoteDebugEnabled" true)</NameValuePair.Value>
            </NameValuePair>
        </EnumProperty.Metadata>
        <EnumValue Name="CopyToDevice" DisplayName="Copy files to device" IsDefault="true">
            
        </EnumValue>
        <EnumValue Name="RegisterFromNetwork" DisplayName="Register layout from network">
            
        </EnumValue>
    </EnumProperty>

    <!-- 'Virtual' launch profile property that is persisted to .user file; needs to be accessible to MSBuild -->
    <StringProperty Name="PackageRegistrationPath" DisplayName="Package registration path" Description="For 'Copy To Device' deployment, this denotes the physical location on the remote device where the layout will be copied and is an optional parameter. For 'Register From Network', this denotes a remote network location of the package layout and is a required parameter." Subtype="folder">
        
        
        <StringProperty.Metadata>
            <NameValuePair Name="VisibilityCondition">
              <NameValuePair.Value>(has-evaluated-value "MsixPackage" "RemoteDebugEnabled" true)</NameValuePair.Value>
            </NameValuePair>
        </StringProperty.Metadata>
    </StringProperty>

    <!-- 'Virtual' launch profile property that is persisted to .user file; needs to be accessible to MSBuild -->
    <BoolProperty Name="RemoveNonLayoutFiles" DisplayName="Cleanup layout" Description="Specifies whether the files that are not a part of the layout be removed from target device during deployment - This is applicable only for 'Copy To Device' deployment.">
        
        
        <BoolProperty.Metadata>
            <NameValuePair Name="VisibilityCondition">
                <NameValuePair.Value>(and (has-evaluated-value "MsixPackage" "RemoteDebugEnabled" true) (has-evaluated-value "MsixPackage" "RemoteDeploymentType" "CopyToDevice"))</NameValuePair.Value>
            </NameValuePair>
        </BoolProperty.Metadata>
    </BoolProperty>

    <BoolProperty Name="HotReloadEnabled" DisplayName="Enable Hot Reload" Description="Apply code changes to the running application.">
        
        
    </BoolProperty>

</Rule>