<doc>
  <assembly>
    <name>Microsoft.Windows.Widgets</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersRequestedArgs">
      <summary>Provides data for the OnCustomQueryParametersRequested(CustomQueryParametersRequestedArgs) method.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersRequestedArgs.FeedProviderDefinitionId">
      <summary>Gets the unique identifier for the feed provider for which query parameters are requested, as specified in the app manifest of the feed provider app.</summary>
      <returns>A string containing the ID of the feed provider.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersUpdateOptions">
      <summary>Conveys the query parameters and related metadata for calls to SetCustomQueryParameters(CustomQueryParametersUpdateOptions).</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersUpdateOptions.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the CustomQueryParametersUpdateOptions class.</summary>
      <param name="feedProviderDefinitionId">The unique identifier for the feed associated with the query parameters update.</param>
      <param name="customQueryParameters">A string containing the query parameters that are passed to the remote feed provider URI when the Widgets Board is requesting feed content.</param>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersUpdateOptions.CustomQueryParameters">
      <summary>Gets a string containing the query parameters.</summary>
      <returns>A string containing the query parameters.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersUpdateOptions.FeedProviderDefinitionId">
      <summary>Gets the unique identifier for the feed associated with the query parameters update.</summary>
      <returns>A string containing the ID of the feed provider.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedAnalyticsInfoReportedArgs">
      <summary>Provides data for the OnAnalyticsInfoReported event which is raised when the feeds host reports analytics data associated with a user interaction with a feed.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedAnalyticsInfoReportedArgs.AnalyticsJson">
      <summary>Gets a JSON string describing the user interaction that triggered the analytics event.</summary>
      <returns>A JSON string.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedAnalyticsInfoReportedArgs.FeedDefinitionId">
      <summary>Gets the definition ID of the feed associated with the analytics info.</summary>
      <returns>The definition ID of the feed associated with the analytics info.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedAnalyticsInfoReportedArgs.FeedProviderDefinitionId">
      <summary>Gets the definition ID of the feed provider associated with the analytics info.</summary>
      <returns>The definition ID of the feed provider associated with the analytics info.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedDisabledArgs">
      <summary>Provides data for the OnFeedDisabled(FeedDisabledArgs) method.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedDisabledArgs.FeedDefinitionId">
      <summary>Gets the unique identifier for the disabled feed, as specified in the app manifest of the feed provider app.</summary>
      <returns>A string containing the ID of the disabled feed.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedDisabledArgs.FeedProviderDefinitionId">
      <summary>Gets the unique identifier for the feed provider associated with the disabled feed, as specified in the app manifest of the feed provider app.</summary>
      <returns>A string containing the ID of the feed provider.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedEnabledArgs">
      <summary>Provides data for the OnFeedEnabled(FeedEnabledArgs) method.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedEnabledArgs.FeedDefinitionId">
      <summary>Gets the unique identifier for the enabled feed, as specified in the app manifest of the feed provider app.</summary>
      <returns>A string containing the ID of the enabled feed.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedEnabledArgs.FeedProviderDefinitionId">
      <summary>Gets the unique identifier for the feed provider associated with the enabled feed, as specified in the app manifest of the feed provider app.</summary>
      <returns>A string containing the ID of the feed provider.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedErrorInfoReportedArgs">
      <summary>Provides data for the OnErrorInfoReported event which is raised when the feeds host reports a feed error.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedErrorInfoReportedArgs.ErrorJson">
      <summary>Gets a JSON string describing the error that triggered the error event.</summary>
      <returns>A JSON string.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedErrorInfoReportedArgs.FeedDefinitionId">
      <summary>Gets the definition ID of the feed associated with the error.</summary>
      <returns>The definition ID of the feed associated with the error.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedErrorInfoReportedArgs.FeedProviderDefinitionId">
      <summary>Gets the definition ID of the feed provider associated with the error.</summary>
      <returns>The definition ID of the feed provider associated with the error.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedManager">
      <summary>Provides methods for feed providers to manage and query feeds.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.FeedManager.GetDefault">
      <summary>Gets the default instance of the FeedManager for the calling feed provider app.</summary>
      <returns>An instance of FeedManager.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.FeedManager.GetEnabledFeedProviders">
      <summary>Gets a list of FeedProviderInfo objects representing the enabled feed providers associated with the calling feed provider app.</summary>
      <returns>A list of enabled feed providers.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.FeedManager.SendMessageToContent(System.String,System.String,System.String)">
      <summary>Send a string message to the web content in a feed being displayed in the feeds host.</summary>
      <param name="feedProviderDefinitionId">The definition ID of the feed provider. This is the value specified in the Id attribute of the FeedProvider element in the provider's package manifest file. For more information see Feed provider package manifest XML format.</param>
      <param name="feedDefinitionId">The definition ID of the feed. This is the value specified in the Id attribute of the Definition element in the provider's package manifest file. For more information see Feed provider package manifest XML format.</param>
      <param name="message">A string containing the message to be sent to the feed web content.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.FeedManager.SetCustomQueryParameters(Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersUpdateOptions)">
      <summary>Sets the query parameters that are passed to the remote feed provider URI when the Widgets Board is requesting feed content. This enables scenarios like passing authentication tokens to the remote service.</summary>
      <param name="options">A CustomQueryParametersUpdateOptions object that conveys the query parameters and related metadata.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.FeedManager.TryShowAnnouncement(System.String,System.String,Microsoft.Windows.Widgets.Notifications.FeedAnnouncement)">
      <summary>Requests that an announcement be shown in the taskbar. The feeds host may or may not show this announcement, based on its policies. If the user interacts with the announcement, the feed provider's OnAnnouncementInvoked(FeedAnnouncementInvokedArgs) method will be called.</summary>
      <param name="feedProviderDefinitionId">The definition ID of the feed provider. This is the value specified in the Id attribute of the FeedProvider element in the provider's package manifest file. For more information see Feed provider package manifest XML format.</param>
      <param name="feedDefinitionId">The definition ID of the feed. This is the value specified in the Id attribute of the Definition element in the provider's package manifest file. For more information see Feed provider package manifest XML format.</param>
      <param name="announcement">A FeedAnnouncement object that provides the data for the announcement to be displayed.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedMessageReceivedArgs">
      <summary>Provides data for the FeedMessageReceivedArgs JavaScript method.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedMessageReceivedArgs.FeedDefinitionId">
      <summary>Gets the definition ID of the feed associated with the message.</summary>
      <returns>The definition ID of the feed associated with the message.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedMessageReceivedArgs.FeedProviderDefinitionId">
      <summary>Gets the definition ID of the feed provider associated with the message.</summary>
      <returns>The definition ID of the feed provider associated with the message.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedMessageReceivedArgs.Message">
      <summary>Gets a string containing the contents of the message that was posted from the feed's content using the window.chrome.webview.postMessage JavaScript method</summary>
      <returns>A string containing the contents of the message</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderDisabledArgs">
      <summary>Provides data for the OnFeedProviderDisabled(FeedProviderDisabledArgs) method.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderDisabledArgs.FeedProviderDefinitionId">
      <summary>Gets the unique identifier of the disabled feed provider, as specified in the app manifest of the feed provider app.</summary>
      <returns>A string containing the ID of the disabled feed provider.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderEnabledArgs">
      <summary>Provides data for the OnFeedProviderEnabled(FeedProviderEnabledArgs) method.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderEnabledArgs.FeedProviderDefinitionId">
      <summary>Gets the unique identifier of the enabled feed provider, as specified in the app manifest of the feed provider app.</summary>
      <returns>A string containing the ID of the enabled feed provider.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderInfo">
      <summary>Provides information about a feed provider.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderInfo.EnabledFeedDefinitionIds">
      <summary>Gets a list of unique identifiers for feeds, as specified in the app manifest of the feed provider app.</summary>
      <returns>A list of strings containing feed IDs.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderInfo.FeedProviderDefinitionId">
      <summary>Gets the unique identifier for the feed provider, as specified in the app manifest of the feed provider app.</summary>
      <returns>A string containing the ID of the feed provider.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequest">
      <summary>Represents a resource request received through an implementation of IFeedResourceProvider.OnResourceRequested.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequest.Content">
      <summary>Gets or sets the stream of content that is provided in a PUT or UPDATE feed resource request.</summary>
      <returns>The stream of content that is provided in a PUT or UPDATE feed resource request.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequest.Headers">
      <summary>Gets or sets a dictionary of header values that can be used to in a feed resource request.</summary>
      <returns>A dictionary of header values.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequest.Method">
      <summary>Gets the HTTP method of the feed resource request.</summary>
      <returns>A string value that may be "GET", "PUT", or "UPDATE".</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequest.Uri">
      <summary>Gets the URI of the requested resource.</summary>
      <returns>The URI of the requested resource.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequestedArgs">
      <summary>Provides data for the FeedResourceRequestedArgs.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequestedArgs.GetDeferral">
      <summary>Informs the system that the resource response task might continue to perform work after the IFeedResourceProvider.OnResourceRequested</summary>
      <returns>A Deferral object.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequestedArgs.FeedDefinitionId">
      <summary>Gets the definition ID of the feed associated with the resource request.</summary>
      <returns>The definition ID of the feed associated with the resource request.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequestedArgs.FeedProviderDefinitionId">
      <summary>Gets the definition ID of the feed provider associated with the resource request.</summary>
      <returns>The definition ID of the feed provider associated with the resource request.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequestedArgs.Request">
      <summary>Sets the FeedResourceRequest representing the feed resource request.</summary>
      <returns>The feed resource request.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequestedArgs.Response">
      <summary>Gets or sets the FeedResourceResponse representing the response to the feed resource request.</summary>
      <returns>The feed resource response.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceResponse">
      <summary>Represents a response to a resource request received through an implementation of IFeedResourceProvider.OnResourceRequested.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceResponse.#ctor(Windows.Storage.Streams.IRandomAccessStreamReference,System.String,System.Int32)">
      <summary>Initializes a new instance of the FeedResourceResponse class.</summary>
      <param name="content">The stream of content that contains the requested resource.</param>
      <param name="reasonPhrase">A text description of the result that is not user facing.</param>
      <param name="statusCode">The HTTP status code of the response.</param>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceResponse.Content">
      <summary>Gets the stream of content that contains the requested resource.</summary>
      <returns>The stream of content that contains the requested resource.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceResponse.Headers">
      <summary>Gets or sets an optional dictionary of header values that may have been returned from a web request by the feed provider.</summary>
      <returns>A dictionary of header values.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceResponse.ReasonPhrase">
      <summary>Gets a text description of the result that is not user facing.</summary>
      <returns>A text description of the result that is not user facing.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceResponse.StatusCode">
      <summary>Gets the HTTP status code of the response.</summary>
      <returns>The HTTP status code of the response.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.IFeedAnnouncementInvokedTarget">
      <summary>The interface implemented by a feed provider to receive notifications when an announcement from the provider is displayed or when a user interacts with it.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedAnnouncementInvokedTarget.OnAnnouncementInvoked(Microsoft.Windows.Widgets.Notifications.FeedAnnouncementInvokedArgs)">
      <summary>Called by the feeds host when a feeds announcement, requested with a call to TryShowAnnouncement(FeedAnnouncement), has been initially displayed or interacted with by the user.</summary>
      <param name="args">A FeedAnnouncementInvokedArgs object conveying identifiers for the announcement and the associated feed and the kind of action that triggered the notification.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.IFeedManager">
      <summary>The interface implemented by the FeedManager class.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedManager.GetEnabledFeedProviders">
      <summary>Gets a list of FeedProviderInfo objects representing the enabled feed providers.</summary>
      <returns>A list of FeedProviderInfo objects.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedManager.SetCustomQueryParameters(Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersUpdateOptions)">
      <summary>Updates the query parameters for a feed.</summary>
      <param name="options">A CustomQueryParametersUpdateOptions containing the identifier for the feed provider and a string containing the query parameters to update.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.IFeedManager2">
      <summary>An interface implemented by the FeedManager to enable additional features.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedManager2.SendMessageToContent(System.String,System.String,System.String)">
      <summary>Interface definition of a method that sends a string message to the web content in a feed being displayed in the feeds host.</summary>
      <param name="feedProviderDefinitionId">The definition ID of the feed provider.</param>
      <param name="feedDefinitionId">The definition ID of the feed.</param>
      <param name="message">The contents of the message.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedManager2.TryShowAnnouncement(System.String,System.String,Microsoft.Windows.Widgets.Notifications.FeedAnnouncement)">
      <summary>Interface definition of a method that requests that an announcement be shown in the feeds host.</summary>
      <param name="feedProviderDefinitionId">The definition ID of the feed provider.</param>
      <param name="feedDefinitionId">The definition ID of the feed.</param>
      <param name="announcement">A FeedAnnouncement object that provides the data for the announcement to be displayed.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProvider">
      <summary>The interface implemented by feed providers for the Windows Widgets Board. The methods of this interface are invoked by the Widgets Board to request custom query string parameters, typically to support authentication scenarios, and to provide telemetry information.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProvider.OnCustomQueryParametersRequested(Microsoft.Windows.Widgets.Feeds.Providers.CustomQueryParametersRequestedArgs)">
      <summary>Notifies the feed provider that the Widgets Board needs the the app to update a feed's custom query parameters. This occurs when a fetch of feed data fails due to due to an expired authentication token was set in a previous call to SetCustomQueryParameters(CustomQueryParametersUpdateOptions).</summary>
      <param name="args">A CustomQueryParametersRequestedArgs that provides the ID for the feed for which query parameters are being requested. The ID is specified in the app manifest file for the feed provider app. For more information, see Feed provider package manifest XML format.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProvider.OnFeedDisabled(Microsoft.Windows.Widgets.Feeds.Providers.FeedDisabledArgs)">
      <summary>Invoked by the Widgets Board when a feed is disabled.</summary>
      <param name="args">A FeedDisabledArgs object that provides the feed ID and feed provider ID of the feed that has been disabled.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProvider.OnFeedEnabled(Microsoft.Windows.Widgets.Feeds.Providers.FeedEnabledArgs)">
      <summary>Invoked by the Widgets Board when a feed is enabled.</summary>
      <param name="args">A FeedEnabledArgs object that provides the feed ID and feed provider ID of the feed that has been enabled.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProvider.OnFeedProviderDisabled(Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderDisabledArgs)">
      <summary>Invoked by the Widgets Board when all of the feeds for this provider have been disabled.</summary>
      <param name="args">A FeedProviderDisabledArgs object that provides the feed provider ID of the feed provider that has been disabled.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProvider.OnFeedProviderEnabled(Microsoft.Windows.Widgets.Feeds.Providers.FeedProviderEnabledArgs)">
      <summary>Invoked when a feed associated with the provider is created by the Widgets Board host.</summary>
      <param name="args">A FeedProviderEnabledArgs object that provides the feed provider ID of the feed provider that has been enabled.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderAnalytics">
      <summary>This optional interface can be implemented by feed providers to receive callbacks from the feeds host for feed analytics events.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderAnalytics.OnAnalyticsInfoReported(Microsoft.Windows.Widgets.Feeds.Providers.FeedAnalyticsInfoReportedArgs)">
      <summary>Raised when the feeds host reports analytics data associated with a user interaction with a feed.</summary>
      <param name="args">A FeedAnalyticsInfoReportedArgs that provides details about the user interaction that triggered the event.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderErrors">
      <summary>This optional interface is implemented by Windows feed providers to receive callbacks from the feeds host for feed errors.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderErrors.OnErrorInfoReported(Microsoft.Windows.Widgets.Feeds.Providers.FeedErrorInfoReportedArgs)">
      <summary>Raised when the feeds host reports a feed error.</summary>
      <param name="args">A FeedErrorInfoReportedArgs that provides details about the error that triggered the event.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderMessage">
      <summary>This optional interface can be implemented by a feed provider to receive string messages from the feed's content that has been posted using the window.chrome.webview.postMessage JavaScript method.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedProviderMessage.OnMessageReceived(Microsoft.Windows.Widgets.Feeds.Providers.FeedMessageReceivedArgs)">
      <summary>Raised when the feed's content posts a message using the window.chrome.webview.postMessage JavaScript method.</summary>
      <param name="args">A FeedMessageReceivedArgs that provides details about the message that triggered the event.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Feeds.Providers.IFeedResourceProvider">
      <summary>This optional interface can be implemented by a feed provider to monitor, provide, or alter resources that are requested by the feed web content.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Feeds.Providers.IFeedResourceProvider.OnResourceRequested(Microsoft.Windows.Widgets.Feeds.Providers.FeedResourceRequestedArgs)">
      <summary>Raised when the feed's content requests a resource that matches the web request filter string for a feed in the WebRequestFilter attribute of the Definition element in the provider's package manifest file. For more information, see Feed provider package manifest XML format.</summary>
      <param name="args">A FeedResourceRequestedArgs containing information about the resource request.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Notifications.AnnouncementActionKind">
      <summary>Specifies the kind of action that triggered the invocation of &lt;cref:Microsoft.Windows.Widgets.Feeds.Providers.IFeedAnnouncementInvokedTarget.OnAnnouncementInvoked(Microsoft.Windows.Widgets.Notifications.FeedAnnouncementInvokedArgs)&gt;.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.Notifications.AnnouncementActionKind.Engaged">
      <summary>The user engaged with the announcement.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.Notifications.AnnouncementActionKind.Shown">
      <summary>The announcement was shown.</summary>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Notifications.AnnouncementTextColor">
      <summary>Specifies the color of text fields in a &lt;xhref:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement&gt;.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.Notifications.AnnouncementTextColor.Accent">
      <summary>The theme accent color.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.Notifications.AnnouncementTextColor.Attention">
      <summary>The theme color indicating an announcement that is requesting attention.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.Notifications.AnnouncementTextColor.Dark">
      <summary>The theme dark color.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.Notifications.AnnouncementTextColor.Default">
      <summary>The default color.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.Notifications.AnnouncementTextColor.Good">
      <summary>The theme color indicating a positive announcement.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.Notifications.AnnouncementTextColor.Light">
      <summary>The theme light color.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.Notifications.AnnouncementTextColor.Warning">
      <summary>The theme color indicating a warning announcement.</summary>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement">
      <summary>Provides data for a feed announcement that a feed provider can request to be displayed by calling &lt;Microsoft.Windows.Widgets.Feeds.Providers.FeedManager.TryShowAnnouncement(System.String,System.String,Microsoft.Windows.Widgets.Notifications.FeedAnnouncement)&gt;.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.#ctor(System.String,System.String,System.String,Windows.Foundation.Uri,Windows.Foundation.Uri)">
      <summary>Initializes a new instance of the FeedAnnouncement class.</summary>
      <param name="id">A required unique identifier for the announcement.</param>
      <param name="primaryText">The required primary text for the announcement.</param>
      <param name="secondaryText">The required secondary text for the announcement.</param>
      <param name="lightModeIcon">The required icon URI for the light mode OS theme.</param>
      <param name="darkModeIcon">The required icon URI for the dark mode OS theme.</param>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.CustomAccessibilityText">
      <summary>Gets or sets custom accessibility text is announced by screen readers.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.DarkModeIconUri">
      <summary>Gets or sets the required icon URI for the dark mode OS theme, which will be displayed on the notification if the current OS theme is dark.</summary>
      <returns>The dark mode icon URI.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.Duration">
      <summary>Gets or sets an optional duration value, specifying how long the announcement will be displayed.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.ExpirationTime">
      <summary>Gets or sets the optional expiration time for the announcement.</summary>
      <returns>The expiration time for the announcement.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.Id">
      <summary>Gets or sets a required unique identifier for the announcement.</summary>
      <returns>The unique identifier for the announcement.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.IsSecondaryTextSubtle">
      <summary>Gets or sets a value indicating wether the SecondaryText string is displayed with subtle formatting to appear less prominent.</summary>
      <returns>True if the secondary text should be displayed with subtle formatting; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.LightModeIconUri">
      <summary>Gets or sets the required icon URI for the light mode OS theme, which will be displayed on the notification if the current OS theme is light.</summary>
      <returns>The light mode icon URI.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.PrimaryText">
      <summary>Gets or sets the required primary text for the announcement.</summary>
      <returns>The primary text for the announcement.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.PrimaryTextColor">
      <summary>Gets or sets the color of the PrimaryText string.</summary>
      <returns>The color of the primary text.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.SecondaryText">
      <summary>Gets or sets the required secondary text for the announcement.</summary>
      <returns>The secondary text for the announcement.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.SecondaryTextColor">
      <summary>Gets or sets the color of the SecondaryText string.</summary>
      <returns>The color of the secondary text.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncement.ShowBadgeIfUserNotEngaged">
      <summary>Gets or sets a value field indicating whether a badge should be displayed on the baseline announcement if the user has not engaged with the announcement. Default value will be false.</summary>
      <returns>True if a badge should be displayed on the baseline announcement; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Notifications.FeedAnnouncementInvokedArgs" />
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncementInvokedArgs.ActionKind" />
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncementInvokedArgs.AnnouncementId" />
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncementInvokedArgs.FeedDefinitionId" />
    <member name="P:Microsoft.Windows.Widgets.Notifications.FeedAnnouncementInvokedArgs.FeedProviderDefinitionId" />
    <member name="T:Microsoft.Windows.Widgets.Providers.IWidgetManager">
      <summary>The interface that is implemented by the WidgetManager class, which provides methods for querying, updating and deleting widgets.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetManager.DeleteWidget(System.String)">
      <summary>Interface definition of the DeleteWidget(String) method.</summary>
      <param name="widgetId">A widget ID.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetManager.GetWidgetIds">
      <summary>Interface definition of the GetWidgetIds method.</summary>
      <returns>An array of widget IDs.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetManager.GetWidgetInfo(System.String)">
      <summary>Interface definition of the GetWidgetInfo(String) method.</summary>
      <param name="widgetId">The widget ID.</param>
      <returns>A WidgetInfo object.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetManager.GetWidgetInfos">
      <summary>Interface definition of the GetWidgetInfos method.</summary>
      <returns>An array of WidgetInfo objects.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetManager.UpdateWidget(Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions)">
      <summary>Interface definition of the UpdateWidget(WidgetUpdateRequestOptions) method.</summary>
      <param name="widgetUpdateRequestOptions">A WidgetUpdateRequestOptions object.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.IWidgetProvider">
      <summary>This interface is implemented by Windows Widget providers to receive callbacks from the widget host for widget lifetime events.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetProvider.Activate(Microsoft.Windows.Widgets.Providers.WidgetContext)">
      <summary>Notifies the widget provider that the widget host is currently interested in receiving updated content from the provider.</summary>
      <param name="widgetContext">A WidgetContext object that identifies the widget that is being activated and provides configuration information.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetProvider.CreateWidget(Microsoft.Windows.Widgets.Providers.WidgetContext)">
      <summary>Notifies the widget provider that a new widget has been created, for example, when a user adds a widget to a widget host.</summary>
      <param name="widgetContext">A WidgetContext object that identifies the widget that is being created and provides information about the configuration of the widget.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetProvider.Deactivate(System.String)">
      <summary>Notifies the widget provider that the widget host is no longer actively requesting updated content from the provider.</summary>
      <param name="widgetId">The unique identifier of the widget that is being deactivated. The widget ID value is dynamically generated by the WidgetManager. The widget ID remains the same for a widget from the moment of its creation until the moment the Widget is deleted. The widget ID is a unique value across all widgets and all widget providers.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetProvider.DeleteWidget(System.String,System.String)">
      <summary>Notifies the widget provider that one of the widgets it supports has been deleted, for example, when a user removes a widget from a widget host.</summary>
      <param name="widgetId">The unique identifier of the widget that is being unpinned. The widget ID value is dynamically generated by the WidgetManager. The widget ID remains the same for a widget from the moment of its creation until the moment the Widget is deleted. The widget ID is a unique value across all widgets and all widget providers.</param>
      <param name="customState">A string containing the custom state of the widget that has been unpinned.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetProvider.OnActionInvoked(Microsoft.Windows.Widgets.Providers.WidgetActionInvokedArgs)">
      <summary>Called when an action is invoked on a widget, such as the user clicking on a button.</summary>
      <param name="actionInvokedArgs">A WidgetActionInvokedArgs object providing data for the callback.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetProvider.OnWidgetContextChanged(Microsoft.Windows.Widgets.Providers.WidgetContextChangedArgs)">
      <summary>Called when the configuration of the widget within the widget host changes.</summary>
      <param name="contextChangedArgs">A WidgetContextChangedArgs object providing data for the callback.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.IWidgetProvider2">
      <summary>This interface is implemented by Windows widget providers to receive callbacks from the widget host for widget customization events.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetProvider2.OnCustomizationRequested(Microsoft.Windows.Widgets.Providers.WidgetCustomizationRequestedArgs)">
      <summary>Raised when the user has requested widget customization through the widget host UI.</summary>
      <param name="customizationRequestedArgs">A WidgetCustomizationRequestedArgs object providing access to the widget context and custom state.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.IWidgetProviderAnalytics">
      <summary>This optional interface can be implemented by Windows widget providers to receive callbacks from the widget host for widget analytics events.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetProviderAnalytics.OnAnalyticsInfoReported(Microsoft.Windows.Widgets.Providers.WidgetAnalyticsInfoReportedArgs)">
      <summary>Raised when the widget host reports analytics data associated with a user interaction with a widget.</summary>
      <param name="args">A WidgetAnalyticsInfoReportedArgs that provides details about the user interaction that triggered the event.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.IWidgetProviderErrors">
      <summary>This interface is implemented by Windows widget providers to receive callbacks from the widget host for widget errors.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.IWidgetProviderErrors.OnErrorInfoReported(Microsoft.Windows.Widgets.Providers.WidgetErrorInfoReportedArgs)">
      <summary>Raised when the widget host reports a widget error.</summary>
      <param name="args">A WidgetErrorInfoReportedArgs that provides details about the error that triggered the event.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.WidgetActionInvokedArgs">
      <summary>Provides data for the OnActionInvoked(WidgetActionInvokedArgs) callback, which is raised when an action is invoked on a widget, such as the user clicking on a button.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetActionInvokedArgs.CustomState">
      <summary>Gets a string containing custom state for the widget associated with the action.</summary>
      <returns>A string containing custom state for the widget associated with the action.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetActionInvokedArgs.Data">
      <summary>Gets a JSON string containing key/value pairs representing the data associated with the action.</summary>
      <returns>A JSON string containing representing the data associated with the action.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetActionInvokedArgs.Verb">
      <summary>Gets a string that indicates the verb associated with the associated action.</summary>
      <returns>A string that indicates the verb associated with the associated action.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetActionInvokedArgs.WidgetContext">
      <summary>Gets a WidgetContext object containing information about the configuration of the associated widget in the widget host.</summary>
      <returns>A WidgetContext object containing information about the configuration of the associated widget in the widget host.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.WidgetAnalyticsInfoReportedArgs">
      <summary>Provides data for the OnAnalyticsInfoReported event which is raised when the widget host reports analytics data associated with a user interaction with a widget.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetAnalyticsInfoReportedArgs.AnalyticsJson">
      <summary>Gets a JSON string describing the user interaction that triggered the analytics event.</summary>
      <returns>A JSON string.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetAnalyticsInfoReportedArgs.WidgetContext">
      <summary>Gets the WidgetContext object associated with the analytics info. This object provides information about the configuration of a widget within the widget host.</summary>
      <returns>The WidgetContext object.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.WidgetContext">
      <summary>Provides information about the configuration of a widget within the widget host.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetContext.DefinitionId">
      <summary>Gets the identifier registered by the widget provider to categorize the widget.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetContext.Id">
      <summary>Gets the ID of the widget associated with the WidgetContext.</summary>
      <returns>The ID of the widget associated with the WidgetContext.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetContext.IsActive">
      <summary>Gets the activation state of the widget.</summary>
      <returns>True if the widget is active; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetContext.Size">
      <summary>Gets a value indicating the currently displayed size of the widget.</summary>
      <returns>A &lt;Microsoft.Windows.Widgets.WidgetSize&gt; indicating the currently displayed size of the widget.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.WidgetContextChangedArgs">
      <summary>Provides data for the OnWidgetContextChanged(WidgetContextChangedArgs) callback.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetContextChangedArgs.WidgetContext">
      <summary>Gets the WidgetContext object associated with the OnWidgetContextChanged(WidgetContextChangedArgs) callback.</summary>
      <returns>A WidgetContext object.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.WidgetCustomizationRequestedArgs">
      <summary>Provides data for the OnCustomizationRequested event which is raised when the user has requested widget customization through the widget host UI.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetCustomizationRequestedArgs.CustomState">
      <summary>Gets a string containing custom state for the widget associated with the customization request.</summary>
      <returns>A string containing custom state for the widget associated with the action.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetCustomizationRequestedArgs.WidgetContext">
      <summary>Gets the WidgetContext object associated with the customization request. This object provides information about the configuration of a widget within the widget host.</summary>
      <returns>The WidgetContext object.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.WidgetErrorInfoReportedArgs">
      <summary>Provides data for the OnErrorInfoReported event which is raised when the widget host reports a widget error.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetErrorInfoReportedArgs.ErrorJson">
      <summary>Gets a JSON string describing the error that triggered the error event.</summary>
      <returns>A JSON string.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetErrorInfoReportedArgs.WidgetContext">
      <summary>Gets the WidgetContext object associated with the error. This object provides information about the configuration of a widget within the widget host.</summary>
      <returns>The WidgetContext object.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.WidgetInfo">
      <summary>Contains status information for a widget, including the visual template, data template, custom state, the last update time, and context information from the widget host.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetInfo.CustomState">
      <summary>Gets a string containing the current Adaptive Card JSON data template for the widget.</summary>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetInfo.Data">
      <summary>Gets a string containing the current Adaptive Card JSON data template for the widget.</summary>
      <returns>A JSON string.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetInfo.LastUpdateTime">
      <summary>Gets the time that the widget was last updated.</summary>
      <returns>The time that the widget was last updated.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetInfo.Template">
      <summary>Gets a string containing the current Adaptive Card JSON visual template for the widget.</summary>
      <returns>A JSON string.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetInfo.WidgetContext">
      <summary>Gets a WidgetContext object containing information about the configuration of a widget within the widget host.</summary>
      <returns>A WidgetContext object.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.WidgetManager">
      <summary>Provides methods for querying, updating, and deleting widgets.</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.WidgetManager.DeleteWidget(System.String)">
      <summary>Requests the removal of a widget from the widget host.</summary>
      <param name="widgetId">The unique identifier of the widget to remove. The widget ID value is dynamically generated by the WidgetManager. The widget ID remains the same for a widget from the moment of its creation until the moment the Widget is deleted. The widget ID is a unique value across all widgets and all widget providers.</param>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.WidgetManager.GetDefault">
      <summary>Gets an instance of the WidgetManager class.</summary>
      <returns>An instance of WidgetManager.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.WidgetManager.GetWidgetIds">
      <summary>Gets all of the widget IDs for widgets associated with the calling provider app.</summary>
      <returns>An array of strings containing the widget IDs for widgets associated with the calling provider app.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.WidgetManager.GetWidgetInfo(System.String)">
      <summary>Gets a WidgetInfo object containing information about the widget with the provided widget ID, including the visual template, data template, custom state, the last update time, and context information from the widget host.</summary>
      <param name="widgetId">The unique identifier of the widget for which information is retrieved.</param>
      <returns>A WidgetInfo object, if the specified ID is associated with a widget associated with the calling app that has not been deleted; otherwise, null.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.WidgetManager.GetWidgetInfos">
      <summary>Gets the stored information for all widgets associated with the calling app.</summary>
      <returns>An array of WidgetInfo objects containing information about the associated widgets.</returns>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.WidgetManager.UpdateWidget(Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions)">
      <summary>Provides updated content for a widget to the widget host.</summary>
      <param name="widgetUpdateRequestOptions">A WidgetUpdateRequestOptions object that contains the content with which to update the widget.</param>
    </member>
    <member name="T:Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions">
      <summary>Provides data for calls to UpdateWidget(WidgetUpdateRequestOptions).</summary>
    </member>
    <member name="M:Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions.#ctor(System.String)">
      <summary>Initializes a new instance of the WidgetRequestOptions class.</summary>
      <param name="widgetId">The widget ID of the widget to be updated.</param>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions.CustomState">
      <summary>Gets or sets a string representing the custom state for the associated widget.</summary>
      <returns>A string representing the custom state for the associated widget.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions.Data">
      <summary>Gets or sets the Adaptive Card data template for the associated widget.</summary>
      <returns>A JSON string containing the Adaptive Card data template.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions.Template">
      <summary>Gets or sets the Adaptive Card visual template for the associated widget.</summary>
      <returns>A JSON string containing the Adaptive Card visual template.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions.UnsetValue">
      <summary>Gets a sentinel value indicating that the associated property is unset.</summary>
      <returns>A sentinel value indicating that the associated property is unset.</returns>
    </member>
    <member name="P:Microsoft.Windows.Widgets.Providers.WidgetUpdateRequestOptions.WidgetId">
      <summary>Gets the widget ID of the widget to be updated.</summary>
      <returns>The widget ID of the widget to be updated.</returns>
    </member>
    <member name="T:Microsoft.Windows.Widgets.WidgetSize">
      <summary>Specifies the display size of a widget within the widget host.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.WidgetSize.Large">
      <summary>Large.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.WidgetSize.Medium">
      <summary>Medium.</summary>
    </member>
    <member name="F:Microsoft.Windows.Widgets.WidgetSize.Small">
      <summary>Small.</summary>
    </member>
  </members>
</doc>