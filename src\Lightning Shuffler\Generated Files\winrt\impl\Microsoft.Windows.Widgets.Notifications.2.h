// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Widgets_Notifications_2_H
#define WINRT_Microsoft_Windows_Widgets_Notifications_2_H
#include "winrt/impl/Windows.Foundation.1.h"
#include "winrt/impl/Microsoft.Windows.Widgets.Notifications.1.h"
WINRT_EXPORT namespace winrt::Microsoft::Windows::Widgets::Notifications
{
    struct WINRT_IMPL_EMPTY_BASES FeedAnnouncement : winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncement
    {
        FeedAnnouncement(std::nullptr_t) noexcept {}
        FeedAnnouncement(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncement(ptr, take_ownership_from_abi) {}
        FeedAnnouncement(param::hstring const& id, param::hstring const& primaryText, param::hstring const& secondaryText, winrt::Windows::Foundation::Uri const& lightModeIcon, winrt::Windows::Foundation::Uri const& darkModeIcon);
    };
    struct WINRT_IMPL_EMPTY_BASES FeedAnnouncementInvokedArgs : winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementInvokedArgs
    {
        FeedAnnouncementInvokedArgs(std::nullptr_t) noexcept {}
        FeedAnnouncementInvokedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Notifications::IFeedAnnouncementInvokedArgs(ptr, take_ownership_from_abi) {}
    };
}
#endif
