﻿<!--
  Copyright (c) Microsoft Corporation. Licensed under the MIT License
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <DefaultDpiAwareSettings>PerMonV2</DefaultDpiAwareSettings>
    <XamlCompilerPropsAndTargetsDirectory Condition="'$(XamlCompilerPropsAndTargetsDirectory)' == ''">$(MSBuildThisFileDirectory)</XamlCompilerPropsAndTargetsDirectory>
    <!-- Set this to some value so that a check for the file existing will fail and won't be imported. -->
    <MsAppxPackageTargets Condition="'$(UsingMicrosoftNETSdk)'=='true'">WinUI3-NET5-Projects-Dont-Use-MsAppxPackageTargets</MsAppxPackageTargets>

    <!--
      The Single-project MSIX Packaging tooling needs to be imported before 
      microsoft.common.currentversion.targets has a chance to import the built-in 
      Microsoft.AppxPackage.targets file. This props file won't actually do anything if EnableMsixTooling
      isn't set to true. Doing this pattern lets users just set the property in their project file, without 
      needing to create a Directory.Build.props file and placing it there. If this doesn't work for a customer 
      because they have their own CustomBeforeMicrosoftCommonTargets (which is incredibly unlikely), we can 
      give them an escape hatch of allowing them to specify the property in a Directory.Build.props.
    -->
    <CustomBeforeMicrosoftCommonTargets Condition="'$(EnableMsixTooling)'=='' or '$(EnablePreviewMsixTooling)'=='true'">$(MSBuildThisFileDirectory)Microsoft.Build.Msix.props</CustomBeforeMicrosoftCommonTargets>
  </PropertyGroup>
  <Import Condition="'$(EnableMsixTooling)'=='true' or '$(EnablePreviewMsixTooling)'=='true'" Project="$(MSBuildThisFileDirectory)Microsoft.Build.Msix.props"/>
  <Import Project="$(XamlCompilerPropsAndTargetsDirectory)Microsoft.UI.Xaml.Markup.Compiler.props" Condition="'$(MSBuildProjectExtension)'!='.wapproj'" />

  <!--
    Enable default items for .NET projects that are using the SDK-style projects

    WPF project also has App.xaml, and it's handled by Microsoft.NET.Sdk.WindowsDesktop.props. We should not handle WPF project here.
    For UWP projects, this is already handled by the a VS .targets file, so disabling this to avoid build errors due to conflicts.
  -->
  <ItemGroup Condition="'$(EnableDefaultItems)' == 'true' and '$(UsingMicrosoftNETSdk)' == 'true' and '$(UseWPF)' != 'true' and '$(UseUwpTools)' != 'true'">
    <ApplicationDefinition Include="App.xaml"
                           Condition="'$(EnableDefaultApplicationDefinition)' != 'false' And Exists('$(MSBuildProjectDirectory)/App.xaml')">
      <Generator>MSBuild:Compile</Generator>
      <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
    </ApplicationDefinition>

    <Page Include="**/*.xaml"
          Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder);@(ApplicationDefinition)"
          Condition="'$(EnableDefaultPageItems)' != 'false'" >
      <Generator>MSBuild:Compile</Generator>
      <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
    </Page>

    <None Remove="**/*.xaml"
          Condition="'$(EnableDefaultApplicationDefinition)' != 'false' And '$(EnableDefaultPageItems)' != 'false'" />

    <Content Include="Assets/**/*.*" Condition="'$(EnableDefaultAssets)'=='true'"/>
  </ItemGroup>

  <!-- Specifically for UWP XAML apps using modern .NET, we also want to include assets automaticaly, to match what
       WinUI also does. This cannot be done in a VS .targets, because it does not work in WAP scenarios. So, we do
       this here instead, so that if users are referencing WinAppSDK from a UWP XAML app (which they would only do
       for the single-project MSIX support), they they can also get this additional convenience. Doing this also
       means they won't have to make any changes here when moving from UWP to WinAppSDK. -->
  <ItemGroup Condition="'$(EnableDefaultItems)' == 'true'
                        and '$(EnableDefaultAssets)' != 'false'
                        and '$(UsingMicrosoftNETSdk)' == 'true'
                        and '$(UseUwpTools)' == 'true'">
    <Content Include="Assets/**/*.*" />
  </ItemGroup>

  <Import Project="$(MSBuildThisFileDirectory)Microsoft.WinUI.ProjectCapabilities.props" />

</Project>