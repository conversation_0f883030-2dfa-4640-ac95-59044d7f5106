﻿<?xml version="1.0" encoding="utf-8"?>
<Window
    x:Class="Lightning_Shuffler.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Lightning_Shuffler"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Lightning Shuffler">

    <Grid Background="{StaticResource DarkBackgroundBrush}">
        <Grid.ColumnDefinitions>
            <!-- Sidebar for playlists and queue -->
            <ColumnDefinition Width="350"/>
            <!-- Main content area -->
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar -->
        <Border Grid.Column="0"
                Background="{StaticResource DarkSurfaceBrush}"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,1,0">
            <Grid>
                <Grid.RowDefinitions>
                    <!-- Search bar -->
                    <RowDefinition Height="Auto"/>
                    <!-- Playlist management -->
                    <RowDefinition Height="Auto"/>
                    <!-- Queue/Video list -->
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Search Bar -->
                <Border Grid.Row="0"
                        Padding="16,16,16,8">
                    <TextBox x:ConnectionId='20' x:Name="SearchBox"
                             Style="{StaticResource ModernTextBoxStyle}"
                             PlaceholderText="Search videos..."
                                                                >
                        <TextBox.Resources>
                            <Style TargetType="TextBox"
                                    BasedOn="{StaticResource ModernTextBoxStyle}">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="TextBox">
                                            <Border x:Name="BorderElement"
                                                    Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="{TemplateBinding CornerRadius}">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- Search Icon -->
                                                    <FontIcon Grid.Column="0"
                                                              Glyph="&#xE721;"
                                                              FontSize="16"
                                                              Foreground="{StaticResource TextSecondaryBrush}"
                                                              Margin="12,0,8,0"
                                                              VerticalAlignment="Center"/>

                                                    <ScrollViewer Grid.Column="1"
                                                                  x:Name="ContentElement"
                                                                  Padding="{TemplateBinding Padding}"
                                                                  HorizontalScrollMode="Disabled"
                                                                  HorizontalScrollBarVisibility="Disabled"
                                                                  VerticalScrollMode="Disabled"
                                                                  VerticalScrollBarVisibility="Disabled"
                                                                  IsTabStop="False"
                                                                  ZoomMode="Disabled"/>
                                                </Grid>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </TextBox.Resources>
                    </TextBox>
                </Border>

                <!-- Playlist Management Section -->
                <Border Grid.Row="1"
                        Padding="16,8">
                    <StackPanel>
                        <TextBlock Text="Playlists"
                                   Foreground="{StaticResource TextPrimaryBrush}"
                                   FontSize="18"
                                   FontWeight="SemiBold"
                                   Margin="0,0,0,12"/>

                        <Button x:ConnectionId='18' x:Name="AddPlaylistButton"
                                Style="{StaticResource AccentButtonStyle}"
                                Content="Add Playlist"
                                HorizontalAlignment="Stretch"
                                                               
                                Margin="0,0,0,8"/>

                        <Button x:ConnectionId='19' x:Name="CreateMixButton"
                                Style="{StaticResource ModernButtonStyle}"
                                Content="Create Mix"
                                HorizontalAlignment="Stretch"
                                                             />
                    </StackPanel>
                </Border>

                <!-- Queue/Video List -->
                <Border Grid.Row="2"
                        Padding="16,8,16,16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0"
                                   Text="Queue"
                                   Foreground="{StaticResource TextPrimaryBrush}"
                                   FontSize="16"
                                   FontWeight="SemiBold"
                                   Margin="0,0,0,8"/>

                        <ListView x:ConnectionId='16' Grid.Row="1"
                                  x:Name="QueueListView"
                                  Background="Transparent"
                                  SelectionMode="Single"
                                  ItemContainerStyle="{StaticResource ModernListViewItemStyle}"
                                                                                   >
                            <ListView.ItemTemplate>
                                <DataTemplate>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Thumbnail -->
                                        <Border Grid.Column="0"
                                                Width="50"
                                                Height="30"
                                                CornerRadius="4"
                                                Background="{StaticResource DarkSurfaceVariantBrush}"
                                                Margin="0,0,8,0">
                                            <Image Source="{Binding ThumbnailUrl}"
                                                   Stretch="UniformToFill"/>
                                        </Border>

                                        <!-- Video Info -->
                                        <StackPanel Grid.Column="1"
                                                    VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Title}"
                                                       Foreground="{StaticResource TextPrimaryBrush}"
                                                       FontSize="13"
                                                       FontWeight="Medium"
                                                       TextTrimming="CharacterEllipsis"
                                                       MaxLines="1"/>
                                            <TextBlock Text="{Binding Author}"
                                                       Foreground="{StaticResource TextSecondaryBrush}"
                                                       FontSize="11"
                                                       TextTrimming="CharacterEllipsis"
                                                       MaxLines="1"
                                                       Margin="0,2,0,0"/>
                                        </StackPanel>
                                    </Grid>
                                </DataTemplate>
                            </ListView.ItemTemplate>
                        </ListView>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <!-- Video Player -->
                <RowDefinition Height="*"/>
                <!-- Controls -->
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Video Player Area -->
            <Border Grid.Row="0"
                    Padding="24">
                <Border x:ConnectionId='14' x:Name="VideoPlayerBorder"
                        CornerRadius="12"
                        Background="{StaticResource DarkSurfaceVariantBrush}"
                        BorderBrush="{StaticResource BorderBrush}"
                        BorderThickness="1">
                    <Grid>
                        <!-- WebView2 for YouTube playback will be added here -->
                        <Border x:ConnectionId='15' x:Name="PlaceholderBorder"
                                Background="{StaticResource DarkSurfaceVariantBrush}"
                                CornerRadius="12">
                            <StackPanel HorizontalAlignment="Center"
                                        VerticalAlignment="Center">
                                <FontIcon Glyph="&#xE768;"
                                          FontSize="64"
                                          Foreground="{StaticResource LightningGreenBrush}"
                                          Margin="0,0,0,16"/>
                                <TextBlock Text="Lightning Shuffler"
                                           Foreground="{StaticResource TextPrimaryBrush}"
                                           FontSize="24"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"/>
                                <TextBlock Text="Add a playlist to get started"
                                           Foreground="{StaticResource TextSecondaryBrush}"
                                           FontSize="14"
                                           HorizontalAlignment="Center"
                                           Margin="0,8,0,0"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </Border>
            </Border>

            <!-- Media Controls -->
            <Border Grid.Row="1"
                    Background="{StaticResource DarkSurfaceBrush}"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="0,1,0,0"
                    Padding="24,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Current Video Info -->
                    <StackPanel Grid.Column="0"
                                VerticalAlignment="Center">
                        <TextBlock x:ConnectionId='12' x:Name="CurrentVideoTitle"
                                   Text="No video playing"
                                   Foreground="{StaticResource TextPrimaryBrush}"
                                   FontSize="14"
                                   FontWeight="SemiBold"
                                   TextTrimming="CharacterEllipsis"/>
                        <TextBlock x:ConnectionId='13' x:Name="CurrentVideoAuthor"
                                   Text=""
                                   Foreground="{StaticResource TextSecondaryBrush}"
                                   FontSize="12"
                                   TextTrimming="CharacterEllipsis"
                                   Margin="0,2,0,0"/>
                    </StackPanel>

                    <!-- Media Control Buttons -->
                    <StackPanel Grid.Column="1"
                                Orientation="Horizontal"
                                HorizontalAlignment="Center"
                                Spacing="12">

                        <Button x:ConnectionId='5' x:Name="PreviousButton"
                                Style="{StaticResource ModernButtonStyle}"
                                Width="48"
                                Height="48"
                                CornerRadius="24"
                                                            >
                            <FontIcon Glyph="&#xE892;"
                                    FontSize="16"/>
                        </Button>

                        <Button x:ConnectionId='6' x:Name="PlayPauseButton"
                                Style="{StaticResource AccentButtonStyle}"
                                Width="56"
                                Height="56"
                                CornerRadius="28"
                                                             >
                            <FontIcon x:ConnectionId='11' x:Name="PlayPauseIcon"
                                    Glyph="&#xE768;"
                                    FontSize="20"/>
                        </Button>

                        <Button x:ConnectionId='7' x:Name="NextButton"
                                Style="{StaticResource ModernButtonStyle}"
                                Width="48"
                                Height="48"
                                CornerRadius="24"
                                                        >
                            <FontIcon Glyph="&#xE893;"
                                    FontSize="16"/>
                        </Button>

                        <Button x:ConnectionId='8' x:Name="ShuffleButton"
                                Style="{StaticResource ModernButtonStyle}"
                                Width="48"
                                Height="48"
                                CornerRadius="24"
                                                           >
                            <FontIcon Glyph="&#xE8B1;"
                                    FontSize="16"/>
                        </Button>

                        <Button x:ConnectionId='9' x:Name="LoopButton"
                                Style="{StaticResource ModernButtonStyle}"
                                Width="48"
                                Height="48"
                                CornerRadius="24"
                                                        
                                                                    >
                            <Grid>
                                <FontIcon Glyph="&#xE8EE;"
                                        FontSize="16"/>
                                <TextBlock x:ConnectionId='10' x:Name="LoopCountText"
                                           Text=""
                                           Foreground="{StaticResource TextPrimaryBrush}"
                                           FontSize="10"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           Visibility="Collapsed"/>
                            </Grid>
                        </Button>

                    </StackPanel>

                    <!-- Volume and Additional Controls -->
                    <StackPanel Grid.Column="2"
                                Orientation="Horizontal"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Center"
                                Spacing="8">

                        <Button x:ConnectionId='2' x:Name="VolumeButton"
                                Style="{StaticResource ModernButtonStyle}"
                                Width="40"
                                Height="40"
                                CornerRadius="20"
                                                          >
                            <FontIcon x:ConnectionId='4' x:Name="VolumeIcon"
                                    Glyph="&#xE767;"
                                    FontSize="14"/>
                        </Button>

                        <Slider x:ConnectionId='3' x:Name="VolumeSlider"
                                Width="80"
                                Minimum="0"
                                Maximum="100"
                                Value="50"
                                VerticalAlignment="Center"
                                                                        />

                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>

