<doc>
  <assembly>
    <name>Microsoft.Windows.AppNotifications.Builder.Projection</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationAudioLooping">
      <summary>Specifies the looping behavior for app notification audio.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationAudioLooping.Loop">
      <summary>Audio is looped.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationAudioLooping.None">
      <summary>Audio is not looped.</summary>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder">
      <summary>Provides APIs for creating the XML string that defines the UI for app notifications.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.#ctor">
      <summary>Initializes a new instance of the AppNotificationBuilder class.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.AddArgument(System.String,System.String)">
      <summary>Adds an argument composed of a key/value pair to the XML payload for an app notification.</summary>
      <param name="key">A string containing the key of the argument.</param>
      <param name="value">A string containing the value of the argument.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.AddButton(Microsoft.Windows.AppNotifications.Builder.AppNotificationButton)">
      <summary>Adds a button to the app notification.</summary>
      <param name="value">An AppNotificationButton object representing the button to be added to the notification.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.AddComboBox(Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox)">
      <summary>Adds a combo box to the XML payload for an app notification.</summary>
      <param name="value">An AppNotificationComboBox object representing the combo box to be added to the notification.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.AddProgressBar(Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar)">
      <summary>Adds a progress bar to the XML payload for an app notification.</summary>
      <param name="value">An AppNotificationProgressBar representing the progress bar to be added to the notification.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.AddText(System.String,Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties)">
      <summary>Adds a block of text, with display and localization options, to the XML payload for an app notification.</summary>
      <param name="text">A string containing the text to be displayed on the app notification.</param>
      <param name="properties">An AppNotificationTextProperties specifying display and localization properties for the text.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.AddText(System.String)">
      <summary>Adds a block of text to the XML payload for an app notification.</summary>
      <param name="text">A string containing the text to be displayed on the app notification.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.AddTextBox(System.String,System.String,System.String)">
      <summary>Adds a text box with the specified placeholder text and title to the XML payload for an app notification.</summary>
      <param name="id">The ID of the text box.</param>
      <param name="placeHolderText">The placeholder text displayed in the text box.</param>
      <param name="title">The title text which is displayed above the text box.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.AddTextBox(System.String)">
      <summary>Adds a text box to the XML payload for an app notification.</summary>
      <param name="id">The ID of the text box.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.BuildNotification">
      <summary>Returns an AppNotification object representing the XML payload for an app notification.</summary>
      <returns>An AppNotification object.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.IsUrgentScenarioSupported">
      <summary>Returns a value indicating whether the urgent app notification scenario is supported on the current device.</summary>
      <returns>True if the current device supports the urgent app notification scenario; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.MuteAudio">
      <summary>Requests that the system mute any audio associated with the app notification.</summary>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetAppLogoOverride(Windows.Foundation.Uri,Microsoft.Windows.AppNotifications.Builder.AppNotificationImageCrop,System.String)">
      <summary>Sets the image that is displayed on the left side of an app notification, using the specified cropping method and alternate text.</summary>
      <param name="imageUri">The URI of the app logo override image file.</param>
      <param name="imageCrop">A member of the AppNotificationImageCrop enumeration specifying the cropping method.</param>
      <param name="alternateText">A string containing the alternate text for the app logo override image.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetAppLogoOverride(Windows.Foundation.Uri,Microsoft.Windows.AppNotifications.Builder.AppNotificationImageCrop)">
      <summary>Sets the image that is displayed on the left side of an app notification, using the specified cropping method.</summary>
      <param name="imageUri">The URI of the app logo override image file.</param>
      <param name="imageCrop">A member of the AppNotificationImageCrop enumeration specifying the cropping method.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetAppLogoOverride(Windows.Foundation.Uri)">
      <summary>Sets the image that is displayed on the left side of an app notification.</summary>
      <param name="imageUri">The URI of the app logo override image file.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetAttributionText(System.String,System.String)">
      <summary>Sets the attribution text for an app notification.</summary>
      <param name="text">The attribution text.</param>
      <param name="language">A string containg an IETF language tag specifying the language of the attribution text.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetAttributionText(System.String)">
      <summary>Sets the attribution text for an app notification.</summary>
      <param name="text">The attribution text.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetAudioEvent(Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent,Microsoft.Windows.AppNotifications.Builder.AppNotificationAudioLooping)">
      <summary>Sets the audio event and looping behavior for an app notification.</summary>
      <param name="appNotificationSoundEvent">A member of the AppNotificationSoundEvent enumeration specifying a system sound to play.</param>
      <param name="loop">A member of the AppNotificationAudioLooping enumeration specifying the looping behavior for the audio.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetAudioEvent(Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent)">
      <summary>Sets the audio event for an app notification.</summary>
      <param name="appNotificationSoundEvent">A member of the AppNotificationSoundEvent enumeration specifying a system sound to play.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetAudioUri(Windows.Foundation.Uri,Microsoft.Windows.AppNotifications.Builder.AppNotificationAudioLooping)">
      <summary>Sets the sound file and the audio looping behavior that is played when an app notification is displayed.</summary>
      <param name="audioUri">The URI of the audio file.</param>
      <param name="loop">A member of the AppNotificationAudioLooping enumeration specifying the looping behavior for the audio.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetAudioUri(Windows.Foundation.Uri)">
      <summary>Sets the sound file that is played when an app notification is displayed.</summary>
      <param name="audioUri">The URI of the audio file.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetDuration(Microsoft.Windows.AppNotifications.Builder.AppNotificationDuration)">
      <summary>Sets the duration for an app notification.</summary>
      <param name="duration">A value from the AppNotificationDuration enumeration specifying the duration for the app notification.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetGroup(System.String)">
      <summary>Sets the group identifier for an app notification.</summary>
      <param name="group">A string identifying the group to which the app notification belongs.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetHeroImage(Windows.Foundation.Uri,System.String)">
      <summary>Sets the image that is displayed at the top of an app notification, taking up the full width of the notification, with the specified alternate text.</summary>
      <param name="imageUri">The URI of the hero image file.</param>
      <param name="alternateText">A string containing the alternate text for the hero image.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetHeroImage(Windows.Foundation.Uri)">
      <summary>Sets the image that is displayed at the top of an app notification, taking up the full width of the notification.</summary>
      <param name="imageUri">The URI of the hero image file.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetInlineImage(Windows.Foundation.Uri,Microsoft.Windows.AppNotifications.Builder.AppNotificationImageCrop,System.String)">
      <summary>Sets the image that is displayed inline, after any text elements, filling the full width of the visual area in an app notification, using the specified cropping behavior.</summary>
      <param name="imageUri">The URI of the inline image file.</param>
      <param name="imagecrop">A member of the AppNotificationImageCrop enumeration specifying the cropping method.</param>
      <param name="alternateText">A string containing the alternate text for the inline image.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetInlineImage(Windows.Foundation.Uri,Microsoft.Windows.AppNotifications.Builder.AppNotificationImageCrop)">
      <summary>Sets the image that is displayed inline, after any text elements, filling the full width of the visual area in an app notification, using the specified cropping behavior.</summary>
      <param name="imageUri">The URI of the inline image file.</param>
      <param name="imageCrop">A member of the AppNotificationImageCrop enumeration specifying the cropping method.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetInlineImage(Windows.Foundation.Uri)">
      <summary>Sets the image that is displayed inline, after any text elements, filling the full width of the visual area in an app notification.</summary>
      <param name="imageUri">The URI of the inline image file.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetScenario(Microsoft.Windows.AppNotifications.Builder.AppNotificationScenario)">
      <summary>Sets the scenario for the app notification, which causes the system to adjust some of the notification behaviors to present a consistent experience for the specified scenario.</summary>
      <param name="value">A value from the AppNotificationScenario enumeration specifying the scenario for the app notification.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetTag(System.String)">
      <summary>Sets the tag identifier for an app notification.</summary>
      <param name="value">A string identifying the tag associated with the app notification.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationBuilder.SetTimeStamp(Windows.Foundation.DateTime)">
      <summary>Sets the custom time stamp for an app notification.</summary>
      <param name="value">A DateTimeOffset value specifying the value for the custom time stamp.</param>
      <returns>Returns the AppNotificationBuilder instance so that additional method calls can be chained.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton">
      <summary>Represents a button that is displayed on an app notification.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.#ctor">
      <summary>Initializes a new instance of the AppNotificationButton class.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.#ctor(System.String)">
      <summary>Initializes a new instance of the AppNotificationButton class with the specified button text.</summary>
      <param name="content">The text displayed on the button.</param>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.AddArgument(System.String,System.String)">
      <summary>Adds an argument composed of a key/value pair to the XML payload for an app notification button.</summary>
      <param name="key">A string containing the key of the argument.</param>
      <param name="value">A string containing the value of the argument.</param>
      <returns>Returns the AppNotificationButton instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.IsButtonStyleSupported">
      <summary>Returns a value indicating whether button styles are supported for app notification buttons on the current device.</summary>
      <returns>True if button styles are supported; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.IsToolTipSupported">
      <summary>Returns a value indicating whether tool-tips are supported for app notification buttons on the current device.</summary>
      <returns>True if tool-tips are supported; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.SetButtonStyle(Microsoft.Windows.AppNotifications.Builder.AppNotificationButtonStyle)">
      <summary>Sets the button style for the app notification button.</summary>
      <param name="value">A value from the AppNotificationButtonStyle enumeration specifying the style for the button.</param>
      <returns>Returns the AppNotificationButton instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.SetContextMenuPlacement">
      <summary>Requests that the app notification button be placed in the context menu of the notification.</summary>
      <returns>Returns the AppNotificationButton instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.SetIcon(Windows.Foundation.Uri)">
      <summary>Sets the icon for an AppNotificationButton.</summary>
      <param name="value">The URI of the button icon.</param>
      <returns>Returns the AppNotificationButton instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.SetInputId(System.String)">
      <summary>Sets the input ID for an AppNotificationButton.</summary>
      <param name="value">A string containing the input ID for the button.</param>
      <returns>Returns the AppNotificationButton instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.SetInvokeUri(Windows.Foundation.Uri,System.String)">
      <summary>Sets the URI that is launched when the app notification button is clicked, with a provided Package Family Name (PFN) for disambiguation.</summary>
      <param name="protocolUri">A Uri object specifying the URI to invoke.</param>
      <param name="targetAppId">A string containing the PFN of the target app, to support cases where multiple apps are registered for a single protocol scheme.</param>
      <returns>Returns the AppNotificationButton instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.SetInvokeUri(Windows.Foundation.Uri)">
      <summary>Sets the URI that is launched when the app notification button is clicked.</summary>
      <param name="protocolUri">A Uri object specifying the URI to invoke.</param>
      <returns>Returns the AppNotificationButton instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.SetToolTip(System.String)">
      <summary>Sets the tool-tip text for the app notification button.</summary>
      <param name="value">A string containing the tool-tip text.</param>
      <returns>Returns the AppNotificationButton instance so that additional method calls can be chained.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.Arguments">
      <summary>Gets or sets the arguments associated with the AppNotificationButton.</summary>
      <returns>A dictionary of key/value pairs representing the app notification button arguments.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.ButtonStyle">
      <summary>Gets or sets the style of a button.</summary>
      <returns>A value from the AppNotificationButtonStyle enumeration specifying the style of the button.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.Content">
      <summary>Gets or sets the button text for an AppNotificationButton.</summary>
      <returns>A string containing the button text.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.ContextMenuPlacement">
      <summary>Gets or sets a value specifying whether the button is displayed inside the app notification context menu.</summary>
      <returns>True if the button is placed inside the app notification context menu; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.Icon">
      <summary>Gets or sets the icon for an AppNotificationButton.</summary>
      <returns>The URI of the button icon.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.InputId">
      <summary>Gets or sets the input ID for an AppNotificationButton.</summary>
      <returns>A string containing the input ID for the button.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.InvokeUri">
      <summary>Gets or sets the URI that is launched when the app notification button is clicked.</summary>
      <returns>A Uri object specifying the URI to invoke.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.TargetAppId">
      <summary>Gets or sets the Package Family Name (PFN) for the app to be launched when the app notification button is clicked.</summary>
      <returns>A string containing the PFN of the target app.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationButton.ToolTip">
      <summary>Gets or sets the tool-tip text for the app notification button.</summary>
      <returns>A string containing the tool-tip text.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationButtonStyle">
      <summary>Specifies the button styles that can be assigned to an AppNotificationButton to provide a consistent experience for common scenarios.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationButtonStyle.Critical">
      <summary>The style associated with a critical action.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationButtonStyle.Default">
      <summary>The default style.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationButtonStyle.Success">
      <summary>The style associated with a successful action.</summary>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox">
      <summary>Represents a combo box that is displayed on an app notification.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox.#ctor(System.String)">
      <summary>Initializes a new instance of the AppNotificationComboBox class with the specified ID.</summary>
      <param name="id">The identifier for the combo box.</param>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox.AddItem(System.String,System.String)">
      <summary>Adds an item to an AppNotificationComboBox.</summary>
      <param name="id">The id associated with the combo box item.</param>
      <param name="content">The text content for the combo box item.</param>
      <returns>Returns the AppNotificationComboBox instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox.SetSelectedItem(System.String)">
      <summary>Sets the selected item for an app notification combo box.</summary>
      <param name="id">A string containing the ID of the selected item.</param>
      <returns>Returns the AppNotificationComboBox instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox.SetTitle(System.String)">
      <summary>Sets the title for an app notification combo box.</summary>
      <param name="value">A string containing the title of the combo box.</param>
      <returns>Returns the AppNotificationComboBox instance so that additional method calls can be chained.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox.Items">
      <summary>Gets or sets the items for an AppNotificationComboBox</summary>
      <returns>A dictionary of key/value pairs representing the IDs and display text for the combo box items.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox.SelectedItem">
      <summary>Gets or sets the selected item for an app notification combo box.</summary>
      <returns>A string containing the ID of the selected item.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationComboBox.Title">
      <summary>Gets or sets the title for an app notification combo box.</summary>
      <returns>A string containing the title of the combo box.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationDuration">
      <summary>Specifies the duration for which an AppNotification is displayed.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationDuration.Default">
      <summary>The default duration.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationDuration.Long">
      <summary>Long duration.</summary>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationImageCrop">
      <summary>Specifies the cropping options for an image in an AppNotification.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationImageCrop.Circle">
      <summary>Circular cropping.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationImageCrop.Default">
      <summary>Default. No cropping.</summary>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar">
      <summary>Represents a progress bar that is displayed on an app notification.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.#ctor">
      <summary>Initializes a new instance of the AppNotificationProgressBar class.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.BindStatus">
      <summary>Binds the AppNotificationProgressBar.Status property.</summary>
      <returns>Returns the AppNotificationProgressBar instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.BindTitle">
      <summary>Binds the AppNotificationProgressBar.Title property.</summary>
      <returns>Returns the AppNotificationProgressBar instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.BindValue">
      <summary>Binds the AppNotificationProgressBar.Value property.</summary>
      <returns>Returns the AppNotificationProgressBar instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.BindValueStringOverride">
      <summary>Binds the AppNotificationProgressBar.ValueStringOverride property.</summary>
      <returns>Returns the AppNotificationProgressBar instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.SetStatus(System.String)">
      <summary>Sets the status text of an app notification progress bar.</summary>
      <param name="value">A string containing the status value.</param>
      <returns>Returns the AppNotificationProgressBar instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.SetTitle(System.String)">
      <summary>Sets the title text of an app notification progress bar.</summary>
      <param name="value">A string containing the title text.</param>
      <returns>Returns the AppNotificationProgressBar instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.SetValue(System.Double)">
      <summary>Sets the progress value of an app notification progress bar.</summary>
      <param name="value">A double representing the progress value.</param>
      <returns>Returns the AppNotificationProgressBar instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.SetValueStringOverride(System.String)">
      <summary>Sets the value string override of an app notification progress bar.</summary>
      <param name="value">A string containing the value string override text.</param>
      <returns>Returns the AppNotificationProgressBar instance so that additional method calls can be chained.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.Status">
      <summary>Gets or sets the status text of an app notification progress bar.</summary>
      <returns>A string containing the status text value.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.Title">
      <summary>Gets or sets a the title text of an app notification progress bar.</summary>
      <returns>A string containing the title text value.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.Value">
      <summary>Gets or sets the progress value of an app notification progress bar.</summary>
      <returns>The progress value.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationProgressBar.ValueStringOverride">
      <summary>Gets or sets the value string override of an app notification progress bar.</summary>
      <returns>The value string override text.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationScenario">
      <summary>Specifies the scenario for an app notification, which causes the system to adjust some of the notification behaviors to present a consistent experience for the specified scenario.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationScenario.Alarm">
      <summary>The alarm scenario.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationScenario.Default">
      <summary>The default scenario.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationScenario.IncomingCall">
      <summary>The incoming call scenario.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationScenario.Reminder">
      <summary>The reminder scenario.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationScenario.Urgent">
      <summary>The urgent scenario.</summary>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent">
      <summary>Specifies system sound events that can be played when an app notification is shown.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm">
      <summary>Alarm 1.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm10">
      <summary>Alarm 10.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm2">
      <summary>Alarm 2.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm3">
      <summary>Alarm 3.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm4">
      <summary>Alarm 4.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm5">
      <summary>Alarm 5.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm6">
      <summary>Alarm 6.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm7">
      <summary>Alarm 7.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm8">
      <summary>Alarm 8.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Alarm9">
      <summary>Alarm 9.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call">
      <summary>Incoming call 1.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call10">
      <summary>Incoming call 10.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call2">
      <summary>Incoming call 2.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call3">
      <summary>Incoming call 3.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call4">
      <summary>Incoming call 4.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call5">
      <summary>Incoming call 5.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call6">
      <summary>Incoming call 6.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call7">
      <summary>Incoming call 7.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call8">
      <summary>Incoming call 8.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Call9">
      <summary>Incoming call 9.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Default">
      <summary>Default.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.IM">
      <summary>Instant message.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Mail">
      <summary>Mail.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.Reminder">
      <summary>Reminder.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppNotifications.Builder.AppNotificationSoundEvent.SMS">
      <summary>SMS.</summary>
    </member>
    <member name="T:Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties">
      <summary>Specifies display and localization properties for text displayed on an app notification.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties.#ctor">
      <summary>Initializes a new instance of the AppNotificationTextProperties class, which specifies display and localization properties for the text.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties.SetIncomingCallAlignment">
      <summary>Sets a value specifying whether the associated text is displayed with incoming call alignment.</summary>
      <returns>Returns the AppNotificationTextProperties instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties.SetLanguage(System.String)">
      <summary>Sets a value specifying the language of the associated text.</summary>
      <param name="value">A string containg an IETF language tag specifying the language of the associated text.</param>
      <returns>Returns the AppNotificationTextProperties instance so that additional method calls can be chained.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties.SetMaxLines(System.Int32)">
      <summary>Sets the maximum number of lines the associated text should span.</summary>
      <param name="value">The maximum number of lines the associated text should span.</param>
      <returns>Returns the AppNotificationTextProperties instance so that additional method calls can be chained.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties.IncomingCallAlignment">
      <summary>Gets or sets a value specifying whether the associated text is aligned for an incoming call.</summary>
      <returns>True if the associated text uses incoming call alignment; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties.Language">
      <summary>Gets or sets a value specifying the language of the associated text.</summary>
      <returns>A string containg an IETF language tag specifying the language of the associated text.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppNotifications.Builder.AppNotificationTextProperties.MaxLines">
      <summary>Gets or sets the maximum number of lines the associated text should span.</summary>
      <returns>The maximum number of lines.</returns>
    </member>
  </members>
</doc>