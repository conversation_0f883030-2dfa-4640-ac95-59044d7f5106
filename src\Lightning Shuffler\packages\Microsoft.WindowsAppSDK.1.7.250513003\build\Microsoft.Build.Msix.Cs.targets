<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- C#/WinRT does not implement the `GetResolvedWinMD` target so we need to provide our own.
       The output .winmd is provided through @(TargetPathWithTargetPlatformMoniker) by C#/WinRT's
       override of the `GetTargetPath` target. -->
  <Target Name="GetResolvedWinMD" DependsOnTargets="GetTargetPath" 
          Returns="@(WinMDFullPath)">
    <ItemGroup>
      <WinMDFullpath Include="@(TargetPathWithTargetPlatformMoniker)"
                     Condition="'%(TargetPathWithTargetPlatformMoniker.WinMDFile)'=='true'" />
    </ItemGroup>
  </Target>
</Project>