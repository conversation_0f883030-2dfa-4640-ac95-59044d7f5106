// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Widgets_Providers_1_H
#define WINRT_Microsoft_Windows_Widgets_Providers_1_H
#include "winrt/impl/Microsoft.Windows.Widgets.Providers.0.h"
WINRT_EXPORT namespace winrt::Microsoft::Windows::Widgets::Providers
{
    struct WINRT_IMPL_EMPTY_BASES IWidgetActionInvokedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetActionInvokedArgs>
    {
        IWidgetActionInvokedArgs(std::nullptr_t = nullptr) noexcept {}
        IWidgetActionInvokedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetAnalyticsInfoReportedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetAnalyticsInfoReportedArgs>
    {
        IWidgetAnalyticsInfoReportedArgs(std::nullptr_t = nullptr) noexcept {}
        IWidgetAnalyticsInfoReportedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetContext :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetContext>
    {
        IWidgetContext(std::nullptr_t = nullptr) noexcept {}
        IWidgetContext(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetContextChangedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetContextChangedArgs>
    {
        IWidgetContextChangedArgs(std::nullptr_t = nullptr) noexcept {}
        IWidgetContextChangedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetCustomizationRequestedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetCustomizationRequestedArgs>
    {
        IWidgetCustomizationRequestedArgs(std::nullptr_t = nullptr) noexcept {}
        IWidgetCustomizationRequestedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetErrorInfoReportedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetErrorInfoReportedArgs>
    {
        IWidgetErrorInfoReportedArgs(std::nullptr_t = nullptr) noexcept {}
        IWidgetErrorInfoReportedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetInfo :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetInfo>
    {
        IWidgetInfo(std::nullptr_t = nullptr) noexcept {}
        IWidgetInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetInfo2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetInfo2>
    {
        IWidgetInfo2(std::nullptr_t = nullptr) noexcept {}
        IWidgetInfo2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetManager>
    {
        IWidgetManager(std::nullptr_t = nullptr) noexcept {}
        IWidgetManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetManager2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetManager2>
    {
        IWidgetManager2(std::nullptr_t = nullptr) noexcept {}
        IWidgetManager2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetManagerStatics>
    {
        IWidgetManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IWidgetManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetMessageReceivedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetMessageReceivedArgs>
    {
        IWidgetMessageReceivedArgs(std::nullptr_t = nullptr) noexcept {}
        IWidgetMessageReceivedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetProvider>
    {
        IWidgetProvider(std::nullptr_t = nullptr) noexcept {}
        IWidgetProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetProvider2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetProvider2>
    {
        IWidgetProvider2(std::nullptr_t = nullptr) noexcept {}
        IWidgetProvider2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetProviderAnalytics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetProviderAnalytics>
    {
        IWidgetProviderAnalytics(std::nullptr_t = nullptr) noexcept {}
        IWidgetProviderAnalytics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetProviderErrors :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetProviderErrors>
    {
        IWidgetProviderErrors(std::nullptr_t = nullptr) noexcept {}
        IWidgetProviderErrors(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetProviderMessage :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetProviderMessage>
    {
        IWidgetProviderMessage(std::nullptr_t = nullptr) noexcept {}
        IWidgetProviderMessage(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetResourceProvider :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetResourceProvider>
    {
        IWidgetResourceProvider(std::nullptr_t = nullptr) noexcept {}
        IWidgetResourceProvider(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetResourceRequest :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetResourceRequest>
    {
        IWidgetResourceRequest(std::nullptr_t = nullptr) noexcept {}
        IWidgetResourceRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetResourceRequestedArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetResourceRequestedArgs>
    {
        IWidgetResourceRequestedArgs(std::nullptr_t = nullptr) noexcept {}
        IWidgetResourceRequestedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetResourceResponse :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetResourceResponse>
    {
        IWidgetResourceResponse(std::nullptr_t = nullptr) noexcept {}
        IWidgetResourceResponse(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetResourceResponseFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetResourceResponseFactory>
    {
        IWidgetResourceResponseFactory(std::nullptr_t = nullptr) noexcept {}
        IWidgetResourceResponseFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetUpdateRequestOptions :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetUpdateRequestOptions>
    {
        IWidgetUpdateRequestOptions(std::nullptr_t = nullptr) noexcept {}
        IWidgetUpdateRequestOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetUpdateRequestOptions2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetUpdateRequestOptions2>
    {
        IWidgetUpdateRequestOptions2(std::nullptr_t = nullptr) noexcept {}
        IWidgetUpdateRequestOptions2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetUpdateRequestOptionsFactory :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetUpdateRequestOptionsFactory>
    {
        IWidgetUpdateRequestOptionsFactory(std::nullptr_t = nullptr) noexcept {}
        IWidgetUpdateRequestOptionsFactory(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IWidgetUpdateRequestOptionsStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IWidgetUpdateRequestOptionsStatics>
    {
        IWidgetUpdateRequestOptionsStatics(std::nullptr_t = nullptr) noexcept {}
        IWidgetUpdateRequestOptionsStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
