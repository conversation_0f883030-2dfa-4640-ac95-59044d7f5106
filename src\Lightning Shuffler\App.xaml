<?xml version="1.0" encoding="utf-8"?>
<Application
    x:Class="Lightning_Shuffler.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Lightning_Shuffler">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <XamlControlsResources xmlns="using:Microsoft.UI.Xaml.Controls"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Lightning Shuffler Theme Colors -->
            <Color x:Key="LightningGreen">#00FF41</Color>
            <Color x:Key="DarkBackground">#0D1117</Color>
            <Color x:Key="DarkSurface">#161B22</Color>
            <Color x:Key="DarkSurfaceVariant">#21262D</Color>
            <Color x:Key="TextPrimary">#F0F6FC</Color>
            <Color x:Key="TextSecondary">#8B949E</Color>
            <Color x:Key="BorderColor">#30363D</Color>

            <!-- Brushes -->
            <SolidColorBrush x:Key="LightningGreenBrush"
                    Color="{StaticResource LightningGreen}"/>
            <SolidColorBrush x:Key="DarkBackgroundBrush"
                    Color="{StaticResource DarkBackground}"/>
            <SolidColorBrush x:Key="DarkSurfaceBrush"
                    Color="{StaticResource DarkSurface}"/>
            <SolidColorBrush x:Key="DarkSurfaceVariantBrush"
                    Color="{StaticResource DarkSurfaceVariant}"/>
            <SolidColorBrush x:Key="TextPrimaryBrush"
                    Color="{StaticResource TextPrimary}"/>
            <SolidColorBrush x:Key="TextSecondaryBrush"
                    Color="{StaticResource TextSecondary}"/>
            <SolidColorBrush x:Key="BorderBrush"
                    Color="{StaticResource BorderColor}"/>

            <!-- Lightning Green Gradient -->
            <LinearGradientBrush x:Key="LightningGradientBrush"
                    StartPoint="0,0"
                    EndPoint="1,1">
                <GradientStop Color="#00FF41"
                        Offset="0"/>
                <GradientStop Color="#00CC33"
                        Offset="1"/>
            </LinearGradientBrush>

            <!-- Glass Effect Brush -->
            <LinearGradientBrush x:Key="GlassBrush"
                    StartPoint="0,0"
                    EndPoint="0,1">
                <GradientStop Color="#40FFFFFF"
                        Offset="0"/>
                <GradientStop Color="#10FFFFFF"
                        Offset="1"/>
            </LinearGradientBrush>

            <!-- Modern Button Style -->
            <Style x:Key="ModernButtonStyle"
                    TargetType="Button">
                <Setter Property="Background"
                        Value="{StaticResource DarkSurfaceVariantBrush}"/>
                <Setter Property="Foreground"
                        Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="BorderBrush"
                        Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness"
                        Value="1"/>
                <Setter Property="CornerRadius"
                        Value="8"/>
                <Setter Property="Padding"
                        Value="16,8"/>
                <Setter Property="FontWeight"
                        Value="SemiBold"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border x:Name="RootBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="{TemplateBinding CornerRadius}">
                                <ContentPresenter x:Name="ContentPresenter"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                  Padding="{TemplateBinding Padding}"
                                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="RootBorder.Background"
                                                        Value="{StaticResource LightningGreenBrush}"/>
                                                <Setter Target="ContentPresenter.Foreground"
                                                        Value="Black"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Pressed">
                                            <VisualState.Setters>
                                                <Setter Target="RootBorder.Background"
                                                        Value="#00CC33"/>
                                                <Setter Target="ContentPresenter.Foreground"
                                                        Value="Black"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Accent Button Style -->
            <Style x:Key="AccentButtonStyle"
                    TargetType="Button"
                    BasedOn="{StaticResource ModernButtonStyle}">
                <Setter Property="Background"
                        Value="{StaticResource LightningGradientBrush}"/>
                <Setter Property="Foreground"
                        Value="Black"/>
                <Setter Property="BorderBrush"
                        Value="{StaticResource LightningGreenBrush}"/>
            </Style>

            <!-- Modern TextBox Style -->
            <Style x:Key="ModernTextBoxStyle"
                    TargetType="TextBox">
                <Setter Property="Background"
                        Value="{StaticResource DarkSurfaceBrush}"/>
                <Setter Property="Foreground"
                        Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="BorderBrush"
                        Value="{StaticResource BorderBrush}"/>
                <Setter Property="BorderThickness"
                        Value="1"/>
                <Setter Property="CornerRadius"
                        Value="8"/>
                <Setter Property="Padding"
                        Value="12,8"/>
                <Setter Property="FontSize"
                        Value="14"/>
            </Style>

            <!-- Modern ListViewItem Style -->
            <Style x:Key="ModernListViewItemStyle"
                    TargetType="ListViewItem">
                <Setter Property="Background"
                        Value="Transparent"/>
                <Setter Property="Foreground"
                        Value="{StaticResource TextPrimaryBrush}"/>
                <Setter Property="Padding"
                        Value="12,8"/>
                <Setter Property="Margin"
                        Value="0,2"/>
                <Setter Property="CornerRadius"
                        Value="6"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ListViewItem">
                            <Border x:Name="RootBorder"
                                    Background="{TemplateBinding Background}"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter x:Name="ContentPresenter"
                                                  Content="{TemplateBinding Content}"
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"/>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Normal"/>
                                        <VisualState x:Name="PointerOver">
                                            <VisualState.Setters>
                                                <Setter Target="RootBorder.Background"
                                                        Value="{StaticResource DarkSurfaceVariantBrush}"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                        <VisualState x:Name="Selected">
                                            <VisualState.Setters>
                                                <Setter Target="RootBorder.Background"
                                                        Value="{StaticResource LightningGreenBrush}"/>
                                                <Setter Target="ContentPresenter.Foreground"
                                                        Value="Black"/>
                                            </VisualState.Setters>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </Border>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application>
