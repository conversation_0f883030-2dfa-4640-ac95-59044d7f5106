<?xml version="1.0" encoding="utf-8"?>

<!--
  Copyright (c) Microsoft Corporation. Licensed under the MIT License
-->
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <Target Condition="'$(InteractiveExperiencesVersion)'!=''" Name="UpdatePackageVersion" BeforeTargets="BeforeBuild">
        <XmlPoke XmlInputPath="$(MSBuildThisFileDirectory)\build\Microsoft.InteractiveExperiences.Capabilities.EC.props" 
         Query="/ns:Project/ns:ItemGroup/ns:ProjectCapability[@Id='VersionSpecific']/@Include" 
         Value="Microsoft.ProjectReunion.InteractiveExperiences.$(InteractiveExperiencesVersion)" Namespaces="&lt;Namespace Prefix='ns' 
         Uri='http://schemas.microsoft.com/developer/msbuild/2003' /&gt;" />
	</Target>
    <Target Condition="'$(InteractiveExperiencesVersion)'!=''" Name="RestorePackageVersion" AfterTargets="Build">
        <XmlPoke XmlInputPath="$(MSBuildThisFileDirectory)\build\Microsoft.InteractiveExperiences.Capabilities.EC.props" 
         Query="/ns:Project/ns:ItemGroup/ns:ProjectCapability[@Id='VersionSpecific']/@Include" 
         Value="Microsoft.ProjectReunion.InteractiveExperiences.0.5.0-dev" Namespaces="&lt;Namespace Prefix='ns' 
         Uri='http://schemas.microsoft.com/developer/msbuild/2003' /&gt;" />
	</Target>
</Project>
