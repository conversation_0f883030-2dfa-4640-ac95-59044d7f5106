﻿<?xml version="1.0" encoding="utf-8"?>
<WindowsAppSDK>
    <!-- Release information -->
    <Release>
        <Major>1</Major>
        <Minor>7</Minor>
        <Patch>513</Patch>
        <MajorMinor>
            <UInt32>65543</UInt32>
            <HexUInt32>0x00010007</HexUInt32>
        </MajorMinor>

        <Channel>stable</Channel>

        <Tag></Tag>
        <ShortTag></ShortTag>

        <Formatted>
            <Tag></Tag>
            <ShortTag></ShortTag>
        </Formatted>
    </Release>

    <!-- Runtime information -->
    <Runtime>
        <Identity>
            <Publisher>CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US</Publisher>
            <PublisherId>8wekyb3d8bbwe</PublisherId>
        </Identity>
        <Version>
            <DotQuadNumber>7000.498.2246.0</DotQuadNumber>
            <UInt16>1970326976015499264</UInt16>
            <HexUInt16>0x1B5801F208C60000</HexUInt16>
            <String>7000.498.2246.0</String>
        </Version>
        <Packages>
            <Framework>
                <PackageFamilyName>Microsoft.WindowsAppRuntime.1.7_8wekyb3d8bbwe</PackageFamilyName>
            </Framework>
            <Main>
                <PackageFamilyName>MicrosoftCorporationII.WinAppRuntime.Main.1.7_8wekyb3d8bbwe</PackageFamilyName>
            </Main>
            <Singleton>
                <PackageFamilyName>MicrosoftCorporationII.WinAppRuntime.Singleton_8wekyb3d8bbwe</PackageFamilyName>
            </Singleton>
            <DDLM>
                <X86>
                    <PackageFamilyName>Microsoft.WinAppRuntime.DDLM.7000.498.2246.0-x8_8wekyb3d8bbwe</PackageFamilyName>
                </X86>
                <X64>
                    <PackageFamilyName>Microsoft.WinAppRuntime.DDLM.7000.498.2246.0-x6_8wekyb3d8bbwe</PackageFamilyName>
                </X64>
                <Arm64>
                    <PackageFamilyName>Microsoft.WinAppRuntime.DDLM.7000.498.2246.0-a6_8wekyb3d8bbwe</PackageFamilyName>
                </Arm64>
            </DDLM>
        </Packages>
    </Runtime>
</WindowsAppSDK>
