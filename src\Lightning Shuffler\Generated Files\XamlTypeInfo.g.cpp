﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#include "pch.h"
#include <memory>
#include <unknwn.h>

// Undefine GetCurrentTime macro to prevent
// conflict with Storyboard::GetCurrentTime
#undef GetCurrentTime

#if __has_include(<winrt/Lightning_Shuffler.h>)
#include <winrt/Lightning_Shuffler.h>
#endif
#if __has_include(<winrt/Microsoft.UI.Xaml.Controls.h>)
#include <winrt/Microsoft.UI.Xaml.Controls.h>
#endif
#if __has_include(<winrt/Windows.Foundation.Collections.h>)
#include <winrt/Windows.Foundation.Collections.h>
#endif

#include "XamlTypeInfo.xaml.g.h"

#include "VideoItemViewModel.h"
#include "App.xaml.h"
#include "MainWindow.xaml.h"
#include "AddPlaylistDialog.h"
#include "XamlBindingInfo.xaml.g.hpp"
#include "App.xaml.g.hpp"
#include "MainWindow.xaml.g.hpp"
#include "AddPlaylistDialog.xaml.g.hpp"

namespace winrt::Lightning_Shuffler::implementation
{
using IXamlMember = ::winrt::Microsoft::UI::Xaml::Markup::IXamlMember;
using IXamlType = ::winrt::Microsoft::UI::Xaml::Markup::IXamlType;
using TypeKind = ::winrt::Windows::UI::Xaml::Interop::TypeKind;

    namespace XamlTypeInfo_staticasserts
    {
        template<typename, typename = void>
        constexpr bool is_type_complete_v = false;

        template<typename T>
        constexpr bool is_type_complete_v<T, std::void_t<decltype(sizeof(T))>> = true;

        static_assert( is_type_complete_v<::winrt::Lightning_Shuffler::MainWindow>, "Please #include the implementation header for '::winrt::Lightning_Shuffler::MainWindow' in your precompiled header 'pch.h'." );
        static_assert( is_type_complete_v<::winrt::Lightning_Shuffler::AddPlaylistDialog>, "Please #include the implementation header for '::winrt::Lightning_Shuffler::AddPlaylistDialog' in your precompiled header 'pch.h'." );
        static_assert( is_type_complete_v<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>, "Please #include the implementation header for '::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode' in your precompiled header 'pch.h'." );
        static_assert( is_type_complete_v<::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources>, "Please #include the implementation header for '::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources' in your precompiled header 'pch.h'." );
        static_assert( is_type_complete_v<::winrt::Windows::Foundation::Collections::IVector<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>>, "Please #include the implementation header for '::winrt::Windows::Foundation::Collections::IVector<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>' in your precompiled header 'pch.h'." );

    }

template <typename T>
::winrt::Windows::Foundation::IInspectable ActivateType()
{
    return T();
}

template <typename T>
::winrt::Windows::Foundation::IInspectable ActivateLocalType()
{
    return ::winrt::make<T>();
}

template<typename TInstance, typename TItem>
void CollectionAdd(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& item)
{
    instance.as<TInstance>().Append(::winrt::unbox_value<TItem>(item));
}

template<typename TInstance, typename TKey, typename TItem>
void DictionaryAdd(
    ::winrt::Windows::Foundation::IInspectable const& instance,
    ::winrt::Windows::Foundation::IInspectable const& key,
    ::winrt::Windows::Foundation::IInspectable const& item)
{
    instance.as<TInstance>().Insert(::winrt::unbox_value<TKey>(key), ::winrt::unbox_value<TItem>(item));
}

template<typename T>
::winrt::Windows::Foundation::IInspectable FromStringConverter(
    XamlUserType const& userType, 
    ::winrt::hstring const& input)
{
    return ::winrt::box_value(static_cast<T>(userType.CreateEnumUIntFromString(input)));
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_UseCompactResources(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().UseCompactResources());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_IsExpanded(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().IsExpanded());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_HasUnrealizedChildren(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().HasUnrealizedChildren());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_Depth(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().Depth());
}

template<typename TDeclaringType, typename TValue>
::winrt::Windows::Foundation::IInspectable GetValueTypeMember_HasChildren(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value<TValue>(instance.as<TDeclaringType>().HasChildren());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeStringMember_PlaylistUrl(::winrt::Windows::Foundation::IInspectable const& instance)
{
   return ::winrt::box_value(::winrt::Windows::Foundation::PropertyValue::CreateString(instance.as<T>().PlaylistUrl()));
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_Content(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().Content());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_Children(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().Children());
}

template <typename T>
::winrt::Windows::Foundation::IInspectable GetReferenceTypeMember_Parent(::winrt::Windows::Foundation::IInspectable const& instance)
{
    return ::winrt::box_value(instance.as<T>().Parent());
}

template<typename TDeclaringType, typename TValue>
void SetValueTypeMember_UseCompactResources(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().UseCompactResources(::winrt::unbox_value<TValue>(value));
}

template<typename TDeclaringType, typename TValue>
void SetValueTypeMember_IsExpanded(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().IsExpanded(::winrt::unbox_value<TValue>(value));
}

template<typename TDeclaringType, typename TValue>
void SetValueTypeMember_HasUnrealizedChildren(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().HasUnrealizedChildren(::winrt::unbox_value<TValue>(value));
}

template<typename TDeclaringType, typename TValue>
void SetReferenceTypeMember_Content(
    ::winrt::Windows::Foundation::IInspectable const& instance, 
    ::winrt::Windows::Foundation::IInspectable const& value)
{
    instance.as<TDeclaringType>().Content(value.as<TValue>());
}

enum TypeInfo_Flags
{
    TypeInfo_Flags_None                 = 0x00,
    TypeInfo_Flags_IsLocalType          = 0x01,
    TypeInfo_Flags_IsSystemType         = 0x02,
    TypeInfo_Flags_IsReturnTypeStub     = 0x04,
    TypeInfo_Flags_IsBindable           = 0x08,
    TypeInfo_Flags_IsMarkupExtension    = 0x10, 
};

struct TypeInfo
{
    const wchar_t* typeName{nullptr};
    const wchar_t* contentPropertyName{nullptr};
    ::winrt::Windows::Foundation::IInspectable (*activator)();
    void (*collectionAdd)(::winrt::Windows::Foundation::IInspectable const&, ::winrt::Windows::Foundation::IInspectable const&);
    void (*dictionaryAdd)(::winrt::Windows::Foundation::IInspectable const&, ::winrt::Windows::Foundation::IInspectable const&, ::winrt::Windows::Foundation::IInspectable const&);
    ::winrt::Windows::Foundation::IInspectable (*fromStringConverter)(XamlUserType const&, ::winrt::hstring const& );
    int     baseTypeIndex;
    int     firstMemberIndex;
    int     firstEnumValueIndex;
    int     createFromStringIndex;
    TypeKind kindOfType;
    unsigned int flags;
    int boxedTypeIndex;
};


const TypeInfo TypeInfos[] = 
{
    //   0
    L"Int32", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   1
    L"Object", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   2
    L"String", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   3
    L"Boolean", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   4
    L"Windows.UI.Color", L"",
    nullptr, nullptr, nullptr, nullptr,
    5, // System.ValueType
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //   5
    L"System.ValueType", L"",
    nullptr, nullptr, nullptr, nullptr,
    1, // Object
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //   6
    L"Microsoft.UI.Xaml.Window", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   7
    L"Lightning_Shuffler.MainWindow", L"",
    &ActivateLocalType<::winrt::Lightning_Shuffler::implementation::MainWindow>, nullptr, nullptr, nullptr,
    6, // Microsoft.UI.Xaml.Window
    0, 0, -1, TypeKind::Custom,
    TypeInfo_Flags_IsLocalType | TypeInfo_Flags_None,
    -1,
    //   8
    L"Microsoft.UI.Xaml.DependencyObject", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //   9
    L"Microsoft.UI.Xaml.ResourceDictionary", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    0, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  10
    L"Lightning_Shuffler.AddPlaylistDialog", L"",
    &ActivateLocalType<::winrt::Lightning_Shuffler::implementation::AddPlaylistDialog>, nullptr, nullptr, nullptr,
    12, // Microsoft.UI.Xaml.Controls.ContentDialog
    0, 0, -1, TypeKind::Custom,
    TypeInfo_Flags_IsLocalType | TypeInfo_Flags_None,
    -1,
    //  11
    L"Microsoft.UI.Xaml.Controls.TreeViewNode", L"",
    &ActivateType<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>, nullptr, nullptr, nullptr,
    8, // Microsoft.UI.Xaml.DependencyObject
    1, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsBindable | TypeInfo_Flags_None,
    -1,
    //  12
    L"Microsoft.UI.Xaml.Controls.ContentDialog", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    8, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  13
    L"Microsoft.UI.Xaml.Controls.ContentControl", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1,
    8, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsSystemType | TypeInfo_Flags_None,
    -1,
    //  14
    L"Microsoft.UI.Xaml.Controls.XamlControlsResources", L"",
    &ActivateType<::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources>, nullptr, &DictionaryAdd<::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources, ::winrt::Windows::Foundation::IInspectable, ::winrt::Windows::Foundation::IInspectable>, nullptr,
    9, // Microsoft.UI.Xaml.ResourceDictionary
    8, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_None,
    -1,
    //  15
    L"Windows.Foundation.Collections.IVector`1<Microsoft.UI.Xaml.Controls.TreeViewNode>", L"",
    nullptr, &CollectionAdd<::winrt::Windows::Foundation::Collections::IVector<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>, ::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>, nullptr, nullptr,
    -1,
    9, 0, -1, TypeKind::Metadata,
    TypeInfo_Flags_IsReturnTypeStub | TypeInfo_Flags_None,
    -1,
    //  Last type here is for padding
    L"", L"",
    nullptr, nullptr, nullptr, nullptr,
    -1, 
    9, 0, -1, TypeKind::Custom,
    TypeInfo_Flags_None,
};

constexpr uint32_t TypeInfoLookup[] = { 
      0,   //   0
      0,   //   1
      0,   //   2
      0,   //   3
      0,   //   4
      0,   //   5
      1,   //   6
      3,   //   7
      4,   //   8
      4,   //   9
      4,   //  10
      4,   //  11
      4,   //  12
      4,   //  13
      4,   //  14
      4,   //  15
      4,   //  16
      6,   //  17
      6,   //  18
      6,   //  19
      6,   //  20
      6,   //  21
      6,   //  22
      6,   //  23
      6,   //  24
      7,   //  25
      7,   //  26
      7,   //  27
      7,   //  28
      7,   //  29
      8,   //  30
      8,   //  31
      8,   //  32
      8,   //  33
      8,   //  34
      9,   //  35
      9,   //  36
     11,   //  37
     11,   //  38
     11,   //  39
     12,   //  40
     13,   //  41
     14,   //  42
     14,   //  43
     14,   //  44
     14,   //  45
     14,   //  46
     14,   //  47
     14,   //  48
     15,   //  49
     15,   //  50
     15,   //  51
     15,   //  52
     15,   //  53
     15,   //  54
     15,   //  55
     15,   //  56
     15,   //  57
     15,   //  58
     15,   //  59
     15,   //  60
     15,   //  61
     15,   //  62
     15,   //  63
     15,   //  64
     15,   //  65
     15,   //  66
     15,   //  67
     15,   //  68
     15,   //  69
     15,   //  70
     15,   //  71
     15,   //  72
     15,   //  73
     15,   //  74
     15,   //  75
     15,   //  76
     15,   //  77
     15,   //  78
     15,   //  79
     15,   //  80
     15,   //  81
     16,   //  82
};

struct MemberInfo 
{
    const wchar_t* shortName{nullptr};
    ::winrt::Windows::Foundation::IInspectable (*getter)(::winrt::Windows::Foundation::IInspectable const&);
    void (*setter)(::winrt::Windows::Foundation::IInspectable const&, ::winrt::Windows::Foundation::IInspectable const&);
    int typeIndex;
    int targetTypeIndex;
    bool isReadOnly;
    bool isDependencyProperty;
    bool isAttachable;
};

const MemberInfo MemberInfos[] = 
{
    //   0 - Lightning_Shuffler.AddPlaylistDialog.PlaylistUrl
    L"PlaylistUrl",
    &GetReferenceTypeStringMember_PlaylistUrl<::winrt::Lightning_Shuffler::AddPlaylistDialog>,
    nullptr,
    2, // String
    -1,
    true,  false, false,
    //   1 - Microsoft.UI.Xaml.Controls.TreeViewNode.IsExpanded
    L"IsExpanded",
    &GetValueTypeMember_IsExpanded<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    &SetValueTypeMember_IsExpanded<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    3, // Boolean
    -1,
    false, true,  false,
    //   2 - Microsoft.UI.Xaml.Controls.TreeViewNode.HasUnrealizedChildren
    L"HasUnrealizedChildren",
    &GetValueTypeMember_HasUnrealizedChildren<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    &SetValueTypeMember_HasUnrealizedChildren<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    3, // Boolean
    -1,
    false, false, false,
    //   3 - Microsoft.UI.Xaml.Controls.TreeViewNode.Content
    L"Content",
    &GetReferenceTypeMember_Content<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>,
    &SetReferenceTypeMember_Content<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, ::winrt::Windows::Foundation::IInspectable>,
    1, // Object
    -1,
    false, true,  false,
    //   4 - Microsoft.UI.Xaml.Controls.TreeViewNode.Children
    L"Children",
    &GetReferenceTypeMember_Children<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>,
    nullptr,
    15, // Windows.Foundation.Collections.IVector`1<Microsoft.UI.Xaml.Controls.TreeViewNode>
    -1,
    true,  false, false,
    //   5 - Microsoft.UI.Xaml.Controls.TreeViewNode.Depth
    L"Depth",
    &GetValueTypeMember_Depth<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, int32_t>,
    nullptr,
    0, // Int32
    -1,
    true,  true,  false,
    //   6 - Microsoft.UI.Xaml.Controls.TreeViewNode.HasChildren
    L"HasChildren",
    &GetValueTypeMember_HasChildren<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode, bool>,
    nullptr,
    3, // Boolean
    -1,
    true,  true,  false,
    //   7 - Microsoft.UI.Xaml.Controls.TreeViewNode.Parent
    L"Parent",
    &GetReferenceTypeMember_Parent<::winrt::Microsoft::UI::Xaml::Controls::TreeViewNode>,
    nullptr,
    11, // Microsoft.UI.Xaml.Controls.TreeViewNode
    -1,
    true,  false, false,
    //   8 - Microsoft.UI.Xaml.Controls.XamlControlsResources.UseCompactResources
    L"UseCompactResources",
    &GetValueTypeMember_UseCompactResources<::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources, bool>,
    &SetValueTypeMember_UseCompactResources<::winrt::Microsoft::UI::Xaml::Controls::XamlControlsResources, bool>,
    3, // Boolean
    -1,
    false, true,  false,
};

const wchar_t* GetShortName(const wchar_t* longName)
{
    const auto separator = wcsrchr(longName, '.');
    return separator ? separator + 1: longName;
}

const TypeInfo* GetTypeInfo(::winrt::hstring const& typeName)
{
    size_t typeNameLength = typeName.size();
    if (typeNameLength < _countof(TypeInfoLookup) - 1)
    {
        const auto begin = TypeInfos + TypeInfoLookup[typeNameLength];
        const auto end = TypeInfos + TypeInfoLookup[typeNameLength + 1];
        auto pos = std::find_if(begin, end, [&typeName](TypeInfo const& elem)
        {
            return wcscmp(typeName.data(), elem.typeName) == 0;
        });
        if (pos != end)
        {
            return pos;
        }
    }
    return nullptr;
}

const MemberInfo* GetMemberInfo(::winrt::hstring const& longMemberName)
{
    const auto dotPosition = std::find(longMemberName.crbegin(), longMemberName.crend(), L'.').base();
    if (dotPosition != longMemberName.end())
    {
        const auto sizeBeforeDot = static_cast<::winrt::hstring::size_type>(dotPosition - longMemberName.begin()) - 1;
        const TypeInfo* pTypeInfo = GetTypeInfo(::winrt::hstring{longMemberName.data(), sizeBeforeDot});
        if (pTypeInfo)
        {
            const TypeInfo* pNextTypeInfo = pTypeInfo + 1;
            const auto shortMemberName = GetShortName(longMemberName.data());
            const auto begin = MemberInfos + pTypeInfo->firstMemberIndex;
            const auto end = MemberInfos + pNextTypeInfo->firstMemberIndex;
            auto info = std::find_if(begin, end,
                [shortMemberName](const MemberInfo& elem)
            {
                return wcscmp(shortMemberName, elem.shortName) == 0;
            });
            if (info != end)
            {
                return info;
            }
        }
    }
    return nullptr;
}

std::vector<::winrt::Microsoft::UI::Xaml::Markup::IXamlMetadataProvider> const& XamlTypeInfoProvider::OtherProviders()
{
    std::lock_guard<std::recursive_mutex> lock(_xamlTypesCriticalSection);
    if (_otherProviders.empty())
    {
        _otherProviders.push_back(::winrt::Microsoft::UI::Xaml::XamlTypeInfo::XamlControlsXamlMetaDataProvider());
    }
    return _otherProviders;
}

IXamlType XamlTypeInfoProvider::CreateXamlType(::winrt::hstring const& typeName)
{
    const TypeInfo* pTypeInfo = GetTypeInfo(typeName);
    const TypeInfo* pNextTypeInfo = pTypeInfo + 1;
    if (!pTypeInfo || !pNextTypeInfo)
    {
        return nullptr;
    }
    else if (pTypeInfo->flags & TypeInfo_Flags_IsSystemType)
    {
        return ::winrt::make<XamlSystemBaseType>(typeName);
    }
    else
    {
        ::winrt::hstring baseName { pTypeInfo->baseTypeIndex >= 0 ? TypeInfos[pTypeInfo->baseTypeIndex].typeName : L""};
        ::winrt::hstring boxedName { pTypeInfo->boxedTypeIndex >= 0 ? TypeInfos[pTypeInfo->boxedTypeIndex].typeName : L""};
        auto userType = ::winrt::make_self<XamlUserType>(shared_from_this(), pTypeInfo->typeName, GetXamlTypeByName(baseName));
        userType->_kindOfType = pTypeInfo->kindOfType;
        userType->_activator = pTypeInfo->activator;
        userType->_collectionAdd = pTypeInfo->collectionAdd;
        userType->_dictionaryAdd = pTypeInfo->dictionaryAdd;
        userType->_fromStringConverter = pTypeInfo->fromStringConverter;
        userType->ContentPropertyName(pTypeInfo->contentPropertyName);
        userType->IsLocalType(pTypeInfo->flags & TypeInfo_Flags_IsLocalType);
        userType->IsReturnTypeStub(pTypeInfo->flags & TypeInfo_Flags_IsReturnTypeStub);
        userType->IsBindable(pTypeInfo->flags & TypeInfo_Flags_IsBindable);
        userType->IsMarkupExtension(pTypeInfo->flags & TypeInfo_Flags_IsMarkupExtension);
        userType->_createFromStringMethod = nullptr;
        userType->SetBoxedType(GetXamlTypeByName(boxedName));
        for (int i = pTypeInfo->firstMemberIndex; i < pNextTypeInfo->firstMemberIndex; ++i)
        {
            userType->AddMemberName(MemberInfos[i].shortName);
        }
        return userType.as<IXamlType>();
    }
}

IXamlMember XamlTypeInfoProvider::CreateXamlMember(::winrt::hstring const& longMemberName)
{
    const MemberInfo* pMemberInfo = GetMemberInfo(longMemberName);
    if (!pMemberInfo)
    {
        return nullptr;
    }
    auto xamlMember = ::winrt::make_self<XamlMember>(shared_from_this(),
        pMemberInfo->shortName, TypeInfos[pMemberInfo->typeIndex].typeName);
    xamlMember->_getter = pMemberInfo->getter;
    xamlMember->_setter = pMemberInfo->setter;
    xamlMember->TargetTypeName(pMemberInfo->targetTypeIndex >= 0 ? TypeInfos[pMemberInfo->targetTypeIndex].typeName : L"");
    xamlMember->IsReadOnly(pMemberInfo->isReadOnly);
    xamlMember->IsDependencyProperty(pMemberInfo->isDependencyProperty);
    xamlMember->IsAttachable(pMemberInfo->isAttachable);

    return xamlMember.as<IXamlMember>();
}
} // namespace


