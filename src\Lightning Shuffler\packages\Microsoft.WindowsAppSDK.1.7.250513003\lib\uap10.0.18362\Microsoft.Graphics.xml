<doc>
  <assembly>
    <name>Microsoft.Graphics</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Graphics.DirectX.DirectXAlphaMode">
      <summary>Identifies the alpha value, transparency behavior, of a surface. This is a Windows Runtime equivalent of the Desktop DXGI_ALPHA_MODE enumeration.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXAlphaMode.Ignore">
      <summary>Indicates to ignore the transparency behavior.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXAlphaMode.Premultiplied">
      <summary>Indicates that the transparency behavior is premultiplied. Each color is first scaled by the alpha value. The alpha value itself is the same in both straight and premultiplied alpha. Typically, no color channel value is greater than the alpha channel value. If a color channel value in a premultiplied format is greater than the alpha channel, the standard source-over blending math results in an additive blend.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXAlphaMode.Straight">
      <summary>Indicates that the transparency behavior is not premultiplied. The alpha channel indicates the transparency of the color.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXAlphaMode.Unspecified">
      <summary>Indicates that the transparency behavior is not specified.</summary>
    </member>
    <member name="T:Microsoft.Graphics.DirectX.DirectXColorSpace">
      <summary>This is a Windows Runtime equivalent of the Desktop DXGI_COLOR_SPACE_TYPE enumeration.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.Reserved">
      <summary>Corresponds to DXGI_COLOR_SPACE_RESERVED.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.RgbFullG10NoneP709">
      <summary>Corresponds to DXGI_COLOR_SPACE_RGB_FULL_G10_NONE_P709.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.RgbFullG2084NoneP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_RGB_FULL_G2084_NONE_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.RgbFullG22NoneP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_RGB_FULL_G22_NONE_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.RgbFullG22NoneP709">
      <summary>Corresponds to DXGI_COLOR_SPACE_RGB_FULL_G22_NONE_P709.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.RgbStudioG2084NoneP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_RGB_STUDIO_G2084_NONE_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.RgbStudioG22NoneP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_RGB_STUDIO_G22_NONE_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.RgbStudioG22NoneP709">
      <summary>Corresponds to DXGI_COLOR_SPACE_RGB_STUDIO_G22_NONE_P709.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.RgbStudioG24NoneP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_RGB_STUDIO_G24_NONE_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.RgbStudioG24NoneP709">
      <summary>Corresponds to DXGI_COLOR_SPACE_RGB_STUDIO_G24_NONE_P709.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccFullG22LeftP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_FULL_G22_LEFT_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccFullG22LeftP601">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_FULL_G22_LEFT_P601.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccFullG22LeftP709">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_FULL_G22_LEFT_P709.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccFullG22NoneP709X601">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_FULL_G22_NONE_P709_X601.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccFullGHlgTopLeftP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_FULL_GHLG_TOPLEFT_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioG2084LeftP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_G2084_LEFT_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioG2084TopLeftP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_G2084_TOPLEFT_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioG22LeftP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_G22_LEFT_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioG22LeftP601">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_G22_LEFT_P601.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioG22LeftP709">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_G22_LEFT_P709.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioG22TopLeftP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_G22_TOPLEFT_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioG24LeftP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_G24_LEFT_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioG24LeftP709">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_G24_LEFT_P709.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioG24TopLeftP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_G24_TOPLEFT_P2020.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXColorSpace.YccStudioGHlgTopLeftP2020">
      <summary>Corresponds to DXGI_COLOR_SPACE_YCBCR_STUDIO_GHLG_TOPLEFT_P2020.</summary>
    </member>
    <member name="T:Microsoft.Graphics.DirectX.DirectXPixelFormat">
      <summary>Specifies pixel formats, which includes fully-typed and type-less formats. This is a Windows Runtime equivalent of the Desktop DXGI_FORMAT enumeration.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.A4B4G4R4" />
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.A8P8">
      <summary>See DXGI_FORMAT_A8P8.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.A8UIntNormalized">
      <summary>See DXGI_FORMAT_A8_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.AI44">
      <summary>See DXGI_FORMAT_AI44.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.Ayuv">
      <summary>See DXGI_FORMAT_AYUV.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.B4G4R4A4UIntNormalized">
      <summary>See DXGI_FORMAT_B4G4R4A4_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.B5G5R5A1UIntNormalized">
      <summary>See DXGI_FORMAT_B5G5R5A1_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.B5G6R5UIntNormalized">
      <summary>See DXGI_FORMAT_B5G6R5_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.B8G8R8A8Typeless">
      <summary>See DXGI_FORMAT_B8G8R8A8_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.B8G8R8A8UIntNormalized">
      <summary>See DXGI_FORMAT_B8G8R8A8_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.B8G8R8A8UIntNormalizedSrgb">
      <summary>See DXGI_FORMAT_B8G8R8A8_UNORM_SRGB.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.B8G8R8X8Typeless">
      <summary>See DXGI_FORMAT_B8G8R8X8_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.B8G8R8X8UIntNormalized">
      <summary>See DXGI_FORMAT_B8G8R8X8_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.B8G8R8X8UIntNormalizedSrgb">
      <summary>See DXGI_FORMAT_B8G8R8X8_UNORM_SRGB.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC1Typeless">
      <summary>See DXGI_FORMAT_BC1_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC1UIntNormalized">
      <summary>See DXGI_FORMAT_BC1_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC1UIntNormalizedSrgb">
      <summary>See DXGI_FORMAT_BC1_UNORM_SRGB.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC2Typeless">
      <summary>See DXGI_FORMAT_BC2_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC2UIntNormalized">
      <summary>See DXGI_FORMAT_BC2_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC2UIntNormalizedSrgb">
      <summary>See DXGI_FORMAT_BC2_UNORM_SRGB.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC3Typeless">
      <summary>See DXGI_FORMAT_BC3_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC3UIntNormalized">
      <summary>See DXGI_FORMAT_BC3_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC3UIntNormalizedSrgb">
      <summary>See DXGI_FORMAT_BC3_UNORM_SRGB.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC4IntNormalized">
      <summary>See DXGI_FORMAT_BC4_SNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC4Typeless">
      <summary>See DXGI_FORMAT_BC4_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC4UIntNormalized">
      <summary>See DXGI_FORMAT_BC4_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC5IntNormalized">
      <summary>See DXGI_FORMAT_BC5_SNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC5Typeless">
      <summary>See DXGI_FORMAT_BC5_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC5UIntNormalized">
      <summary>See DXGI_FORMAT_BC5_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC6H16Float">
      <summary>See DXGI_FORMAT_BC6H_SF16.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC6H16UnsignedFloat">
      <summary>See DXGI_FORMAT_BC6H_UF16.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC6HTypeless">
      <summary>See DXGI_FORMAT_BC6H_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC7Typeless">
      <summary>See DXGI_FORMAT_BC7_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC7UIntNormalized">
      <summary>See DXGI_FORMAT_BC7_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.BC7UIntNormalizedSrgb">
      <summary>See DXGI_FORMAT_BC7_UNORM_SRGB.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.D16UIntNormalized">
      <summary>See DXGI_FORMAT_D16_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.D24UIntNormalizedS8UInt">
      <summary>See DXGI_FORMAT_D24_UNORM_S8_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.D32Float">
      <summary>See DXGI_FORMAT_D32_FLOAT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.D32FloatS8X24UInt">
      <summary>See DXGI_FORMAT_D32_FLOAT_S8X24_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.G8R8G8B8UIntNormalized">
      <summary>See DXGI_FORMAT_G8R8_G8B8_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.IA44">
      <summary>See DXGI_FORMAT_IA44.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.NV11">
      <summary>See DXGI_FORMAT_NV11.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.NV12">
      <summary>See DXGI_FORMAT_NV12.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.Opaque420">
      <summary>See DXGI_FORMAT_420_OPAQUE.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.P010">
      <summary>See DXGI_FORMAT_P010.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.P016">
      <summary>See DXGI_FORMAT_P016.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.P208">
      <summary>See DXGI_FORMAT_P208.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.P8">
      <summary>See DXGI_FORMAT_P8.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R10G10B10A2Typeless">
      <summary>See DXGI_FORMAT_R10G10B10A2_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R10G10B10A2UInt">
      <summary>See DXGI_FORMAT_R10G10B10A2_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R10G10B10A2UIntNormalized">
      <summary>See DXGI_FORMAT_R10G10B10A2_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R10G10B10XRBiasA2UIntNormalized">
      <summary>See DXGI_FORMAT_R10G10B10_XR_BIAS_A2_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R11G11B10Float">
      <summary>See DXGI_FORMAT_R11G11B10_FLOAT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16Float">
      <summary>See DXGI_FORMAT_R16_FLOAT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16B16A16Float">
      <summary>See DXGI_FORMAT_R16G16B16A16_FLOAT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16B16A16Int">
      <summary>See DXGI_FORMAT_R16G16B16A16_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16B16A16IntNormalized">
      <summary>See DXGI_FORMAT_R16G16B16A16_SNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16B16A16Typeless">
      <summary>See DXGI_FORMAT_R16G16B16A16_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16B16A16UInt">
      <summary>See DXGI_FORMAT_R16G16B16A16_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16B16A16UIntNormalized">
      <summary>See DXGI_FORMAT_R16G16B16A16_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16Float">
      <summary>See DXGI_FORMAT_R16G16_FLOAT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16Int">
      <summary>See DXGI_FORMAT_R16G16_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16IntNormalized">
      <summary>See DXGI_FORMAT_R16G16_SNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16Typeless">
      <summary>See DXGI_FORMAT_R16G16_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16UInt">
      <summary>See DXGI_FORMAT_R16G16_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16G16UIntNormalized">
      <summary>See DXGI_FORMAT_R16G16_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16Int">
      <summary>See DXGI_FORMAT_R16_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16IntNormalized">
      <summary>See DXGI_FORMAT_R16_SNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16Typeless">
      <summary>See DXGI_FORMAT_R16_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16UInt">
      <summary>See DXGI_FORMAT_R16_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R16UIntNormalized">
      <summary>See DXGI_FORMAT_R16_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R1UIntNormalized">
      <summary>See DXGI_FORMAT_R1_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R24G8Typeless">
      <summary>See DXGI_FORMAT_R24G8_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R24UIntNormalizedX8Typeless">
      <summary>See DXGI_FORMAT_R24_UNORM_X8_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32Float">
      <summary>See DXGI_FORMAT_R32_FLOAT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32FloatX8X24Typeless">
      <summary>See DXGI_FORMAT_R32_FLOAT_X8X24_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32B32A32Float">
      <summary>See DXGI_FORMAT_R32G32B32A32_FLOAT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32B32A32Int">
      <summary>See DXGI_FORMAT_R32G32B32A32_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32B32A32Typeless">
      <summary>See DXGI_FORMAT_R32G32B32A32_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32B32A32UInt">
      <summary>See DXGI_FORMAT_R32G32B32A32_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32B32Float">
      <summary>See DXGI_FORMAT_R32G32B32_FLOAT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32B32Int">
      <summary>See DXGI_FORMAT_R32G32B32_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32B32Typeless">
      <summary>See DXGI_FORMAT_R32G32B32_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32B32UInt">
      <summary>See DXGI_FORMAT_R32G32B32_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32Float">
      <summary>See DXGI_FORMAT_R32G32_FLOAT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32Int">
      <summary>See DXGI_FORMAT_R32G32_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32Typeless">
      <summary>See DXGI_FORMAT_R32G32_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G32UInt">
      <summary>See DXGI_FORMAT_R32G32_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32G8X24Typeless">
      <summary>See DXGI_FORMAT_R32G8X24_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32Int">
      <summary>See DXGI_FORMAT_R32_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32Typeless">
      <summary>See DXGI_FORMAT_R32_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R32UInt">
      <summary>See DXGI_FORMAT_R32_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8B8A8Int">
      <summary>See DXGI_FORMAT_R8G8B8A8_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8B8A8IntNormalized">
      <summary>See DXGI_FORMAT_R8G8B8A8_SNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8B8A8Typeless">
      <summary>See DXGI_FORMAT_R8G8B8A8_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8B8A8UInt">
      <summary>See DXGI_FORMAT_R8G8B8A8_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8B8A8UIntNormalized">
      <summary>See DXGI_FORMAT_R8G8B8A8_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8B8A8UIntNormalizedSrgb">
      <summary>See DXGI_FORMAT_R8G8B8A8_UNORM_SRGB.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8B8G8UIntNormalized">
      <summary>See DXGI_FORMAT_R8G8_B8G8_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8Int">
      <summary>See DXGI_FORMAT_R8G8_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8IntNormalized">
      <summary>See DXGI_FORMAT_R8G8_SNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8Typeless">
      <summary>See DXGI_FORMAT_R8G8_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8UInt">
      <summary>See DXGI_FORMAT_R8G8_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8G8UIntNormalized">
      <summary>See DXGI_FORMAT_R8G8_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8Int">
      <summary>See DXGI_FORMAT_R8_SINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8IntNormalized">
      <summary>See DXGI_FORMAT_R8_SNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8Typeless">
      <summary>See DXGI_FORMAT_R8_TYPELESS.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8UInt">
      <summary>See DXGI_FORMAT_R8_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R8UIntNormalized">
      <summary>See DXGI_FORMAT_R8_UNORM.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.R9G9B9E5SharedExponent">
      <summary>See DXGI_FORMAT_R9G9B9E5_SHAREDEXP.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.SamplerFeedbackMinMipOpaque">
      <summary>See DXGI_FORMAT_SAMPLER_FEEDBACK_MIN_MIP_OPAQUE.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.SamplerFeedbackMipRegionUsedOpaque">
      <summary>See DXGI_FORMAT_SAMPLER_FEEDBACK_MIP_REGION_USED_OPAQUE.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.Unknown">
      <summary>See DXGI_FORMAT_UNKNOWN.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.V208">
      <summary>See DXGI_FORMAT_V208.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.V408">
      <summary>See DXGI_FORMAT_V408.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.X24TypelessG8UInt">
      <summary>See DXGI_FORMAT_X24_TYPELESS_G8_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.X32TypelessG8X24UInt">
      <summary>See DXGI_FORMAT_X32_TYPELESS_G8X24_UINT.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.Y210">
      <summary>See DXGI_FORMAT_Y210.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.Y216">
      <summary>See DXGI_FORMAT_Y216.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.Y410">
      <summary>See DXGI_FORMAT_Y410.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.Y416">
      <summary>See DXGI_FORMAT_Y416.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPixelFormat.Yuy2">
      <summary>See DXGI_FORMAT_YUY2.</summary>
    </member>
    <member name="T:Microsoft.Graphics.DirectX.DirectXPrimitiveTopology">
      <summary>Values that indicate how the pipeline interprets vertex data that is bound to the input-assembler stage. These primitive topology values determine how the vertex data is rendered on screen. This is a Windows Runtime equivalent of the D3D_PRIMITIVE_TOPOLOGY enumeration.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPrimitiveTopology.LineList">
      <summary>See D3D_PRIMITIVE_TOPOLOGY_LINELIST.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPrimitiveTopology.LineStrip">
      <summary>See D3D_PRIMITIVE_TOPOLOGY_LINESTRIP.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPrimitiveTopology.PointList">
      <summary>See D3D_PRIMITIVE_TOPOLOGY_POINTLIST.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPrimitiveTopology.TriangleList">
      <summary>See D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPrimitiveTopology.TriangleStrip">
      <summary>See D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP.</summary>
    </member>
    <member name="F:Microsoft.Graphics.DirectX.DirectXPrimitiveTopology.Undefined">
      <summary>See D3D_PRIMITIVE_TOPOLOGY_UNDEFINED.</summary>
    </member>
    <member name="T:Microsoft.Graphics.Display.DisplayAdvancedColorInfo">
      <summary>Contains a snapshot of the Advanced Color-related information of a monitor or display driver.</summary>
    </member>
    <member name="M:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.IsAdvancedColorKindAvailable(Microsoft.Graphics.Display.DisplayAdvancedColorKind)">
      <summary>Returns a Boolean value that indicates whether or not the specified Advanced Color kind value is inherently supported by the monitor or display driver.</summary>
      <param name="kind">The Advanced Color kind to check.</param>
      <returns>true if the specified Advanced Color kind is supported; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.IsHdrMetadataFormatCurrentlySupported(Microsoft.Graphics.Display.DisplayHdrMetadataFormat)">
      <summary>Returns a Boolean value that indicates whether or not the specified HDR metadata format is supported by the display in its current state.</summary>
      <param name="format">The HDR metadata format to check.</param>
      <returns>true if the format is supported; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.BluePrimary">
      <summary>Gets the native Blue color primary for the display.</summary>
      <returns>The native Blue color primary for the display.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.CurrentAdvancedColorKind">
      <summary>Gets the DisplayAdvancedColorKind that is currently being set on the display.</summary>
      <returns>The DisplayAdvancedColorKind that is currently being set on the display.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.GreenPrimary">
      <summary>Gets the native Green color primary for the display.</summary>
      <returns>The native Green color primary for the display.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.MaxAverageFullFrameLuminanceInNits">
      <summary>Gets the maximum frame average luminance of the display that can be achieved on the whole display.</summary>
      <returns>The maximum frame average luminance of the display that can be achieved on the whole display.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.MaxLuminanceInNits">
      <summary>Gets the maximum peak luminance of the display in nits, which is usually valid for a small part of the display.</summary>
      <returns>The maximum peak luminance of the display in nits.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.MinLuminanceInNits">
      <summary>Get the minimum luminance of the display in nits.</summary>
      <returns>The minimum luminance of the display in nits.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.RedPrimary">
      <summary>Gets the native Red color primary for the display.</summary>
      <returns>The native Red color primary for the display.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.SdrWhiteLevelInNits">
      <summary>Get the luminance value, in nits, that is currently being used by the operating system for all of the SDR content on this display.</summary>
      <returns>The luminance value, in nits.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayAdvancedColorInfo.WhitePoint">
      <summary>Gets the native white point for the display.</summary>
      <returns>The native white point for the display.</returns>
    </member>
    <member name="T:Microsoft.Graphics.Display.DisplayAdvancedColorKind">
      <summary>Describes the kind of Advanced Color that the display supports.</summary>
    </member>
    <member name="F:Microsoft.Graphics.Display.DisplayAdvancedColorKind.HighDynamicRange">
      <summary>The display supports high dynamic range. In this case, it's safe to assume that OS composition is being done using an RGB:FP16 surface encoded as scRGB gamma. The actual wire signaling is usually done using ST2084.</summary>
    </member>
    <member name="F:Microsoft.Graphics.Display.DisplayAdvancedColorKind.StandardDynamicRange">
      <summary>The display supports only standard dynamic range. In this case, it's safe to assume that operating system (OS) composition is being done using an RGB:8 surface encoded as sRGB gamma. The actual wire signaling is usually done using sRGB.</summary>
    </member>
    <member name="F:Microsoft.Graphics.Display.DisplayAdvancedColorKind.WideColorGamut">
      <summary>The display supports Wide Color Gamut. In this case, it's safe to assume that OS composition is being done using an RGB:FP16 surface encoded as scRGB gamma. The actual wire signaling is usually done using sRGB.</summary>
    </member>
    <member name="T:Microsoft.Graphics.Display.DisplayHdrMetadataFormat">
      <summary>Describes the HDR metadata format.</summary>
    </member>
    <member name="F:Microsoft.Graphics.Display.DisplayHdrMetadataFormat.Hdr10">
      <summary>The specified display supports the HDR10 style metadata, as per the HDR10 specification.</summary>
    </member>
    <member name="F:Microsoft.Graphics.Display.DisplayHdrMetadataFormat.Hdr10Plus">
      <summary>The specified display supports the HDR10Plus style of metadata, as per the HDR10Plus specification.</summary>
    </member>
    <member name="T:Microsoft.Graphics.Display.DisplayInformation">
      <summary>Monitors and controls display-related information for a top-level window or display monitor. The class provides events to allow clients to monitor for changes affecting which display(s) the view resides on, as well as changes in displays that can affect the app window.</summary>
    </member>
    <member name="E:Microsoft.Graphics.Display.DisplayInformation.AdvancedColorInfoChanged">
      <summary>Raised when the advanced color information is changed.</summary>
    </member>
    <member name="E:Microsoft.Graphics.Display.DisplayInformation.ColorProfileChanged">
      <summary>Occurs when the physical display's color profile changes.</summary>
    </member>
    <member name="E:Microsoft.Graphics.Display.DisplayInformation.Destroyed">
      <summary>Raised when the DisplayInformation object is destroyed.</summary>
    </member>
    <member name="E:Microsoft.Graphics.Display.DisplayInformation.IsStereoEnabledChanged">
      <summary>Occurs when the IsStereoEnabled property changes due to a change in support for stereoscopic 3D.</summary>
    </member>
    <member name="M:Microsoft.Graphics.Display.DisplayInformation.Close">
      <summary>Closes the DisplayInformation object, and releases system resources.</summary>
    </member>
    <member name="M:Microsoft.Graphics.Display.DisplayInformation.CreateForDisplayId(Microsoft.UI.DisplayId)">
      <summary>Creates a new DisplayInformation object for the specified DisplayId.

&gt; [!NOTE]
&gt; You must call this method from a thread that has a Microsoft.UI.Dispatching.DispatcherQueue already running.</summary>
      <param name="displayId">The display monitor identifier for which to create a DisplayInformation.</param>
      <returns>The new DisplayInformation object.</returns>
    </member>
    <member name="M:Microsoft.Graphics.Display.DisplayInformation.CreateForWindowId(Microsoft.UI.WindowId)">
      <summary>Creates a new DisplayInformation object for the specified WindowId.

&gt; [!NOTE]
&gt; You must call this method from a thread that has a Microsoft.UI.Dispatching.DispatcherQueue already running.</summary>
      <param name="windowId">The top-level window identifier for which to create a DisplayInformation.</param>
      <returns>The new DisplayInformation object.</returns>
    </member>
    <member name="M:Microsoft.Graphics.Display.DisplayInformation.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="M:Microsoft.Graphics.Display.DisplayInformation.GetAdvancedColorInfo">
      <summary>Retrieves the Advanced Color information.</summary>
      <returns>The Advanced Color information.</returns>
    </member>
    <member name="M:Microsoft.Graphics.Display.DisplayInformation.GetColorProfile">
      <summary>Retrieves the default International Color Consortium (ICC) color profile that is associated with the physical display.</summary>
      <returns>The default International Color Consortium (ICC) color profile that is associated with the physical display.</returns>
    </member>
    <member name="M:Microsoft.Graphics.Display.DisplayInformation.GetColorProfileAsync">
      <summary>Asynchronously retrieves the default International Color Consortium (ICC) color profile that is associated with the physical display.</summary>
      <returns>An asynchronous operation object which, when it completes, contains the color profile.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayInformation.DispatcherQueue">
      <summary>Gets the dispatcher queue associated with the DisplayInformation object.</summary>
      <returns>The dispatcher queue associated with the DisplayInformation object.</returns>
    </member>
    <member name="P:Microsoft.Graphics.Display.DisplayInformation.IsStereoEnabled">
      <summary>Gets a value that indicates whether or not the device supports stereoscopic 3D.</summary>
      <returns>true if the device is capable of stereoscopic 3D (and stereoscopic 3D is currently enabled); otherwise, false.</returns>
    </member>
  </members>
</doc>