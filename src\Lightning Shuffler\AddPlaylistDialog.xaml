<?xml version="1.0" encoding="utf-8"?>
<ContentDialog
    x:Class="Lightning_Shuffler.AddPlaylistDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:Lightning_Shuffler"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Add Playlist"
    PrimaryButtonText="Add"
    SecondaryButtonText="Cancel"
    DefaultButton="Primary"
    Background="{StaticResource DarkSurfaceBrush}"
    Foreground="{StaticResource TextPrimaryBrush}">

    <Grid Padding="24" MinWidth="400">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0"
                   Text="Add YouTube Playlist"
                   FontSize="20"
                   FontWeight="SemiBold"
                   Foreground="{StaticResource TextPrimaryBrush}"
                   Margin="0,0,0,16"/>

        <!-- Description -->
        <TextBlock Grid.Row="1"
                   Text="Enter a YouTube playlist URL to add it to your collection."
                   FontSize="14"
                   Foreground="{StaticResource TextSecondaryBrush}"
                   TextWrapping="Wrap"
                   Margin="0,0,0,16"/>

        <!-- URL Input -->
        <StackPanel Grid.Row="2" Margin="0,0,0,16">
            <TextBlock Text="Playlist URL"
                       FontSize="14"
                       FontWeight="SemiBold"
                       Foreground="{StaticResource TextPrimaryBrush}"
                       Margin="0,0,0,8"/>
            
            <TextBox x:Name="PlaylistUrlTextBox"
                     Style="{StaticResource ModernTextBoxStyle}"
                     PlaceholderText="https://www.youtube.com/playlist?list=..."
                     TextChanged="PlaylistUrlTextBox_TextChanged"/>
        </StackPanel>

        <!-- Status/Error Message -->
        <Border Grid.Row="3"
                x:Name="StatusBorder"
                Background="{StaticResource DarkSurfaceVariantBrush}"
                BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="1"
                CornerRadius="6"
                Padding="12,8"
                Visibility="Collapsed">
            <StackPanel Orientation="Horizontal">
                <FontIcon x:Name="StatusIcon"
                          FontSize="16"
                          Margin="0,0,8,0"
                          VerticalAlignment="Center"/>
                <TextBlock x:Name="StatusText"
                           FontSize="13"
                           VerticalAlignment="Center"
                           TextWrapping="Wrap"/>
            </StackPanel>
        </Border>
    </Grid>
</ContentDialog>
