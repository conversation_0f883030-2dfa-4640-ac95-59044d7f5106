<!-- Copyright (c) Microsoft Corporation. All rights reserved. -->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="uap10.0">
      <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Graphics.Imaging.winmd">
          <Implementation>Microsoft.Graphics.Imaging.dll</Implementation>
          <Private>false</Private>
      </Reference>
      <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.AI.winmd">
          <Private>false</Private>
      </Reference>
      <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.AI.ContentSafety.winmd">
          <Implementation>Microsoft.Windows.AI.ContentSafety.dll</Implementation>
          <Private>false</Private>
      </Reference>
      <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.AI.Foundation.winmd">
          <Implementation>Microsoft.Windows.AI.Text.dll</Implementation>
          <Private>false</Private>
      </Reference>
      <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.AI.Imaging.winmd">
          <Implementation>Microsoft.Windows.AI.Imaging.dll</Implementation>
          <Private>false</Private>
      </Reference>
      <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.AI.Text.winmd">
          <Implementation>Microsoft.Windows.AI.Text.dll</Implementation>
          <Private>false</Private>
      </Reference>
      <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.AI.winmd">
          <Private>false</Private>
      </Reference>
  </ItemGroup>
</Project>