#pragma once
#include "VideoItemViewModel.g.h"

namespace winrt::Lightning_Shuffler::implementation
{
    struct VideoItemViewModel : VideoItemViewModelT<VideoItemViewModel>
    {
        VideoItemViewModel() = default;
        VideoItemViewModel(hstring const& title, hstring const& author, hstring const& thumbnailUrl);

        hstring Title();
        void Title(hstring const& value);

        hstring Author();
        void Author(hstring const& value);

        hstring ThumbnailUrl();
        void ThumbnailUrl(hstring const& value);

        winrt::event_token PropertyChanged(Microsoft::UI::Xaml::Data::PropertyChangedEventHandler const& handler);
        void PropertyChanged(winrt::event_token const& token) noexcept;

    private:
        hstring m_title;
        hstring m_author;
        hstring m_thumbnailUrl;
        winrt::event<Microsoft::UI::Xaml::Data::PropertyChangedEventHandler> m_propertyChanged;

        void RaisePropertyChanged(hstring const& propertyName);
    };
}

namespace winrt::Lightning_Shuffler::factory_implementation
{
    struct VideoItemViewModel : VideoItemViewModelT<VideoItemViewModel, implementation::VideoItemViewModel>
    {
    };
}
