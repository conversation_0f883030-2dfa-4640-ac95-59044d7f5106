// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_PushNotifications_1_H
#define WINRT_Microsoft_Windows_PushNotifications_1_H
#include "winrt/impl/Microsoft.Windows.PushNotifications.0.h"
WINRT_EXPORT namespace winrt::Microsoft::Windows::PushNotifications
{
    struct WINRT_IMPL_EMPTY_BASES IPushNotificationChannel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationChannel>
    {
        IPushNotificationChannel(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationChannel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPushNotificationCreateChannelResult :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationCreateChannelResult>
    {
        IPushNotificationCreateChannelResult(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationCreateChannelResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPushNotificationManager :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationManager>
    {
        IPushNotificationManager(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPushNotificationManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationManagerStatics>
    {
        IPushNotificationManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPushNotificationReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationReceivedEventArgs>
    {
        IPushNotificationReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
