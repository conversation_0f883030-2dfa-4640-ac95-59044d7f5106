#include "pch.h"
#include "VideoItemViewModel.h"
#if __has_include("VideoItemViewModel.g.cpp")
#include "VideoItemViewModel.g.cpp"
#endif

using namespace winrt;
using namespace Microsoft::UI::Xaml;

namespace winrt::Lightning_Shuffler::implementation
{
    VideoItemViewModel::VideoItemViewModel(hstring const& title, hstring const& author, hstring const& thumbnailUrl)
        : m_title(title), m_author(author), m_thumbnailUrl(thumbnailUrl)
    {
    }

    hstring VideoItemViewModel::Title()
    {
        return m_title;
    }

    void VideoItemViewModel::Title(hstring const& value)
    {
        if (m_title != value)
        {
            m_title = value;
            RaisePropertyChanged(L"Title");
        }
    }

    hstring VideoItemViewModel::Author()
    {
        return m_author;
    }

    void VideoItemViewModel::Author(hstring const& value)
    {
        if (m_author != value)
        {
            m_author = value;
            RaisePropertyChanged(L"Author");
        }
    }

    hstring VideoItemViewModel::ThumbnailUrl()
    {
        return m_thumbnailUrl;
    }

    void VideoItemViewModel::ThumbnailUrl(hstring const& value)
    {
        if (m_thumbnailUrl != value)
        {
            m_thumbnailUrl = value;
            RaisePropertyChanged(L"ThumbnailUrl");
        }
    }

    winrt::event_token VideoItemViewModel::PropertyChanged(Microsoft::UI::Xaml::Data::PropertyChangedEventHandler const& handler)
    {
        return m_propertyChanged.add(handler);
    }

    void VideoItemViewModel::PropertyChanged(winrt::event_token const& token) noexcept
    {
        m_propertyChanged.remove(token);
    }

    void VideoItemViewModel::RaisePropertyChanged(hstring const& propertyName)
    {
        m_propertyChanged(*this, Microsoft::UI::Xaml::Data::PropertyChangedEventArgs(propertyName));
    }
}
