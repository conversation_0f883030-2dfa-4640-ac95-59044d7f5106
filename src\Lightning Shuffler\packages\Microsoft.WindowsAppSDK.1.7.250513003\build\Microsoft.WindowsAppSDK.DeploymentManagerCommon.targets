<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <!-- Targets file common to both managed and native projects -->

    <PropertyGroup Condition="'$(WindowsAppSdkDeploymentManagerInitialize)'=='' and '$(WindowsAppSDKSelfContained)'!='true' and '$(WindowsPackageType)'=='MSIX' and ('$(OutputType)'=='Exe' or '$(OutputType)'=='Winexe')">
        <!--Allows GenerateDeploymentManagerCS/GenerateDeploymentManagerCpp to run-->
        <WindowsAppSdkDeploymentManagerInitialize>true</WindowsAppSdkDeploymentManagerInitialize>
    </PropertyGroup>

</Project>
