<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <!-- Targets file common to both managed and native projects that helps enable access to Undocked RegFree WinRT-->

    <PropertyGroup Condition="'$(WindowsAppSdkUndockedRegFreeWinRTInitialize)'=='' and '$(WindowsAppSDKSelfContained)'=='true'">
        <!--Allows GenerateUndockedRegFreeWinRTCS/GenerateUndockedRegFreeWinRTCpp to run-->
        <WindowsAppSdkUndockedRegFreeWinRTInitialize>true</WindowsAppSdkUndockedRegFreeWinRTInitialize>
    </PropertyGroup>

    <PropertyGroup Condition="'$(WindowsAppSdkUndockedRegFreeWinRTInitializeLoadLibrary)'=='' and '$(WindowsAppSdkUndockedRegFreeWinRTInitialize)'=='true'">
        <WindowsAppSdkUndockedRegFreeWinRTInitializeLoadLibrary>true</WindowsAppSdkUndockedRegFreeWinRTInitializeLoadLibrary>
    </PropertyGroup>

</Project>
