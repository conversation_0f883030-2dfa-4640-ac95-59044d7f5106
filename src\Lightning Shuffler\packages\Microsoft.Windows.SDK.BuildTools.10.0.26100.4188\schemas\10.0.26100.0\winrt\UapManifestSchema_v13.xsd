<?xml version="1.0" encoding="utf-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://schemas.microsoft.com/appx/manifest/uap/windows10/13"
           xmlns="http://schemas.microsoft.com/appx/manifest/uap/windows10/13"
           xmlns:t="http://schemas.microsoft.com/appx/manifest/types"
           xmlns:f="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
           xmlns:uap6="http://schemas.microsoft.com/appx/manifest/uap/windows10/6"
           xmlns:uap10="http://schemas.microsoft.com/appx/manifest/uap/windows10/10"
           xmlns:uap11="http://schemas.microsoft.com/appx/manifest/uap/windows10/11"
           xmlns:desktop11="http://schemas.microsoft.com/appx/manifest/desktop/windows10/11"
           >

  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/types"/>
  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/foundation/windows10"/>
  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/uap/windows10/6"/>
  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/uap/windows10/11"/>
  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/uap/windows10/10"/>
  <xs:import namespace="http://schemas.microsoft.com/appx/manifest/desktop/windows10/11"/>

  <xs:element name="Extension" substitutionGroup="f:ExtensionChoice">
    <xs:complexType>
      <xs:attribute name="Category" type="t:ST_ExtensionCategory_Uap13" use="required"/>
      <xs:attribute ref="desktop11:AppLifecycleBehavior" use="optional"/>
      <xs:attributeGroup ref="t:ExtensionBaseAttributes"/>
      <xs:attributeGroup ref="uap10:TrustLevelGroup"/>
      <xs:attributeGroup ref="uap11:ManganeseExtensionAttributesGroup"/>
    </xs:complexType>
  </xs:element>

  <xs:element name="HostRuntimeDependency">
    <xs:complexType>
      <xs:attribute name="Name" type="t:ST_AsciiIdentifier" use="required"/>
      <xs:attribute name="Publisher" type="t:ST_Publisher_2010_v2" use="required"/>
      <xs:attribute name="MinVersion" type="t:ST_VersionQuad" use="required"/>
    </xs:complexType>
  </xs:element>

  <xs:element name="AutoUpdate">
    <xs:complexType>
      <xs:all>
        <xs:element name="AppInstaller">
          <xs:complexType>
            <xs:attribute name="File" type="ST_AppInstallerFilePath" use="required"/>
          </xs:complexType>
        </xs:element>
      </xs:all>
    </xs:complexType>
  </xs:element>

  <xs:simpleType name="ST_AppInstallerFilePath">
    <xs:restriction base="t:ST_FileName">
      <xs:pattern value=".+\.([Aa][Pp][Pp][Ii][Nn][Ss][Tt][Aa][Ll][Ll][Ee][Rr])"/>
    </xs:restriction>
  </xs:simpleType>
    
</xs:schema>

