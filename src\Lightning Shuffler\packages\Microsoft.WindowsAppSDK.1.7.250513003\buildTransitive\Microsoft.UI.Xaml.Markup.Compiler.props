﻿<!-- Copyright (c) Microsoft Corporation. Licensed under the MIT License. See LICENSE in the project root for license information. -->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(UsingMicrosoftNETSdk)' != 'true'">

    <!-- This property tells C++/WinRT build tools that they're compiling a WinUI app (Microsoft.UI.Xaml)
         and not an inbox UWP app (Windows.UI.Xaml). It is safe for this property to be set here,
         because it only applies to C++ apps, and not C# one (so 'UseUwpTools' is irrelevant). -->
    <XamlNamespace Condition="'$(XamlNamespace)' == ''">Microsoft.UI.Xaml</XamlNamespace>
  </PropertyGroup>

  <!-- Setup the custom .targets to run as 'CustomBeforeMicrosoftCommonTargets'. We also make
       a copy of the original value so that we can manually import it ourselves after ours. -->
  <PropertyGroup>
    <_OriginalCustomBeforeMicrosoftCommonTargetsFromWinUIProps>$(CustomBeforeMicrosoftCommonTargets)</_OriginalCustomBeforeMicrosoftCommonTargetsFromWinUIProps>
    <CustomBeforeMicrosoftCommonTargets>$(MSBuildThisFileDirectory)Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets</CustomBeforeMicrosoftCommonTargets>
  </PropertyGroup>

   <!-- Add any needed props here, but not they will be executed *before* the .csproj.
        Avoid adding properties here that are condition upon other properties that
        users are expected to set in their .csproj files (eg. 'UseUwpTools').
        See 'Microsoft.UI.Xaml.Markup.Compiler.BeforeCommon.targets'. -->
</Project>