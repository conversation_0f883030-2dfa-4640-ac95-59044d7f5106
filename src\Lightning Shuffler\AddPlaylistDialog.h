#pragma once
#include "AddPlaylistDialog.g.h"

namespace winrt::Lightning_Shuffler::implementation
{
    struct AddPlaylistDialog : AddPlaylistDialogT<AddPlaylistDialog>
    {
        AddPlaylistDialog();

        hstring PlaylistUrl();
        bool IsValidUrl();

        void PlaylistUrlTextBox_TextChanged(winrt::Windows::Foundation::IInspectable const& sender, winrt::Microsoft::UI::Xaml::Controls::TextChangedEventArgs const& e);

    private:
        hstring m_playlistUrl;
        void ValidateUrl();
        void ShowStatus(hstring const& message, bool isError = false);
        void HideStatus();
    };
}

namespace winrt::Lightning_Shuffler::factory_implementation
{
    struct AddPlaylistDialog : AddPlaylistDialogT<AddPlaylistDialog, implementation::AddPlaylistDialog>
    {
    };
}
