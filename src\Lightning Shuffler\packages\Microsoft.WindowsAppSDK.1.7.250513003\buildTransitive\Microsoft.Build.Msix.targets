<?xml version="1.0" encoding="utf-8"?>
<!--
***********************************************************************************************
Microsoft.Build.Msix.targets

WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
          created a backup copy.  Incorrect changes to this file will make it
          impossible to load or build your projects from the command-line or the IDE.

Copyright (C) Microsoft Corporation. All rights reserved.
***********************************************************************************************
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <MsixPackageSupport Condition="'$(MsixPackageSupport)'==''">true</MsixPackageSupport>
    <_MicrosoftBuildMsixTargetsOverriden Condition="'$(MicrosoftBuildMsixLocation)'!=''">true</_MicrosoftBuildMsixTargetsOverriden>
    <_MicrosoftBuildMsixTargetsOverriden Condition="'$(_MicrosoftBuildMsixTargetsOverriden)'==''">false</_MicrosoftBuildMsixTargetsOverriden>
    <_MicrosoftBuildMsixTargetsLocation Condition="'$(_MicrosoftBuildMsixTargetsOverriden)'=='true'">$(MicrosoftBuildMsixLocation)\Targets\</_MicrosoftBuildMsixTargetsLocation>
    <_MicrosoftBuildMsixTargetsLocation Condition="'$(_MicrosoftBuildMsixTargetsOverriden)'==''">$(MSBuildThisFileDirectory)</_MicrosoftBuildMsixTargetsLocation>
  </PropertyGroup>

  <PropertyGroup>
    <MsixDesignTimeTargets>$(_MicrosoftBuildMsixTargetsLocation)Microsoft.Build.Msix.DesignTime.targets</MsixDesignTimeTargets>
    <MsixPackagingTargets>$(_MicrosoftBuildMsixTargetsLocation)Microsoft.Build.Msix.Packaging.targets</MsixPackagingTargets>
    <MsixCppTargets>$(_MicrosoftBuildMsixTargetsLocation)Microsoft.Build.Msix.Cpp.targets</MsixCppTargets>
    <MsixCsTargets>$(_MicrosoftBuildMsixTargetsLocation)Microsoft.Build.Msix.Cs.targets</MsixCsTargets>
  </PropertyGroup>

  <PropertyGroup Condition="'$(_MicrosoftBuildMsixTargetsOverriden)'=='true'">
    <_MsixTaskAssemblyConfig Condition="'$(_MsixTaskAssemblyConfig)'==''">Debug</_MsixTaskAssemblyConfig>
    <MsixTaskAssemblyLocation Condition=" '$(MSBuildRuntimeType)' == 'Core'">$(MicrosoftBuildMsixLocation)\bin\$(_MsixTaskAssemblyConfig)\net6.0-windows$(DotNet6WindowsVersion)\</MsixTaskAssemblyLocation>
    <MsixTaskAssemblyLocation Condition=" '$(MSBuildRuntimeType)' != 'Core'">$(MicrosoftBuildMsixLocation)\bin\$(_MsixTaskAssemblyConfig)\net472\</MsixTaskAssemblyLocation>
    <_PowershellScriptLocation>$(MicrosoftBuildMsixLocation)\AppDevPackageScripts\</_PowershellScriptLocation>
  </PropertyGroup>
  
  <PropertyGroup>
    <ShouldImportMsixDesignTimeTargets Condition="'$(ShouldImportMsixDesignTimeTargets)'==''">$(MsixPackageSupport)</ShouldImportMsixDesignTimeTargets>
    <ShouldImportMsixPackagingTargets Condition="'$(ShouldImportMsixPackagingTargets)'==''">$(MsixPackageSupport)</ShouldImportMsixPackagingTargets>
    <ShouldImportMsixCppTargets Condition="'$(ShouldImportMsixCppTargets)'=='' and '$(_MsixIsNativeProject)'=='true'">$(MsixPackageSupport)</ShouldImportMsixCppTargets>
    <ShouldImportMsixCsTargets Condition="'$(ShouldImportMsixCsTargets)'=='' and '$(_MsixIsNativeProject)'!='true'">$(MsixPackageSupport)</ShouldImportMsixCsTargets>
  </PropertyGroup>
  
  <Import Condition="Exists('$(MsixPackagingTargets)') and '$(ShouldImportMsixPackagingTargets)' == 'true'" Project="$(MsixPackagingTargets)" />
  <Import Condition="Exists('$(MsixCppTargets)') and '$(ShouldImportMsixCppTargets)' == 'true'" Project="$(MsixCppTargets)" />
  <Import Condition="Exists('$(MsixCsTargets)') and '$(ShouldImportMsixCsTargets)' == 'true'" Project="$(MsixCsTargets)" />
  <Import Condition="Exists('$(MsixDesignTimeTargets)') and '$(ShouldImportMsixDesignTimeTargets)' == 'true'" Project="$(MsixDesignTimeTargets)" />
</Project>
