<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <!--
      Microsoft.Common.CurrentVersion.targets ends up importing a bunch of .NET based targets, which import Microsoft.Nuget.targets,
      where they has a target called ComputeNetCoreFrameworkInjectionParameters, this target only runs when the 
      _ComputeNetCoreFrameworkInjectionParametersBeforeTargets is defined, and we don't need/want this target to run in C++ based projects.
    -->
    <NetfxCoreRuntimeTargets>Cpp-Projects-Dont-Import-NetCore</NetfxCoreRuntimeTargets>
  </PropertyGroup>
</Project>
