// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Security_AccessControl_0_H
#define WINRT_Microsoft_Windows_Security_AccessControl_0_H
WINRT_EXPORT namespace winrt::Microsoft::Windows::Security::AccessControl
{
    struct ISecurityDescriptorHelpersStatics;
    struct SecurityDescriptorHelpers;
    struct AppContainerNameAndAccess;
    struct AccessControlContract;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::Windows::Security::AccessControl::ISecurityDescriptorHelpersStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::Security::AccessControl::SecurityDescriptorHelpers>{ using type = class_category; };
    template <> struct category<winrt::Microsoft::Windows::Security::AccessControl::AppContainerNameAndAccess>{ using type = struct_category<hstring, uint32_t>; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Security::AccessControl::SecurityDescriptorHelpers> = L"Microsoft.Windows.Security.AccessControl.SecurityDescriptorHelpers";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Security::AccessControl::AppContainerNameAndAccess> = L"Microsoft.Windows.Security.AccessControl.AppContainerNameAndAccess";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Security::AccessControl::ISecurityDescriptorHelpersStatics> = L"Microsoft.Windows.Security.AccessControl.ISecurityDescriptorHelpersStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::Security::AccessControl::AccessControlContract> = L"Microsoft.Windows.Security.AccessControl.AccessControlContract";
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::Security::AccessControl::ISecurityDescriptorHelpersStatics>{ 0x14FA9E8D,0x59F0,0x5017,{ 0x85,0x2F,0x3A,0xE2,0x4F,0xD5,0xEB,0xB1 } }; // 14FA9E8D-59F0-5017-852F-3AE24FD5EBB1
    template <> struct abi<winrt::Microsoft::Windows::Security::AccessControl::ISecurityDescriptorHelpersStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetSddlForAppContainerNames(uint32_t, struct struct_Microsoft_Windows_Security_AccessControl_AppContainerNameAndAccess*, void*, uint32_t, void**) noexcept = 0;
            virtual int32_t __stdcall GetSecurityDescriptorBytesFromAppContainerNames(uint32_t, struct struct_Microsoft_Windows_Security_AccessControl_AppContainerNameAndAccess*, void*, uint32_t, uint32_t* __resultSize, uint8_t**) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_Windows_Security_AccessControl_ISecurityDescriptorHelpersStatics
    {
        auto GetSddlForAppContainerNames(array_view<winrt::Microsoft::Windows::Security::AccessControl::AppContainerNameAndAccess const> accessRequests, param::hstring const& principalStringSid, uint32_t principalAccessMask) const;
        auto GetSecurityDescriptorBytesFromAppContainerNames(array_view<winrt::Microsoft::Windows::Security::AccessControl::AppContainerNameAndAccess const> accessRequests, param::hstring const& principalStringSid, uint32_t principalAccessMask) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::Security::AccessControl::ISecurityDescriptorHelpersStatics>
    {
        template <typename D> using type = consume_Microsoft_Windows_Security_AccessControl_ISecurityDescriptorHelpersStatics<D>;
    };
    struct struct_Microsoft_Windows_Security_AccessControl_AppContainerNameAndAccess
    {
        void* appContainerName;
        uint32_t accessMask;
    };
    template <> struct abi<Microsoft::Windows::Security::AccessControl::AppContainerNameAndAccess>
    {
        using type = struct_Microsoft_Windows_Security_AccessControl_AppContainerNameAndAccess;
    };
}
#endif
