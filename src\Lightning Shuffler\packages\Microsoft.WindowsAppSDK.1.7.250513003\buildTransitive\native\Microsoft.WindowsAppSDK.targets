<!--
  Copyright (c) Microsoft Corporation. Licensed under the MIT License
-->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <!--Imports for each component Targets-->
    <Import Project="$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.Custom.targets" Condition="Exists('$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.Custom.targets')" />
    <Import Project="$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.DWrite.targets" Condition="Exists('$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.DWrite.targets')" />
    <Import Project="$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.Foundation.targets" Condition="Exists('$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.Foundation.targets')" />
    <Import Project="$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.WinUI.targets" Condition="Exists('$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.WinUI.targets')" />
    <Import Project="$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.InteractiveExperiences.targets" Condition="Exists('$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.InteractiveExperiences.targets')" />
    <Import Project="$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.Widgets.targets" Condition="Exists('$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.Widgets.targets')" />
    <Import Project="$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.AIFabric.PublicTransportPackage.targets" Condition="Exists('$(MSBuildThisFileDirectory)Microsoft.WindowsAppSDK.AIFabric.PublicTransportPackage.targets')" />

    <!--Import Arm64EC configuration support -->
    <Import Project="$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.Arm64Ec.targets" Condition="'$(Platform)'=='arm64ec'"/>

    <!--Import after all other props to ensure WindowsAppSDKSelfContained is set (required for SelfContained projects)-->
    <Import Project="$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.SelfContained.targets" Condition="'$(WindowsAppSDKSelfContained)'=='true'"/>
    
    <!--Import after all other props to ensure WindowsAppSDKFrameworkPackageReference is set (required for C++ projects)-->
    <Import Project="$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.AppXReference.props" Condition="'$(WindowsAppSDKFrameworkPackageReference)'!='false'"/>

</Project>
