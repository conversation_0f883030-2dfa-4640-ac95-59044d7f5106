// Copyright (c) Microsoft Corporation and Contributors.
// Licensed under the MIT License.

namespace Microsoft.Windows.ApplicationModel.Resources
{
    [contractversion(2)]
    apicontract MrtCoreContract{};

    [contract(MrtCoreContract, 1)]
    [default_interface]
    runtimeclass ResourceLoader
    {
        ResourceLoader();
        ResourceLoader(String fileName);
        ResourceLoader(String fileName, String resourceMap);

        static String GetDefaultResourceFilePath();

        String GetString(String resourceId);
        String GetStringForUri(Windows.Foundation.Uri resourceUri);
    }

    [contract(MrtCoreContract, 1)]
    runtimeclass ResourceNotFoundEventArgs
    {
        ResourceContext Context { get; };
        String Name { get; };
        void SetResolvedCandidate(ResourceCandidate candidate);
    }

    [contract(MrtCoreContract, 1)]
    runtimeclass ResourceMap
    {
        UInt32 ResourceCount { get; };

        ResourceMap GetSubtree(String reference);
        ResourceMap TryGetSubtree(String reference);

        ResourceCandidate GetValue(String resource);
        [method_name("GetValueWithContext")]
        ResourceCandidate GetValue(String resource, ResourceContext context);

        IKeyValuePair<String, ResourceCandidate> GetValueByIndex(UInt32 index);
        [method_name("GetValueByIndexWithContext")]
        IKeyValuePair<String, ResourceCandidate> GetValueByIndex(UInt32 index, ResourceContext context);

        ResourceCandidate TryGetValue(String resource);
        [method_name("TryGetValueWithContext")]
        ResourceCandidate TryGetValue(String resource, ResourceContext context);
    }

    [contract(MrtCoreContract, 1)]
    interface IResourceManager
    {
        ResourceMap MainResourceMap{ get; };
        ResourceContext CreateResourceContext();

        event Windows.Foundation.TypedEventHandler<ResourceManager, ResourceNotFoundEventArgs> ResourceNotFound;
    }

    [contract(MrtCoreContract, 1)]
    [default_interface]
    runtimeclass ResourceManager : [default] IResourceManager
    {
        ResourceManager();
        ResourceManager(String fileName);
    }

    [contract(MrtCoreContract, 1)]
    interface IResourceContext
    {
        IMap<String, String> QualifierValues{ get; };
    }

    [contract(MrtCoreContract, 1)]
    [default_interface]
    runtimeclass ResourceContext : [default] IResourceContext
    {
    }

    [contract(MrtCoreContract, 1)]
    enum ResourceCandidateKind
    {
        Unknown = 0,
        String,
        FilePath,
        EmbeddedData,
    };

    [contract(MrtCoreContract, 1)]
    runtimeclass ResourceCandidate
    {
        ResourceCandidate(ResourceCandidateKind kind, String data);
        ResourceCandidate(byte[] data);

        String ValueAsString { get; };
        byte[] ValueAsBytes { get; };
        ResourceCandidateKind Kind { get; };

        IMapView<String, String> QualifierValues { get; };
    }

    [contract(MrtCoreContract, 1)]
    runtimeclass KnownResourceQualifierName
    {
        static String Contrast { get; };
        static String Custom { get; };
        static String DeviceFamily{ get; };
        static String HomeRegion{ get; };
        static String Language { get; };
        static String LayoutDirection{ get; };
        static String Scale { get; };
        static String TargetSize { get; };
        static String Theme { get; };
    }
} // namespace Microsoft.Windows.ApplicationModel.Resources

namespace Microsoft.Windows.Globalization
{
    [contract(Microsoft.Windows.ApplicationModel.Resources.MrtCoreContract, 2)]
    runtimeclass ApplicationLanguages
    {
        static IVectorView<String> Languages { get; };
        static IVectorView<String> ManifestLanguages { get; };
        static String PrimaryLanguageOverride;
    }

} // namespace Microsoft.Windows.Globalization
