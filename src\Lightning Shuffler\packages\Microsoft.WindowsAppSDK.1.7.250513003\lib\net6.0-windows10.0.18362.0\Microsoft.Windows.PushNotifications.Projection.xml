<doc>
  <assembly>
    <name>Microsoft.Windows.PushNotifications.Projection</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.PushNotifications.PushNotificationChannel">
      <summary>Represents a push notification channel.</summary>
    </member>
    <member name="M:Microsoft.Windows.PushNotifications.PushNotificationChannel.Close">
      <summary>Closes the push notification channel, after which a new channel must be requested with CreateChannelAsync</summary>
    </member>
    <member name="P:Microsoft.Windows.PushNotifications.PushNotificationChannel.ExpirationTime">
      <summary>Gets the expiration time of the push notification channel, after which a new channel must be requested with CreateChannelAsync.</summary>
      <returns>The expiration time of the push notification channel</returns>
    </member>
    <member name="P:Microsoft.Windows.PushNotifications.PushNotificationChannel.Uri">
      <summary>Gets the URI representing the push notification channel.</summary>
    </member>
    <member name="T:Microsoft.Windows.PushNotifications.PushNotificationChannelStatus">
      <summary>Specifies the status of a push channel created with a call to CreateChannelAsync.</summary>
    </member>
    <member name="F:Microsoft.Windows.PushNotifications.PushNotificationChannelStatus.CompletedFailure">
      <summary>The push channel creation request failed with a critical internal error. Check the PushNotificationCreateChannelResult.ExtendedError property to get the last extended error seen when creating a channel request.</summary>
    </member>
    <member name="F:Microsoft.Windows.PushNotifications.PushNotificationChannelStatus.CompletedSuccess">
      <summary>The push channel creation request completed succesfully.</summary>
    </member>
    <member name="F:Microsoft.Windows.PushNotifications.PushNotificationChannelStatus.InProgress">
      <summary>The push channel creation request is in progress.</summary>
    </member>
    <member name="F:Microsoft.Windows.PushNotifications.PushNotificationChannelStatus.InProgressRetry">
      <summary>The push channel creation request is in progress and is in a backoff retry state. Check the PushNotificationCreateChannelResult.ExtendedError property to get the last extended error seen when creating a channel request.</summary>
    </member>
    <member name="T:Microsoft.Windows.PushNotifications.PushNotificationCreateChannelResult">
      <summary>Represents the result of a push channel creation request initiated with a call to CreateChannelAsync.</summary>
    </member>
    <member name="P:Microsoft.Windows.PushNotifications.PushNotificationCreateChannelResult.Channel">
      <summary>Gets the push notification channel resulting from a successful call to CreateChannelAsync.</summary>
      <returns>The created push notification channel.</returns>
    </member>
    <member name="P:Microsoft.Windows.PushNotifications.PushNotificationCreateChannelResult.ExtendedError">
      <summary>Get the last extended error seen during channel creation.</summary>
      <returns>An object representing the extended error.</returns>
    </member>
    <member name="P:Microsoft.Windows.PushNotifications.PushNotificationCreateChannelResult.Status">
      <summary>Gets the status of a push channel creation request initiated with a call to CreateChannelAsync.</summary>
      <returns>The status of the push channel creation request.</returns>
    </member>
    <member name="T:Microsoft.Windows.PushNotifications.PushNotificationCreateChannelStatus">
      <summary>Provides status information for a push channel creation request initiated with a call to CreateChannelAsync.</summary>
    </member>
    <member name="F:Microsoft.Windows.PushNotifications.PushNotificationCreateChannelStatus.extendedError">
      <summary>The last extended error seen during channel creation.</summary>
    </member>
    <member name="F:Microsoft.Windows.PushNotifications.PushNotificationCreateChannelStatus.retryCount">
      <summary>The current count of retry attempts made by the platform.</summary>
    </member>
    <member name="F:Microsoft.Windows.PushNotifications.PushNotificationCreateChannelStatus.status">
      <summary>A member of the PushNotificationChannelStatus enumeration specifying the current status of the push notification channel.</summary>
    </member>
    <member name="T:Microsoft.Windows.PushNotifications.PushNotificationManager">
      <summary>Provides APIs for receiving and registering to receive push notifications.

&gt; [!NOTE]
&gt; The PushNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="E:Microsoft.Windows.PushNotifications.PushNotificationManager.PushReceived">
      <summary>Raised when a push notification for the app is received by the platform.

&gt; [!NOTE]
&gt; The PushNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="M:Microsoft.Windows.PushNotifications.PushNotificationManager.CreateChannelAsync(System.Guid)">
      <summary>Asynchronously requests a push channel from the Windows Push Notification Service (WNS).

&gt; [!NOTE]
&gt; The PushNotificationManager class has a dependency on the Singleton package.</summary>
      <param name="remoteId">The remote identifier for the created push channel. This value, known as the Azure AppId, is a GUID that matches the application ID specified in Azure Active Directory (AAD). For information on getting your application ID, see [Use the portal to create an Azure AD application and service principal that can access resources](/azure/active-directory/develop/howto-create-service-principal-portal
)</param>
      <returns>An asynchronous action with progress that returns a PushNotificationCreateChannelResult on completion.</returns>
    </member>
    <member name="M:Microsoft.Windows.PushNotifications.PushNotificationManager.IsSupported">
      <summary>Gets a boolean value indicating if the Microsoft.Windows.PushNotifications notification APIs are supported for the calling app. 

&gt; [!NOTE]
&gt; The PushNotificationManager class has a dependency on the Singleton package.</summary>
      <returns>True if push notifications APIs are supported; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.Windows.PushNotifications.PushNotificationManager.Register">
      <summary>Registers the app to receive PushReceived events when incoming notifications are received.

&gt; [!NOTE]
&gt; The PushNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="M:Microsoft.Windows.PushNotifications.PushNotificationManager.Unregister">
      <summary>Unregisters the app from receiving PushReceived events for incoming push notifications.

&gt; [!NOTE]
&gt; The PushNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="M:Microsoft.Windows.PushNotifications.PushNotificationManager.UnregisterAll">
      <summary>Cleans up all registration-related data for push notifications. After this, push notifications for the app will not function until Register is called again.

&gt; [!NOTE]
&gt; The PushNotificationManager class has a dependency on the Singleton package.</summary>
    </member>
    <member name="P:Microsoft.Windows.PushNotifications.PushNotificationManager.Default">
      <summary>Gets the default instance of the PushNotificationManager class.

&gt; [!NOTE]
&gt; The PushNotificationManager class has a dependency on the Singleton package.</summary>
      <returns>The default instance of the PushNotificationManager class.</returns>
    </member>
    <member name="T:Microsoft.Windows.PushNotifications.PushNotificationReceivedEventArgs">
      <summary>Provides data for the PushReceived event.</summary>
    </member>
    <member name="E:Microsoft.Windows.PushNotifications.PushNotificationReceivedEventArgs.Canceled">
      <summary>Raised when the system is going to cancel the background task launched to handle the PushReceived event.</summary>
    </member>
    <member name="M:Microsoft.Windows.PushNotifications.PushNotificationReceivedEventArgs.GetDeferral">
      <summary>Informs the system that the app might continue to perform work after the PushReceived event handler returns.</summary>
      <returns>A background task deferral.</returns>
    </member>
    <member name="P:Microsoft.Windows.PushNotifications.PushNotificationReceivedEventArgs.Payload">
      <summary>Gets the payload of the push notification that triggered the associated PushReceived event.</summary>
      <returns>An array of bytes representing the push notification payload.</returns>
    </member>
  </members>
</doc>