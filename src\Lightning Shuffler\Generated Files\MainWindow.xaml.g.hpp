﻿
//------------------------------------------------------------------------------
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
//------------------------------------------------------------------------------
#include "pch.h"
#include "MainWindow.xaml.h"

#pragma warning(push)
#pragma warning(disable: 4100) // unreferenced formal parameter

namespace winrt::Lightning_Shuffler::implementation
{


    using Application = ::winrt::Microsoft::UI::Xaml::Application;
    using ComponentResourceLocation = ::winrt::Microsoft::UI::Xaml::Controls::Primitives::ComponentResourceLocation;
    using DataTemplate = ::winrt::Microsoft::UI::Xaml::DataTemplate;
    using DependencyObject = ::winrt::Microsoft::UI::Xaml::DependencyObject;
    using DependencyProperty = ::winrt::Microsoft::UI::Xaml::DependencyProperty;
    using IComponentConnector = ::winrt::Microsoft::UI::Xaml::Markup::IComponentConnector;
    using Uri = ::winrt::Windows::Foundation::Uri;
    using XamlBindingHelper = ::winrt::Microsoft::UI::Xaml::Markup::XamlBindingHelper;

    template <typename D, typename ... I>
    void MainWindowT<D, I...>::InitializeComponent()
    {
        if (!_contentLoaded)
        {
            _contentLoaded = true;
            ::winrt::Windows::Foundation::Uri resourceLocator{ L"ms-appx:///MainWindow.xaml" };
            ::winrt::Microsoft::UI::Xaml::Application::LoadComponent(*this, resourceLocator, ComponentResourceLocation::Application);
        }
    }

    template <typename D, typename ... I>
    void MainWindowT<D, I...>::Connect(int32_t connectionId, IInspectable const& target)
    {
        switch (connectionId)
        {
        case 2:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Button>();
                this->VolumeButton(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.Click([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::RoutedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->VolumeButton_Click(p0, p1);
                    }
                });
            }
            break;
        case 3:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Slider>();
                this->VolumeSlider(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.ValueChanged([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::Controls::Primitives::RangeBaseValueChangedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->VolumeSlider_ValueChanged(p0, p1);
                    }
                });
            }
            break;
        case 4:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::FontIcon>();
                this->VolumeIcon(targetElement);
            }
            break;
        case 5:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Button>();
                this->PreviousButton(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.Click([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::RoutedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->PreviousButton_Click(p0, p1);
                    }
                });
            }
            break;
        case 6:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Button>();
                this->PlayPauseButton(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.Click([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::RoutedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->PlayPauseButton_Click(p0, p1);
                    }
                });
            }
            break;
        case 7:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Button>();
                this->NextButton(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.Click([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::RoutedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->NextButton_Click(p0, p1);
                    }
                });
            }
            break;
        case 8:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Button>();
                this->ShuffleButton(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.Click([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::RoutedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->ShuffleButton_Click(p0, p1);
                    }
                });
            }
            break;
        case 9:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Button>();
                this->LoopButton(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.Click([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::RoutedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->LoopButton_Click(p0, p1);
                    }
                });
                targetElement.RightTapped([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::Input::RightTappedRoutedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->LoopButton_RightTapped(p0, p1);
                    }
                });
            }
            break;
        case 10:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::TextBlock>();
                this->LoopCountText(targetElement);
            }
            break;
        case 11:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::FontIcon>();
                this->PlayPauseIcon(targetElement);
            }
            break;
        case 12:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::TextBlock>();
                this->CurrentVideoTitle(targetElement);
            }
            break;
        case 13:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::TextBlock>();
                this->CurrentVideoAuthor(targetElement);
            }
            break;
        case 14:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Border>();
                this->VideoPlayerBorder(targetElement);
            }
            break;
        case 15:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Border>();
                this->PlaceholderBorder(targetElement);
            }
            break;
        case 16:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::ListView>();
                this->QueueListView(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.SelectionChanged([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::Controls::SelectionChangedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->QueueListView_SelectionChanged(p0, p1);
                    }
                });
            }
            break;
        case 18:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Button>();
                this->AddPlaylistButton(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.Click([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::RoutedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->AddPlaylistButton_Click(p0, p1);
                    }
                });
            }
            break;
        case 19:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::Button>();
                this->CreateMixButton(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.Click([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::RoutedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->CreateMixButton_Click(p0, p1);
                    }
                });
            }
            break;
        case 20:
            {
                auto targetElement = target.as<::winrt::Microsoft::UI::Xaml::Controls::TextBox>();
                this->SearchBox(targetElement);
                auto weakThis = ::winrt::make_weak<class_type>(*this);
                targetElement.TextChanged([weakThis](::winrt::Windows::Foundation::IInspectable const& p0, ::winrt::Microsoft::UI::Xaml::Controls::TextChangedEventArgs const& p1){
                    if (auto t = weakThis.get())
                    {
                        ::winrt::get_self<D>(t)->SearchBox_TextChanged(p0, p1);
                    }
                });
            }
            break;
        }
        _contentLoaded = true;
    }

    template <typename D, typename ... I>
    void MainWindowT<D, I...>::DisconnectUnloadedObject(int32_t)
    {
        throw ::winrt::hresult_invalid_argument { L"No unloadable objects to disconnect." };
    }

    template <typename D, typename ... I>
    void MainWindowT<D, I...>::UnloadObject(DependencyObject const&)
    {
        throw ::winrt::hresult_invalid_argument { L"No unloadable objects." };
    }


    template <typename D, typename... I>
    IComponentConnector MainWindowT<D, I...>::GetBindingConnector(int32_t, IInspectable const&)
    {
        return nullptr;
    }

    template struct MainWindowT<struct MainWindow>;
}


#pragma warning(pop)


