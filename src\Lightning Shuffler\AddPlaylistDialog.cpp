#include "pch.h"
#include "AddPlaylistDialog.h"
#if __has_include("AddPlaylistDialog.g.cpp")
#include "AddPlaylistDialog.g.cpp"
#endif

using namespace winrt;
using namespace Microsoft::UI::Xaml;
using namespace Microsoft::UI::Xaml::Controls;

namespace winrt::Lightning_Shuffler::implementation
{
    AddPlaylistDialog::AddPlaylistDialog()
    {
        this->InitializeComponent();
    }

    hstring AddPlaylistDialog::PlaylistUrl()
    {
        return m_playlistUrl;
    }

    bool AddPlaylistDialog::IsValidUrl()
    {
        std::wstring url = m_playlistUrl.c_str();
        
        // Basic YouTube playlist URL validation
        return url.find(L"youtube.com/playlist") != std::wstring::npos ||
               url.find(L"youtu.be/playlist") != std::wstring::npos ||
               url.find(L"list=") != std::wstring::npos;
    }

    void AddPlaylistDialog::PlaylistUrlTextBox_TextChanged(IInspectable const& sender, TextChangedEventArgs const&)
    {
        auto textBox = sender.as<TextBox>();
        m_playlistUrl = textBox.Text();
        ValidateUrl();
    }

    void AddPlaylistDialog::ValidateUrl()
    {
        if (m_playlistUrl.empty())
        {
            HideStatus();
            this->IsPrimaryButtonEnabled(false);
            return;
        }

        if (IsValidUrl())
        {
            ShowStatus(L"Valid YouTube playlist URL", false);
            this->IsPrimaryButtonEnabled(true);
        }
        else
        {
            ShowStatus(L"Please enter a valid YouTube playlist URL", true);
            this->IsPrimaryButtonEnabled(false);
        }
    }

    void AddPlaylistDialog::ShowStatus(hstring const& message, bool isError)
    {
        auto statusBorder = this->FindName(L"StatusBorder").as<Border>();
        auto statusIcon = this->FindName(L"StatusIcon").as<FontIcon>();
        auto statusText = this->FindName(L"StatusText").as<TextBlock>();

        statusText.Text(message);
        
        if (isError)
        {
            statusIcon.Glyph(L"\uE783"); // Error icon
            statusIcon.Foreground(SolidColorBrush(Windows::UI::Color{ 255, 255, 99, 71 })); // Red
            statusText.Foreground(SolidColorBrush(Windows::UI::Color{ 255, 255, 99, 71 }));
        }
        else
        {
            statusIcon.Glyph(L"\uE73E"); // Checkmark icon
            statusIcon.Foreground(Application::Current().Resources().Lookup(winrt::box_value(L"LightningGreenBrush")).as<SolidColorBrush>());
            statusText.Foreground(Application::Current().Resources().Lookup(winrt::box_value(L"LightningGreenBrush")).as<SolidColorBrush>());
        }

        statusBorder.Visibility(Visibility::Visible);
    }

    void AddPlaylistDialog::HideStatus()
    {
        auto statusBorder = this->FindName(L"StatusBorder").as<Border>();
        statusBorder.Visibility(Visibility::Collapsed);
    }
}
