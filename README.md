# Lightning Shuffler

A beautiful, modern playlist shuffler app for Windows 11 built with WinUI 3 and C++/WinRT.

## Features Implemented

### ✅ Core UI Structure
- **Dark Theme with Neon Green Accents**: Modern dark interface with vibrant #00FF41 lightning green highlights
- **Three-Panel Layout**:
  - Sidebar for playlists and queue management
  - Main video player area with placeholder
  - Media controls bar at the bottom
- **Modern Styling**: Rounded corners, gradients, glassmorphism effects, and smooth animations

### ✅ Basic Functionality
- **Playlist Management**: Add playlist button (currently adds sample data)
- **Queue System**: Scrollable video list with thumbnails and metadata
- **Search Functionality**: Real-time filtering of videos by title and author
- **Media Controls**: Play/pause, previous, next, shuffle, and loop buttons
- **Volume Control**: Volume slider and mute button with dynamic icons
- **Loop System**: Advanced loop functionality with counter display

### ✅ Data Architecture
- **VideoItem**: Data structure for individual videos
- **PlaylistItem**: Data structure for playlists and mixes
- **VideoItemViewModel**: MVVM pattern for data binding
- **Proper Data Binding**: ListView with custom templates and search filtering

## Project Structure

```
src/Lightning Shuffler/
├── MainWindow.xaml          # Main UI layout
├── MainWindow.xaml.h        # Main window header
├── MainWindow.xaml.cpp      # Main window implementation
├── MainWindow.idl           # Main window interface definition
├── App.xaml                 # Application resources and themes
├── VideoItemViewModel.*     # Data binding for video items
├── AddPlaylistDialog.*      # Dialog for adding playlists
└── Assets/                  # App icons and images
```

## Current State

The app currently has a fully functional UI with:
- Beautiful dark theme with lightning green accents
- Working media controls (UI only, no actual playback yet)
- Search functionality for filtering videos
- Sample data for testing the interface
- Proper MVVM data binding architecture

## Next Steps

### 🔄 High Priority
1. **YouTube API Integration**
   - Implement actual playlist fetching using YouTube Data API v3
   - API Key: `AIzaSyAFGB8-5IffhA-sAtvt7MYQJLwQJZTPypI`
   - Parse playlist URLs and extract video metadata

2. **Video Playback**
   - Integrate WebView2 for YouTube video playback
   - Implement play/pause/seek functionality
   - Handle video end events for auto-advance

3. **Playlist Dialog Integration**
   - Connect AddPlaylistDialog to the main window
   - Implement URL validation and playlist fetching

### 🔄 Medium Priority
4. **Local Storage**
   - Implement persistent storage for playlists and settings
   - Save/load user's playlist collection
   - Store app preferences and state

5. **Mix Creation**
   - Implement mix creation dialog
   - Allow combining multiple playlists
   - Support comma-separated URL input

6. **Enhanced UI**
   - Add loading animations and progress indicators
   - Implement smooth transitions between states
   - Add playlist thumbnails and metadata display

### 🔄 Future Features
7. **System Integration**
   - System tray functionality
   - Media key support
   - Windows 11 media overlay integration

8. **Keyboard Shortcuts**
   - Spacebar for play/pause
   - Arrow keys for seek
   - M for mute
   - H for help overlay

9. **Advanced Features**
   - Shuffle animations ("deck of cards" effect)
   - Video gradient backgrounds
   - Export/import playlist functionality

## Building the Project

### Option 1: Using VSCode (Recommended)

1. **Prerequisites:**
   - Visual Studio 2022 Community with C++ development tools
   - VSCode with recommended extensions (see `.vscode/extensions.json`)

2. **Setup:**
   ```bash
   # Open the project in VSCode
   code .

   # Install recommended extensions when prompted
   ```

3. **Building:**
   ```bash
   # Using the build script (easiest)
   build.bat debug          # Debug build
   build.bat release        # Release build
   build.bat debug run      # Build and run
   build.bat clean          # Clean build

   # Or use VSCode tasks (Ctrl+Shift+P -> "Tasks: Run Task")
   # - Build (Debug) - Default build task (Ctrl+Shift+B)
   # - Build (Release)
   # - Build and Run (Debug)
   # - Clean Build
   ```

4. **Running:**
   ```bash
   # Run the built executable
   .\src\Lightning Shuffler\x64\Debug\Lightning_Shuffler.exe

   # Or use VSCode debugger (F5)
   ```

### Option 2: Using Visual Studio 2022

1. Open `src/Lightning Shuffler/Lightning Shuffler.sln` in Visual Studio 2022
2. Ensure Windows App SDK and WinUI 3 packages are restored
3. Build and run the project (F5)

## Technologies Used

- **WinUI 3**: Modern Windows UI framework
- **C++/WinRT**: Native Windows Runtime APIs
- **WebView2**: For YouTube video playback
- **YouTube Data API v3**: For playlist and video metadata
- **MVVM Pattern**: For clean data binding and separation of concerns

## Design Philosophy

Lightning Shuffler follows modern Windows 11 design principles with:
- **Fluent Design**: Acrylic materials, depth, and motion
- **Dark-First**: Optimized for dark mode with neon accents
- **Performance**: Native C++ for snappy, responsive experience
- **Accessibility**: Proper contrast ratios and keyboard navigation
- **Consistency**: Unified visual language throughout the app
