<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. See LICENSE in the project root for license information. -->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup Condition="'$(WindowsAppSDKRuntimePatchLevel1)' != '' or '$(WindowsAppSDKRuntimePatchLevel2)' != '' or '$(WindowsAppSDKDisabledChanges)' != ''">
      <WindowsAppSdkCompatibilityInitialize>true</WindowsAppSdkCompatibilityInitialize>
  </PropertyGroup>

  <Target Name="WindowsAppSDKCompatibilitySetterTarget" BeforeTargets="ClCompile"
          Condition="'$(WindowsAppSDKRuntimePatchLevel1)' != '' or '$(WindowsAppSDKRuntimePatchLevel2)' != '' or '$(WindowsAppSDKDisabledChanges)' != ''">
    <PropertyGroup>
      <WindowsAppSDKCompatibilitySetterFile>$(GeneratedFilesDir)WindowsAppSDKCompatibilitySetter.cpp</WindowsAppSDKCompatibilitySetterFile>
      <WindowsAppSDKCompatibilityPatchLevel1Lines Condition="'$(WindowsAppSDKRuntimePatchLevel1)' != ''">
            compatibilityOptions.PatchLevel1({$(WindowsAppSDKRuntimePatchLevel1.Replace(".", ","))})%3B
      </WindowsAppSDKCompatibilityPatchLevel1Lines>
      <WindowsAppSDKCompatibilityPatchLevel2Lines Condition="'$(WindowsAppSDKRuntimePatchLevel2)' != ''">
            compatibilityOptions.PatchLevel2({$(WindowsAppSDKRuntimePatchLevel2.Replace(".", ","))})%3B
      </WindowsAppSDKCompatibilityPatchLevel2Lines>
      <WindowsAppSDKCompatibilityDisabledChangesNoSpaces Condition="'$(WindowsAppSDKDisabledChanges)' != ''">$([System.Text.RegularExpressions.Regex]::Replace($(WindowsAppSDKDisabledChanges), "\s+", ""))</WindowsAppSDKCompatibilityDisabledChangesNoSpaces>
      <WindowsAppSDKCompatibilityDisabledChangesLines Condition="'$(WindowsAppSDKCompatibilityDisabledChangesNoSpaces)' != ''">
            RuntimeCompatibilityChange disabledChangesArray[] = { RuntimeCompatibilityChange::$([System.Text.RegularExpressions.Regex]::Replace($(WindowsAppSDKCompatibilityDisabledChangesNoSpaces), ",([A-Za-z])", ", RuntimeCompatibilityChange::$1")) }%3B
            for (auto changeId : disabledChangesArray)
            {
                compatibilityOptions.DisabledChanges().Append(changeId)%3B
            }
      </WindowsAppSDKCompatibilityDisabledChangesLines>
      
      <WindowsAppSDKCompatibilitySetterFileLines>
// This file is generated by the build based on project properties.
#include &lt;winrt/Windows.Foundation.h&gt;
#include &lt;winrt/Windows.Foundation.Collections.h&gt;

#include &lt;winrt/Microsoft.Windows.ApplicationModel.WindowsAppRuntime.h&gt;

using namespace winrt::Microsoft::Windows::ApplicationModel::WindowsAppRuntime%3B

namespace Microsoft::Windows::ApplicationModel::WindowsAppRuntime::Compatibility
{
    namespace AutoInitialize
    {
        // Called by WindowsAppRuntimeAutoInitializer.cpp
        void Initialize()
        {
            RuntimeCompatibilityOptions compatibilityOptions%3B
$(WindowsAppSDKCompatibilityPatchLevel1Lines)
$(WindowsAppSDKCompatibilityPatchLevel2Lines)
$(WindowsAppSDKCompatibilityDisabledChangesLines)
            compatibilityOptions.Apply()%3B
        }
    }
}
      </WindowsAppSDKCompatibilitySetterFileLines>
    </PropertyGroup>

    <WriteLinesToFile
        File="$(WindowsAppSDKCompatibilitySetterFile)" Lines="$(WindowsAppSDKCompatibilitySetterFileLines)"
        Overwrite="true"
        WriteOnlyWhenDifferent="true" />

    <ItemGroup>
      <ClCompile Include="$(WindowsAppSDKCompatibilitySetterFile)">
        <PrecompiledHeader>NotUsing</PrecompiledHeader>
      </ClCompile>
    </ItemGroup>
  </Target>

  <PropertyGroup>
    <BeforeClCompileTargets>
        $(BeforeClCompileTargets); WindowsAppSDKCompatibilitySetterTarget;
    </BeforeClCompileTargets>
  </PropertyGroup>

</Project>
