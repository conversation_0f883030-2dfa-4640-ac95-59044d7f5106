<doc>
  <assembly>
    <name>Microsoft.UI.Text</name>
  </assembly>
  <members>
    <member name="T:Microsoft.UI.Text.CaretType">
      <summary>Specifies the caret type.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.CaretType.Normal">
      <summary>The insertion point for a sequenced language (characters that are typed with one key stroke).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.CaretType.Null">
      <summary>The insertion point is null.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.FindOptions">
      <summary>Specifies the options to use when doing a text search.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.FindOptions.Case">
      <summary>Match case; that is, a case-sensitive search.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.FindOptions.None">
      <summary>Use the default text search options; namely, use case- independent, arbitrary character boundaries.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.FindOptions.Word">
      <summary>Match whole words.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.FontWeights">
      <summary>Provides a set of predefined font weights as static property values.</summary>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.Black">
      <summary>Specifies a font weight value of 900.</summary>
      <returns>A FontWeight with a Weight value of 900.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.Bold">
      <summary>Specifies a font weight value of 700.</summary>
      <returns>A FontWeight with a Weight value of 700.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.ExtraBlack">
      <summary>Specifies a font weight value of 950.</summary>
      <returns>A FontWeight with a Weight value of 950.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.ExtraBold">
      <summary>Specifies a font weight value of 800.</summary>
      <returns>A FontWeight with a Weight value of 800.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.ExtraLight">
      <summary>Specifies a font weight value of 200.</summary>
      <returns>A FontWeight with a Weight value of 200.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.Light">
      <summary>Specifies a font weight value of 300.</summary>
      <returns>A FontWeight with a Weight value of 300.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.Medium">
      <summary>Specifies a font weight value of 500.</summary>
      <returns>A FontWeight with a Weight value of 500.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.Normal">
      <summary>Specifies a font weight value of 400.</summary>
      <returns>A FontWeight with a Weight value of 400.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.SemiBold">
      <summary>Specifies a font weight value of 600.</summary>
      <returns>A FontWeight with a Weight value of 600.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.SemiLight">
      <summary>Specifies a font weight value of 350.</summary>
      <returns>A FontWeight with a Weight value of 350.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.FontWeights.Thin">
      <summary>Specifies a font weight value of 100.</summary>
      <returns>A FontWeight with a Weight value of 100.</returns>
    </member>
    <member name="T:Microsoft.UI.Text.FormatEffect">
      <summary>Defines values that indicate the state of a character or paragraph formatting property.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.FormatEffect.Off">
      <summary>Turns off the property.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.FormatEffect.On">
      <summary>Turns on the property.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.FormatEffect.Toggle">
      <summary>Toggles the current setting.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.FormatEffect.Undefined">
      <summary>No change.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.HorizontalCharacterAlignment">
      <summary>Specifies the horizontal position of a character relative to a bounding rectangle.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.HorizontalCharacterAlignment.Center">
      <summary>The character is at the center of the bounding rectangle.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.HorizontalCharacterAlignment.Left">
      <summary>The character is at the left edge of the bounding rectangle.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.HorizontalCharacterAlignment.Right">
      <summary>The character is at the right edge of the bounding rectangle.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.ITextCharacterFormat">
      <summary>Defines the default character formatting attributes of a document, or the current character formatting attributes of a text range.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.ITextCharacterFormat.GetClone">
      <summary>Creates a new object that is identical to this character format object.</summary>
      <returns>The duplicate character format object.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextCharacterFormat.IsEqual(Microsoft.UI.Text.ITextCharacterFormat)">
      <summary>Determines whether this character format object has the same properties as the specified character format object.</summary>
      <param name="format">The character format object to compare against.</param>
      <returns>True if the objects have the same properties, or false if they don't.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextCharacterFormat.SetClone(Microsoft.UI.Text.ITextCharacterFormat)">
      <summary>Sets the character formatting by copying another text character formatting object.</summary>
      <param name="value">The character formatting to apply.</param>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.AllCaps">
      <summary>Gets or sets whether the characters are all uppercase.</summary>
      <returns>The uppercase state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.BackgroundColor">
      <summary>Gets or sets the text background (highlight) color.</summary>
      <returns>The text background color.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Bold">
      <summary>Gets or sets whether the characters are bold.</summary>
      <returns>The bold state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.FontStretch">
      <summary>Gets or sets the degree to which the font is stretched, compared to the normal aspect ratio of the font.</summary>
      <returns>The degree to which the font is stretched.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.FontStyle">
      <summary>Gets or sets the style of the font face, such as normal or italic.</summary>
      <returns>The font style.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.ForegroundColor">
      <summary>Gets or sets the foreground, or text, color.</summary>
      <returns>The foreground color.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Hidden">
      <summary>Gets or sets whether characters are hidden.</summary>
      <returns>The hidden state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Italic">
      <summary>Gets or sets whether characters are in italics.</summary>
      <returns>The italicized state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Kerning">
      <summary>Gets or sets the minimum font size at which kerning occurs.</summary>
      <returns>The kerning size, in floating-point points.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.LanguageTag">
      <summary>Gets or sets the Internet Engineering Task Force (IETF) language tag (BCP 47 standard) that identifies the language currently associated with the characters.</summary>
      <returns>The language tag.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.LinkType">
      <summary>Gets the link type of the text.</summary>
      <returns>The link type.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Name">
      <summary>Gets or sets the font name.</summary>
      <returns>The font name.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Outline">
      <summary>Gets or sets whether characters are displayed as outlined characters.</summary>
      <returns>The outlined state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Position">
      <summary>Gets or sets the character offset relative to the baseline.</summary>
      <returns>The character offset, in floating-point points.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.ProtectedText">
      <summary>Gets or sets whether the characters are protected against attempts to modify them.</summary>
      <returns>The protected state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Size">
      <summary>Gets or sets the font size.</summary>
      <returns>The font size, in floating-point points.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.SmallCaps">
      <summary>Gets or sets whether characters are in small capital letters.</summary>
      <returns>The small capitals state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Spacing">
      <summary>Gets or sets the amount of horizontal spacing between characters.</summary>
      <returns>The amount of horizontal spacing, in floating-point points.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Strikethrough">
      <summary>Gets or sets whether characters are displayed with a horizontal line through the center.</summary>
      <returns>The strikethrough state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Subscript">
      <summary>Gets or sets whether characters are displayed as subscript.</summary>
      <returns>The subscript state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Superscript">
      <summary>Gets or sets whether characters are displayed as superscript.</summary>
      <returns>The superscript state.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.TextScript">
      <summary>Gets or sets the character repertoire.</summary>
      <returns>The character repertoire.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Underline">
      <summary>Gets or sets the type of underlining that the characters use.</summary>
      <returns>The type of underlining.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextCharacterFormat.Weight">
      <summary>Gets or sets the font weight of the characters.</summary>
      <returns>The font weight expressed as a numeric value. See Remarks.</returns>
    </member>
    <member name="T:Microsoft.UI.Text.ITextParagraphFormat">
      <summary>Defines the default paragraph formatting attributes of a document, or the current paragraph formatting attributes of a text range.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.ITextParagraphFormat.AddTab(System.Single,Microsoft.UI.Text.TabAlignment,Microsoft.UI.Text.TabLeader)">
      <summary>Adds a new tab at the specified position.</summary>
      <param name="position">The position of the new tab, in floating-point points relative to the left side of the page for left-to-right paragraphs, or the right side of the page for right-to-left paragraphs. A maximum of 63 tabs are allowed. Tabs beyond the page are ignored. Negative tabs are not valid.</param>
      <param name="align">The alignment option for the tab position.</param>
      <param name="leader">The character used to fill the space taken by a tab character.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextParagraphFormat.ClearAllTabs">
      <summary>Clears all tabs, reverting to equally spaced tabs with the default tab spacing.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.ITextParagraphFormat.DeleteTab(System.Single)">
      <summary>Deletes the tab at the specified position.</summary>
      <param name="position">The position of the tab to delete, in floating-point points.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextParagraphFormat.GetClone">
      <summary>Creates a new object that is identical to this paragraph format object.</summary>
      <returns>The duplicate paragraph format object.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextParagraphFormat.GetTab(System.Int32,System.Single@,Microsoft.UI.Text.TabAlignment@,Microsoft.UI.Text.TabLeader@)">
      <summary>Retrieves information about the specified tab.</summary>
      <param name="index">The zero-based index of the tab to retrieve.</param>
      <param name="position">The tab's position, in floating-point points. This parameter is zero if the tab does not exist.</param>
      <param name="align">The alignment option for the tab position.</param>
      <param name="leader">The character used to fill the space taken by a tab character.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextParagraphFormat.IsEqual(Microsoft.UI.Text.ITextParagraphFormat)">
      <summary>Determines whether this paragraph format object has the same properties as the specified paragraph format object.</summary>
      <param name="format">The paragraph format object to compare against.</param>
      <returns>True if the objects have the same properties, or false if they don't.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextParagraphFormat.SetClone(Microsoft.UI.Text.ITextParagraphFormat)">
      <summary>Sets the paragraph formatting by copying another paragraph formatting object.</summary>
      <param name="format">The paragraph formatting to apply.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextParagraphFormat.SetIndents(System.Single,System.Single,System.Single)">
      <summary>Sets the first-line indent, the left indent, and the right indent for a paragraph.</summary>
      <param name="start">The indent of the first line in a paragraph, relative to the left indent. The value is in floating-point points and can be positive or negative.</param>
      <param name="left">The left indent of all lines except the first line in a paragraph, relative to the left margin. The value is in floating-point points and can be positive or negative.</param>
      <param name="right">The right indent of all lines in a paragraph, relative to the right margin. The value is in floating-point points and can be positive or negative. This value is optional.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextParagraphFormat.SetLineSpacing(Microsoft.UI.Text.LineSpacingRule,System.Single)">
      <summary>Sets the paragraph line-spacing rule and the amount of line spacing for a paragraph.</summary>
      <param name="rule">The new line-spacing rule.</param>
      <param name="spacing">The new line spacing amount. If the line-spacing rule interprets the spacing value as a linear dimension, spacing is given in floating-point points.</param>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.Alignment">
      <summary>Gets or sets the paragraph alignment.</summary>
      <returns>The paragraph alignment value.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.FirstLineIndent">
      <summary>Gets the amount used to indent the first line of a paragraph relative to the left indent.</summary>
      <returns>The first line indentation amount, in floating-point points.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.KeepTogether">
      <summary>Gets or sets whether page breaks are allowed in paragraphs.</summary>
      <returns>A value that indicates whether page breaks are allowed.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.KeepWithNext">
      <summary>Gets or sets whether page breaks are allowed between paragraphs in a range.</summary>
      <returns>The value that indicates whether page breaks are allowed.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.LeftIndent">
      <summary>Gets the amount used to indent all lines except the first line of a paragraph.</summary>
      <returns>The amount of left indentation, in floating-point points. Indentation is relative to the left margin.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.LineSpacing">
      <summary>Gets the paragraph line-spacing value.</summary>
      <returns>The line spacing value. The meaning depends on the value of the LineSpacingRule property. The line spacing value is in floating-point points except when the line-spacing rule is Multiple or Percent.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.LineSpacingRule">
      <summary>Gets the paragraph line-spacing rule.</summary>
      <returns>The paragraph line-spacing rule.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.ListAlignment">
      <summary>Gets or sets the alignment to use for bulleted and numbered lists.</summary>
      <returns>The alignment for bulleted and numbered lists.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.ListLevelIndex">
      <summary>Gets or sets the list level index used with paragraphs.</summary>
      <returns>The list level index. It can be a value of 0 or higher, as described in the following table. &lt;table&gt;
   &lt;tr&gt;&lt;th&gt;Value&lt;/th&gt;&lt;th&gt;Meaning&lt;/th&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;0&lt;/td&gt;&lt;td&gt;No list.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;1&lt;/td&gt;&lt;td&gt;First-level (outermost) list.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;2&lt;/td&gt;&lt;td&gt;Second-level (nested) list. This is nested under a level 1 list item.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;3&lt;/td&gt;&lt;td&gt;Third-level (nested) list. This is nested under a level 2 list item.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;And so on&lt;/td&gt;&lt;td&gt;Nesting continues similarly.&lt;/td&gt;&lt;/tr&gt;
&lt;/table&gt;

 Up to three levels are common in HTML documents.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.ListStart">
      <summary>Gets or sets the starting value or code of a list numbering sequence.</summary>
      <returns>The starting value or code of a list numbering sequence.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.ListStyle">
      <summary>Gets or sets the style used to mark the item paragraphs in a list.</summary>
      <returns>The style used to mark the item paragraphs.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.ListTab">
      <summary>Gets or sets the list tab setting, which is the distance between the first indent and the start of the text on the first line.</summary>
      <returns>The list tab setting.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.ListType">
      <summary>Gets or sets the kind of characters used to mark the item paragraphs in a list.</summary>
      <returns>The kind of characters used to mark the item paragraphs.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.NoLineNumber">
      <summary>Gets or sets whether paragraph numbering is suppressed.</summary>
      <returns>A value that indicates whether line numbering is suppressed.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.PageBreakBefore">
      <summary>Gets or sets whether there is a page break before a paragraph.</summary>
      <returns>A value that indicates whether there is page break.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.RightIndent">
      <summary>Gets or sets the right margin of a paragraph.</summary>
      <returns>The size of the right margin, in floating-point points.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.RightToLeft">
      <summary>Gets or sets whether the paragraph uses right-to-left formatting.</summary>
      <returns>A value that indicates whether the paragraph uses right-to-left formatting.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.SpaceAfter">
      <summary>Gets or sets the amount of vertical space that follows a paragraph.</summary>
      <returns>The amount of vertical space, in floating-point points.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.SpaceBefore">
      <summary>Gets or sets the amount of vertical space above a paragraph.</summary>
      <returns>The amount of vertical space, in floating-point points.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.Style">
      <summary>Gets or sets the paragraph style.</summary>
      <returns>The paragraph style.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.TabCount">
      <summary>Retrieves the tab count.</summary>
      <returns>The tab count.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextParagraphFormat.WidowControl">
      <summary>Gets or sets whether widow and orphan suppression is on or off.</summary>
      <returns>The state of widow and orphan suppression.</returns>
    </member>
    <member name="T:Microsoft.UI.Text.ITextRange">
      <summary>Represents a span of continuous text in a document, and provides powerful editing and data-binding properties and methods that allow an app to select, examine, and change document text.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.CanPaste(System.Int32)">
      <summary>Determines whether the Clipboard contains content that can be pasted, using a specified format, into the current text range.</summary>
      <param name="format">The clipboard format. Zero represents the best format, which usually is Rich Text Format (RTF), but CF_UNICODETEXT and other formats are also possible. The default value is zero.</param>
      <returns>True if the Clipboard content can be pasted into the text range in the specified format, and otherwise false.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.ChangeCase(Microsoft.UI.Text.LetterCase)">
      <summary>Changes the case of letters in a text range.</summary>
      <param name="value">The new case of letters in the text range. The default value is Lower.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.Collapse(System.Boolean)">
      <summary>Collapses the text range into a degenerate point at either the beginning or end of the range.</summary>
      <param name="value">True collapses at the start of the text range, and false collapses at the end of the range. The default value is true.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.Copy">
      <summary>Copies the text of the text range to the Clipboard.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.Cut">
      <summary>Moves the text of the text range to the Clipboard.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.Delete(Microsoft.UI.Text.TextRangeUnit,System.Int32)">
      <summary>Deletes text from the text range.</summary>
      <param name="unit">The unit of text to delete.</param>
      <param name="count">The number of units to delete. See Remarks.</param>
      <returns>The number of units deleted. Deleting the text in a nondegenerate text range counts as one unit.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.EndOf(Microsoft.UI.Text.TextRangeUnit,System.Boolean)">
      <summary>Moves or extends the text range to the end of the nearest specified text unit. The text range is moved or extended forward in the document.</summary>
      <param name="unit">The unit by which to move the end position of the text range.</param>
      <param name="extend">True extends the text range by moving just the end position of the range to the end of the specified unit. False moves both ends of the text range to the end of the specified unit. The default value is false.</param>
      <returns>The number of character positions that the range was moved or extended, plus 1 if the text range collapsed to the start of the range. If the text range includes the final carriage return (CR) at the end of the story, and extend is false, the return value is set to –1 to indicate that the collapse occurred before the end of the range. This is because an insertion point cannot exist beyond the final CR.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.Expand(Microsoft.UI.Text.TextRangeUnit)">
      <summary>Expands a text range to completely contain any partial text units.</summary>
      <param name="unit">The text unit to use to expand the range. The default value is Word.</param>
      <returns>The number of characters added to the text range, if the range was expanded to include a partially contained unit.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.FindText(System.String,System.Int32,Microsoft.UI.Text.FindOptions)">
      <summary>Searches for a particular text string in a range and, if found, selects the string.</summary>
      <param name="value">The text string to search for.</param>
      <param name="scanLength">The maximum number of characters to search. It can be one of the following.</param>
      <param name="options">The options to use when doing the text search.</param>
      <returns>The length of the matching text string, or zero if no matching string is found.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.GetCharacterUtf32(System.UInt32@,System.Int32)">
      <summary>Retrieves the Unicode Transformation Format (UTF)-32 character code of the character at the specified offset from the end of the text range.</summary>
      <param name="value">The character value.</param>
      <param name="offset">The offset from the end of the text range.

| If offset is | The method returns this character |
|---|---|
| 0 | The character at the end of the range |
| in the middle of a surrogate pair | The corresponding UTF-32 character |</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.GetClone">
      <summary>Creates a new object that is identical to this text range object.</summary>
      <returns>The duplicate text range object.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.GetIndex(Microsoft.UI.Text.TextRangeUnit)">
      <summary>Retrieves the story index of the text unit (word, line, sentence, paragraph, and so on) at the starting character position of the text range.</summary>
      <param name="unit">The text unit that is indexed.</param>
      <returns>The index value. The value is zero if unit does not exist.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.GetPoint(Microsoft.UI.Text.HorizontalCharacterAlignment,Microsoft.UI.Text.VerticalCharacterAlignment,Microsoft.UI.Text.PointOptions,Windows.Foundation.Point@)">
      <summary>Retrieves the screen coordinates of a particular location in the text range.</summary>
      <param name="horizontalAlign">The horizontal position to retrieve, relative to the bounding rectangle of the text range.</param>
      <param name="verticalAlign">The vertical position to retrieve, relative to the bounding rectangle of the text range.</param>
      <param name="options">The options for retrieving the coordinates of the specified location in the text range.</param>
      <param name="point">A structure that receives the coordinates of the specified location in the text range, represented as an ordered pair of floating-point x- and y-coordinates that define a point in a two-dimensional plane.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.GetRect(Microsoft.UI.Text.PointOptions,Windows.Foundation.Rect@,System.Int32@)">
      <summary>Retrieves the bounding rectangle that encompasses the text range on the screen.</summary>
      <param name="options">A value that indicates the rectangle to retrieve.</param>
      <param name="rect">A structure that contains four floating-point numbers that represent the location and size of the bounding rectangle.</param>
      <param name="hit">The hit-test value for the text range.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.GetText(Microsoft.UI.Text.TextGetOptions,System.String@)">
      <summary>Retrieves the text in a text range according to the specified conversion flags.</summary>
      <param name="options">The conversion flags that control how the text is retrieved.</param>
      <param name="value">The text in the text range.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.GetTextViaStream(Microsoft.UI.Text.TextGetOptions,Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Retrieves the text in the text range according to the specified conversion flags, as a random access stream.</summary>
      <param name="options">The conversion flags that control how the text is retrieved. A value of default retrieves the plain text in the text range.</param>
      <param name="value">The text stream.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.InRange(Microsoft.UI.Text.ITextRange)">
      <summary>Determines whether this range is in or at the same text as a specified range.</summary>
      <param name="range">Text that is compared to the current range.</param>
      <returns>The comparison result. The result can be null. The method returns True if the range is in or at the same text as ITextRange; otherwise it returns False.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.InsertImage(System.Int32,System.Int32,System.Int32,Microsoft.UI.Text.VerticalCharacterAlignment,System.String,Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Inserts an image into this range.</summary>
      <param name="width">The width of the image, in Device-independent pixels (DIPs).</param>
      <param name="height">The height of the image, in DIPs.</param>
      <param name="ascent">If verticalAlign is Baseline, this parameter is the distance, in DIPs, that the top of the image extends above the text baseline. If verticalAlign is Baseline and ascent is zero, the bottom of the image is placed at the text baseline.</param>
      <param name="verticalAlign">The vertical alignment of the image.</param>
      <param name="alternateText">The alternate text for the image.</param>
      <param name="value">The stream that contains the image data.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.InStory(Microsoft.UI.Text.ITextRange)">
      <summary>Determines whether this range's story is the same as a specified range's story.</summary>
      <param name="range">The ITextRange object whose story is compared to this range's story.</param>
      <returns>The comparison result. The result can be null. The method returns True if this range's story is the same as that of the ITextRange; otherwise it returns False.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.IsEqual(Microsoft.UI.Text.ITextRange)">
      <summary>Determines whether this range has the same character positions and story as those of a specified range.</summary>
      <param name="range">The text range to compare to this text range.</param>
      <returns>True if this text range has the same character positions and story as range, and otherwise false.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.MatchSelection">
      <summary>Sets the start and end positions of this range to match the active selection.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.Move(Microsoft.UI.Text.TextRangeUnit,System.Int32)">
      <summary>Moves the insertion point forward or backward by the specified number of units. If the text range is nondegenerate, it is collapsed to an insertion point at the start or end position of the text range, depending on count, and then is moved.</summary>
      <param name="unit">The units to move the insertion point. The default value is Character.</param>
      <param name="count">The number of units to move the insertion point. The default value is 1. If count is greater than zero, the insertion point moves forward, toward the end of the story. If count is less than zero, the insertion point moves backward, toward the beginning of the story. If count is zero, the range is unchanged.</param>
      <returns>The actual number of units the insertion point moves. For more information, see the Remarks section.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.MoveEnd(Microsoft.UI.Text.TextRangeUnit,System.Int32)">
      <summary>Moves the end position of the text range.</summary>
      <param name="unit">The unit by which to move the end position of the text range. The default value is Character.</param>
      <param name="count">The number of units to move the end position of the text range. The default value is 1. If count is greater than zero, the end position moves forward, toward the end of the story. If count is less than zero, the end position move backward, toward the beginning of the story. If count is zero, the end position does not move.</param>
      <returns>The actual number of units that the end position of the text range moved.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.MoveStart(Microsoft.UI.Text.TextRangeUnit,System.Int32)">
      <summary>Moves the start position of a text range.</summary>
      <param name="unit">The unit by which to move the start position of the text range. The default value is Character.</param>
      <param name="count">The number of units to move the start position of the text range. The default value is 1. If count is greater than zero, the start position of the text range moves forward, toward the end of the story. If count is less than zero, the start position of the text range moves backward, toward the beginning of the story. If count is zero, the start position doesn't move.</param>
      <returns>The actual number of units that the start position moved. The pointer can be NULL.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.Paste(System.Int32)">
      <summary>Pastes text from the Clipboard into the text range.</summary>
      <param name="format">The clipboard format to use in the paste operation. Zero represents the best format, which usually is Rich Text Format (RTF), but CF_UNICODETEXT and other formats are also possible. The default value is zero.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.ScrollIntoView(Microsoft.UI.Text.PointOptions)">
      <summary>Scrolls this text range into view.</summary>
      <param name="value">The end of the text range to scroll into view. This function uses only the Start, NoHorizontalScroll, and NoVerticalScroll values of the PointOptions enumeration.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.SetIndex(Microsoft.UI.Text.TextRangeUnit,System.Int32,System.Boolean)">
      <summary>Moves the text range to the specified unit of the story.</summary>
      <param name="unit">The unit used to move the text range.</param>
      <param name="index">The index of the specified unit. The text range is relocated to the unit that has this index. If unit is positive, the numbering of units begins at the start of the story and proceeds forward. If negative, the numbering begins at the end of the story and proceeds backward. The start of the story corresponds to index = 1 for all existing units, and the last unit in the story corresponds to index = – 1.</param>
      <param name="extend">Indicates how to change the text range. True extends the text range to include the unit by moving only the end position of the text range. False collapses the text range to an insertion point and then moves the insertion point. The default value is false.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.SetPoint(Windows.Foundation.Point,Microsoft.UI.Text.PointOptions,System.Boolean)">
      <summary>Changes the text range based on the specified point.</summary>
      <param name="point">An ordered pair of floating-point x- and y-coordinates that defines a point in a two-dimensional plane.</param>
      <param name="options">The alignment type of the specified point.</param>
      <param name="extend">Indicates how to set the endpoints of the text range. If extend is 0, the text range is an insertion point located at the specified point, or at the nearest point with selectable text. If extend is 1, the endpoint specified by options is moved to the point and the other endpoint is left alone. The default value is 0.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.SetRange(System.Int32,System.Int32)">
      <summary>Sets the endpoints of the text range to the specified values.</summary>
      <param name="startPosition">The character position for the start of the text range. This parameter must be less than endPosition.</param>
      <param name="endPosition">The character position for the end of the text range.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.SetText(Microsoft.UI.Text.TextSetOptions,System.String)">
      <summary>Replaces the text in the text range.</summary>
      <param name="options">The conversion flags that control how the text is inserted in the text range.</param>
      <param name="value">The new text.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.SetTextViaStream(Microsoft.UI.Text.TextSetOptions,Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Sets the text in the text range based on the contents of a random access stream.</summary>
      <param name="options">The text options.</param>
      <param name="value">The random access stream.</param>
    </member>
    <member name="M:Microsoft.UI.Text.ITextRange.StartOf(Microsoft.UI.Text.TextRangeUnit,System.Boolean)">
      <summary>Moves or extends the text range to the start of the nearest specified text unit. The text range is moved or extended backward in the document.</summary>
      <param name="unit">The unit by which to move the start position of the text range. The default value is Word.</param>
      <param name="extend">True extends the text range by moving just the start position of the range to the start of the specified unit. False moves both ends of the text range to the start of the specified unit. The default value is false.</param>
      <returns>The number of characters the insertion point or start position is moved. Note that this value is always less than or equal to zero, since the motion is always toward the beginning of the story.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.Character">
      <summary>Gets or sets the first character of the text range; that is, the character associated with the StartPosition property.</summary>
      <returns>The value of the first character in the text range.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.CharacterFormat">
      <summary>Gets or sets the character formatting attributes of the text range.</summary>
      <returns>The character formatting attributes.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.EndPosition">
      <summary>Gets or sets the end character position of the text range.</summary>
      <returns>The end character position.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.FormattedText">
      <summary>Gets or sets an ITextRange object with the formatted text of the specified range.</summary>
      <returns>The formatted text.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.Gravity">
      <summary>Gets or sets the gravity of the text range.</summary>
      <returns>The gravity of the text range.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.Length">
      <summary>Gets the count of characters in the text range.</summary>
      <returns>The count of characters.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.Link">
      <summary>Gets or sets the URL text associated with a text range.</summary>
      <returns>The URL as text.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.ParagraphFormat">
      <summary>Gets or sets the paragraph formatting attributes of the text range.</summary>
      <returns>The paragraph formatting attributes.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.StartPosition">
      <summary>Gets or sets the start position of the text range.</summary>
      <returns>The character position to set as the start position of the text range.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.StoryLength">
      <summary>Gets the count of characters in the story of the text range.</summary>
      <returns>The count of characters in the story.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextRange.Text">
      <summary>Gets or sets the plain text of the text range.</summary>
      <returns>The plain text.</returns>
    </member>
    <member name="T:Microsoft.UI.Text.ITextSelection">
      <summary>Represents the currently selected text of a document.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.ITextSelection.EndKey(Microsoft.UI.Text.TextRangeUnit,System.Boolean)">
      <summary>Moves the insertion point or the active end of the text selection to the end of the specified unit, mimicking the functionality of the End key.</summary>
      <param name="unit">The units by which to move the insertion point or active end. The following values are valid.</param>
      <param name="extend">Indicates how to change the selection. True extends the selection by moving only the active end. False collapses the selection to an insertion point and then moves the insertion point. The default value is false.</param>
      <returns>The number of units that the insertion point or the active end is moved.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextSelection.HomeKey(Microsoft.UI.Text.TextRangeUnit,System.Boolean)">
      <summary>Moves the insertion point or the active end of the text selection to the home position, mimicking the functionality of the Home key.</summary>
      <param name="unit">The units by which to move the insertion point or active end. The following values are valid.</param>
      <param name="extend">Indicates how to change the selection. True extends the selection by moving only the active end. False collapses the selection to an insertion point and then moves the insertion point. The default value is false.</param>
      <returns>The number of units that the insertion point or the active end is moved.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextSelection.MoveDown(Microsoft.UI.Text.TextRangeUnit,System.Int32,System.Boolean)">
      <summary>Moves the insertion point or the active end of the text selection down, mimicking the functionality of the Down Arrow or Page Down key.</summary>
      <param name="unit">The units by which to move the insertion point or active end. The following values are valid.&lt;table&gt;
   &lt;tr&gt;&lt;th&gt;Value&lt;/th&gt;&lt;th&gt;Corresponding key combination&lt;/th&gt;&lt;th&gt;Meaning&lt;/th&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Line&lt;/td&gt;&lt;td&gt;Down Arrow&lt;/td&gt;&lt;td&gt;Moves down one line. This is the default.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Paragraph&lt;/td&gt;&lt;td&gt;Ctrl+Down Arrow&lt;/td&gt;&lt;td&gt;Moves down one paragraph.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Screen&lt;/td&gt;&lt;td&gt;Page Down&lt;/td&gt;&lt;td&gt;Moves down one screen.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Window&lt;/td&gt;&lt;td&gt;Ctrl+Page Down&lt;/td&gt;&lt;td&gt;Moves to the last character in the window.&lt;/td&gt;&lt;/tr&gt;
&lt;/table&gt;</param>
      <param name="count">The number of units to move. The default value is 1.</param>
      <param name="extend">Indicates how to change the selection. True extends the selection by moving only the active end. False collapses the selection to an insertion point and then moves the insertion point. The default value is false.</param>
      <returns>The number of units that the insertion point or active end moved down. Collapsing the selection counts as one unit.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextSelection.MoveLeft(Microsoft.UI.Text.TextRangeUnit,System.Int32,System.Boolean)">
      <summary>Moves the insertion point or the active end of the text selection to the left, mimicking the functionality of the Left Arrow key.</summary>
      <param name="unit">The units by which to move the insertion point or active end. The following values are valid. &lt;table&gt;
   &lt;tr&gt;&lt;th&gt;Value&lt;/th&gt;&lt;th&gt;Corresponding key combination&lt;/th&gt;&lt;th&gt;Meaning&lt;/th&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Character&lt;/td&gt;&lt;td&gt;Left Arrow&lt;/td&gt;&lt;td&gt;Move one character position to the left. This is the default.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Word&lt;/td&gt;&lt;td&gt;Ctrl+Left Arrow&lt;/td&gt;&lt;td&gt;Move one word to the left.&lt;/td&gt;&lt;/tr&gt;
&lt;/table&gt;</param>
      <param name="count">The number of units to move. The default value is 1. If count is less than zero, movement is to the right.</param>
      <param name="extend">Indicates how to change the selection. True extends the selection by moving only the active end. False collapses the selection to an insertion point and then moves the insertion point. The default value is false.</param>
      <returns>The number of units that the insertion point or active end moved. Collapsing the selection counts as one unit.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextSelection.MoveRight(Microsoft.UI.Text.TextRangeUnit,System.Int32,System.Boolean)">
      <summary>Moves the insertion point or the active end of the text selection to the right, mimicking the functionality of the Right Arrow key.</summary>
      <param name="unit">The units by which to move the insertion point or active end. The following values are valid. &lt;table&gt;
   &lt;tr&gt;&lt;th&gt;Value&lt;/th&gt;&lt;th&gt;Corresponding key combination&lt;/th&gt;&lt;th&gt;Meaning&lt;/th&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Character&lt;/td&gt;&lt;td&gt;Right Arrow&lt;/td&gt;&lt;td&gt;Move one character position to the right. This is the default.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Word&lt;/td&gt;&lt;td&gt;Ctrl+Right Arrow&lt;/td&gt;&lt;td&gt;Move one word to the right.&lt;/td&gt;&lt;/tr&gt;
&lt;/table&gt;</param>
      <param name="count">The number of units to move. The default value is 1. If count is less than zero, movement is to the left.</param>
      <param name="extend">Indicates how to change the selection. True extends the selection by moving only the active end. False collapses the selection to an insertion point and then moves the insertion point. The default value is false.</param>
      <returns>The number of units that the insertion point or active end moved. Collapsing the selection counts as one unit.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextSelection.MoveUp(Microsoft.UI.Text.TextRangeUnit,System.Int32,System.Boolean)">
      <summary>Moves the insertion point or the active end of the text selection up, mimicking the functionality of the Up Arrow or Page Up keys.</summary>
      <param name="unit">The units by which to move the insertion point or active end. The following values are valid.&lt;table&gt;
   &lt;tr&gt;&lt;th&gt;Value&lt;/th&gt;&lt;th&gt;Corresponding key combination&lt;/th&gt;&lt;th&gt;Meaning&lt;/th&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Line&lt;/td&gt;&lt;td&gt;Up Arrow&lt;/td&gt;&lt;td&gt;Moves up one line. This is the default.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Paragraph&lt;/td&gt;&lt;td&gt;Ctrl+Up Arrow&lt;/td&gt;&lt;td&gt;Moves up one paragraph.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Screen&lt;/td&gt;&lt;td&gt;Page Up&lt;/td&gt;&lt;td&gt;Moves up one screen.&lt;/td&gt;&lt;/tr&gt;
   &lt;tr&gt;&lt;td&gt;Window&lt;/td&gt;&lt;td&gt;Ctrl+Page Up&lt;/td&gt;&lt;td&gt;Moves to the first character in the window.&lt;/td&gt;&lt;/tr&gt;
&lt;/table&gt;</param>
      <param name="count">The number of units to move. The default value is 1.</param>
      <param name="extend">Indicates how to change the selection. True extends the selection by moving only the active end. False collapses the selection to an insertion point and then moves the insertion point. The default value is false.</param>
      <returns>The number of units the insertion point or active end is moved down. Collapsing the selection counts as one unit.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.ITextSelection.TypeText(System.String)">
      <summary>Enters text into the selection as if someone typed it.</summary>
      <param name="value">The text string to type into this selection.</param>
    </member>
    <member name="P:Microsoft.UI.Text.ITextSelection.Options">
      <summary>Gets and sets text selection options.</summary>
      <returns>The text selection options. Each option is binary, so if a particular option is not set, the text selection has the opposite option. For example, if the Overtype option is not set, the text selection is set to insert mode.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.ITextSelection.Type">
      <summary>Retrieves the type of text selection.</summary>
      <returns>The selection type.</returns>
    </member>
    <member name="T:Microsoft.UI.Text.LetterCase">
      <summary>Represents the character case formatting.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LetterCase.Lower">
      <summary>Lowercase characters.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LetterCase.Upper">
      <summary>Uppercase characters.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.LineSpacingRule">
      <summary>Specifies options for line-spacing rules.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LineSpacingRule.AtLeast">
      <summary>The line-spacing value specifies the spacing from one line to the next. However, if the value is less than single spacing, text is single spaced.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LineSpacingRule.Double">
      <summary>Double line spacing. The line-spacing value is ignored.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LineSpacingRule.Exactly">
      <summary>The line-spacing value specifies the exact spacing from one line to the next, even if the value is less than single spacing.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LineSpacingRule.Multiple">
      <summary>The line-spacing value specifies the line spacing, in lines.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LineSpacingRule.OneAndHalf">
      <summary>One-and-a-half line spacing. The line-spacing value is ignored.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LineSpacingRule.Percent">
      <summary>The line-spacing value specifies the line spacing by percent of line height. This option is not supported by Windows.UI.Xaml.Controls.RichEditBox. Setting it will always return an InvalidArgumentException.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LineSpacingRule.Single">
      <summary>Single space. The line-spacing value is ignored.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LineSpacingRule.Undefined">
      <summary>The line spacing is undefined.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.LinkType">
      <summary>Indicates the link type of a range of text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LinkType.AutoLink">
      <summary>A Uniform Resource Identifier (URI) that is automatically recognized.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LinkType.AutoLinkEmail">
      <summary>An email address that is automatically recognized.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LinkType.AutoLinkPath">
      <summary>A file name, including the full path, that is automatically recognized.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LinkType.AutoLinkPhone">
      <summary>A phone number that is automatically recognized.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LinkType.ClientLink">
      <summary>A link specified by the client; that is, not an autolink or a friendly-name link.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LinkType.FriendlyLinkAddress">
      <summary>The address Uniform Resource Identifier (URI) part of friendly-name link. The address it the part that is sent when the user clicks the name.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LinkType.FriendlyLinkName">
      <summary>The name part of a friendly-name link. The name is the part that is displayed.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LinkType.NotALink">
      <summary>Not a link.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.LinkType.Undefined">
      <summary>A mix of link and nonlink attributes.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.MarkerAlignment">
      <summary>Defines bullet and numbering alignment.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerAlignment.Center">
      <summary>Text is centered in the line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerAlignment.Left">
      <summary>Text is left aligned.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerAlignment.Right">
      <summary>Text is right aligned.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerAlignment.Undefined">
      <summary>The value is undefined.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.MarkerStyle">
      <summary>Specifies the style used to mark the item paragraphs in a list.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerStyle.Minus">
      <summary>The item marker is followed by a hyphen (-).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerStyle.NoNumber">
      <summary>The items have no markers.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerStyle.Parentheses">
      <summary>The item marker is enclosed in parentheses, as in (1).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerStyle.Parenthesis">
      <summary>The item marker is followed by a parenthesis, as in 1).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerStyle.Period">
      <summary>The item marker is followed by a period.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerStyle.Plain">
      <summary>The item marker appears by itself.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerStyle.Undefined">
      <summary>The marker style is not defined.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.MarkerType">
      <summary>Specifies the kind of characters used to mark the item paragraphs in a list.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.Arabic">
      <summary>The list is numbered with Arabic numerals (0, 1, 2, ...).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.ArabicAbjad">
      <summary>Arabic abjadi ( أ ,ب ,ج ,د ,…).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.ArabicDictionary">
      <summary>Arabic alphabetic ( أ ,ب ,ت ,ث ,…).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.ArabicWide">
      <summary>Full-width ASCII (０, １, ２, ３, …).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.BlackCircleWingding">
      <summary>The list is ordered with Wingdings black circled digits &lt;img alt="Circled black digits." src="./microsoft.ui.text/images/wingdingblackcircleddigits.png" /&gt;</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.Bullet">
      <summary>The list uses bullets (character code 0x2022).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.CircledNumber">
      <summary>The list is ordered with Unicode circled numbers &lt;img alt="Unicode numbers in a circle." src="./microsoft.ui.text/images/unicodecirclednumbers.png" /&gt;</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.DevanagariConsonant">
      <summary>Devanāgarī consonants (क, ख, ग, घ, …).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.DevanagariNumeric">
      <summary>Devanāgarī numbers (१, २, ३, ४, …).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.DevanagariVowel">
      <summary>Devanāgarī vowels (अ, आ, इ, ई, …).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.Hebrew">
      <summary>Hebrew alphabet (א, ב, ג, ד, …).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.JapanKorea">
      <summary>Chinese with no 十.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.JapanSimplifiedChinese">
      <summary>Chinese with a full-width period, no 十.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.LowercaseEnglishLetter">
      <summary>The list is ordered with lowercase letters (a, b, c, ...).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.LowercaseRoman">
      <summary>The list is ordered with lowercase Roman letters (i, ii, iii, ...).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.None">
      <summary>Not a list paragraph.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.SimplifiedChinese">
      <summary>Chinese with 十 only in items 10 through 99 (一, 二, 三, 四, …).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.ThaiAlphabetic">
      <summary>Thai alphabetic (ก, ข,ค, ง, …).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.ThaiNumeric">
      <summary>Thai numbers (๑, ๒,๓, ๔, …).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.TraditionalChinese">
      <summary>Chinese with 十 only in items 10 through 19.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.Undefined">
      <summary>The list type is not defined.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.UnicodeSequence">
      <summary>The value returned by ITextParagraphFormat.ListStart is treated as the first code in a Unicode sequence.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.UppercaseEnglishLetter">
      <summary>The list is ordered with uppercase letters (A, B, C, ...).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.UppercaseRoman">
      <summary>The list is ordered with uppercase Roman letters (I, II, III, ...).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.MarkerType.WhiteCircleWingding">
      <summary>The list is ordered with Wingdings white circled digits &lt;img alt="White digits in a black circle." src="./microsoft.ui.text/images/wingdingwhitecircleddigits.png" /&gt;</summary>
    </member>
    <member name="T:Microsoft.UI.Text.ParagraphAlignment">
      <summary>Specifies values for aligning paragraphs.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphAlignment.Center">
      <summary>Text is centered between the margins.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphAlignment.Justify">
      <summary>Text is equally distributed between the margins so that each line of the paragraph, other than the last, is identical in length.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphAlignment.Left">
      <summary>Text aligns with the left margin.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphAlignment.Right">
      <summary>Text aligns with the right margin.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphAlignment.Undefined">
      <summary>No paragraph alignment is defined.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.ParagraphStyle">
      <summary>Specifies the paragraph style.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Heading1">
      <summary>The top level heading.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Heading2">
      <summary>The second level heading.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Heading3">
      <summary>Third level heading.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Heading4">
      <summary>Fourth level heading.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Heading5">
      <summary>Fifth level heading.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Heading6">
      <summary>Sixth level heading.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Heading7">
      <summary>Seventh level heading.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Heading8">
      <summary>Eighth level heading.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Heading9">
      <summary>Ninth level heading.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.None">
      <summary>There is no paragraph style.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Normal">
      <summary>The paragraph style is normal.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.ParagraphStyle.Undefined">
      <summary>The paragraph style is undefined.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.PointOptions">
      <summary>Defines options for specifying or retrieving a point.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.PointOptions.AllowOffClient">
      <summary>Allow points outside of the client area.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.PointOptions.ClientCoordinates">
      <summary>Return client coordinates instead of screen coordinates.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.PointOptions.IncludeInset">
      <summary>Add left and top insets to the left and top coordinates of the rectangle, and subtract right and bottom insets from the right and bottom coordinates.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.PointOptions.NoHorizontalScroll">
      <summary>Horizontal scrolling is disabled.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.PointOptions.None">
      <summary>No options.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.PointOptions.NoVerticalScroll">
      <summary>Vertical scrolling is disabled.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.PointOptions.Start">
      <summary>The start position of the text range.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.PointOptions.Transform">
      <summary>Transform coordinates using a world transform supplied by the host app.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.RangeGravity">
      <summary>Specifies the gravity for a text range.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.RangeGravity.Backward">
      <summary>Use the formatting of the previous text run when on a boundary between runs.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.RangeGravity.Forward">
      <summary>Use the formatting of the following text run when on a boundary between runs.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.RangeGravity.Inward">
      <summary>The start of the text range has forward gravity, and the end has backward gravity.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.RangeGravity.Outward">
      <summary>The start of the text range has backward gravity, and the end has forward gravity.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.RangeGravity.UIBehavior">
      <summary>Use selection user interface rules.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.RichEditMathMode">
      <summary>Defines constants that specify whether a RichEditBox interprets input as math (MathML or text.

&gt; [!IMPORTANT]
&gt; The Microsoft.UI.Text.RichEditMathMode API is part of a Limited Access Feature (see LimitedAccessFeatures class.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.RichEditMathMode.MathOnly">
      <summary>Input is interpreted as math.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.RichEditMathMode.NoMath">
      <summary>Input is interpreted as text.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.RichEditTextDocument">
      <summary>Represents a rich text document that can be loaded, saved, and edited.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.ApplyDisplayUpdates">
      <summary>Decrements an internal counter that controls whether text updates are displayed immediately or batched.</summary>
      <returns>The value of the internal counter.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.BatchDisplayUpdates">
      <summary>Increments an internal counter that controls whether text updates are displayed immediately or batched.</summary>
      <returns>The value of the internal counter.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.BeginUndoGroup">
      <summary>Turns on undo grouping.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.CanCopy">
      <summary>Determines whether document content can be copied to the Clipboard.</summary>
      <returns>true if copying to the Clipboard is allowed; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.CanPaste">
      <summary>Determines whether the Clipboard has content that can be pasted into the document.</summary>
      <returns>true if the Clipboard has content that can be pasted into the document; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.CanRedo">
      <summary>Determines whether one or more redo operations exist.</summary>
      <returns>true if one or more redo operations exist; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.CanUndo">
      <summary>Determines whether one or more undo operations exist.</summary>
      <returns>true if one or more undo operations exist; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.ClearUndoRedoHistory">
      <summary>Empties the undo and redo buffers.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.EndUndoGroup">
      <summary>Turns off undo grouping.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.GetDefaultCharacterFormat">
      <summary>Retrieves the default character formatting attributes of the document.</summary>
      <returns>The default character formatting attributes.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.GetDefaultParagraphFormat">
      <summary>Retrieves the default paragraph formatting attributes of the document.</summary>
      <returns>The default paragraph formatting attributes.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.GetRange(System.Int32,System.Int32)">
      <summary>Retrieves a new text range for the active story of the document.</summary>
      <param name="startPosition">The starting position of the new text range, relative to the beginning of the story.</param>
      <param name="endPosition">The ending position of the new text range.</param>
      <returns>The new text range.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.GetRangeFromPoint(Windows.Foundation.Point,Microsoft.UI.Text.PointOptions)">
      <summary>Retrieves the degenerate (empty) text range at, or nearest to, a particular point on the screen.</summary>
      <param name="point">The location of the point on the screen, in screen coordinates.</param>
      <param name="options">The alignment type of the specified point.</param>
      <returns>The new text range.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.GetText(Microsoft.UI.Text.TextGetOptions,System.String@)">
      <summary>Gets the text in the active story (document).</summary>
      <param name="options">The text retrieval options.</param>
      <param name="value">The text in the active story.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.LoadFromStream(Microsoft.UI.Text.TextSetOptions,Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Loads a document from a stream.</summary>
      <param name="options">The text options to use for the loading the document.</param>
      <param name="value">The random access stream that contains the document.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.Redo">
      <summary>Reverses the most recent undo operation.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.SaveToStream(Microsoft.UI.Text.TextGetOptions,Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Saves the document to a stream.</summary>
      <param name="options">The text options for saving the document.</param>
      <param name="value">The random access stream for saving the document.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.SetDefaultCharacterFormat(Microsoft.UI.Text.ITextCharacterFormat)">
      <summary>Sets the default character formatting attributes of the document.</summary>
      <param name="value">The new default character formatting attributes.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.SetDefaultParagraphFormat(Microsoft.UI.Text.ITextParagraphFormat)">
      <summary>Sets the default paragraph formatting attributes of the document.</summary>
      <param name="value">The default paragraph formatting attributes.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.SetText(Microsoft.UI.Text.TextSetOptions,System.String)">
      <summary>Sets the text of the document.</summary>
      <param name="options">Options that control how the document text is set.</param>
      <param name="value">The new text.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextDocument.Undo">
      <summary>Undoes the most recent undo group.</summary>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextDocument.AlignmentIncludesTrailingWhitespace">
      <summary>Gets or sets a value that indicates whether trailing white space is taken into account when text is aligned.</summary>
      <returns>true if trailing whitespace is taken into account when text is aligned; false if trailing whitespace is ignored. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextDocument.CaretType">
      <summary>Gets or sets the caret type.</summary>
      <returns>The caret type.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextDocument.DefaultTabStop">
      <summary>Gets or sets the default tab spacing.</summary>
      <returns>The default tab spacing.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextDocument.IgnoreTrailingCharacterSpacing">
      <summary>Gets or sets a value that indicates whether character spacing is applied to the last character in a line.</summary>
      <returns>true if spacing is applied to the last character in a line of text; otherwise, false. The default is false.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextDocument.Selection">
      <summary>Gets the active text selection.</summary>
      <returns>The active text selection.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextDocument.UndoLimit">
      <summary>Gets or sets the maximum number of actions that can be stored in the undo queue.</summary>
      <returns>The maximum number of undo actions.</returns>
    </member>
    <member name="T:Microsoft.UI.Text.RichEditTextRange">
      <summary>Represents a span of continuous text in a RichEditTextDocument, and provides editing and data-binding properties and methods that allow an app to select, examine, and change document content.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.CanPaste(System.Int32)">
      <summary>Determines whether the Clipboard contains content that can be pasted, using a specified format, into the current text range.</summary>
      <param name="format">The clipboard format. Zero represents the best format, which usually is Rich Text Format (RTF), but CF_UNICODETEXT and other formats are also possible. The default value is zero.</param>
      <returns>true if the Clipboard content can be pasted into the text range in the specified format; otherwise, false.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.ChangeCase(Microsoft.UI.Text.LetterCase)">
      <summary>Changes the case of letters in a text range.</summary>
      <param name="value">The new case of letters in the text range. The default value is Lower.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.Collapse(System.Boolean)">
      <summary>Collapses the text range into a degenerate point at either the beginning or end of the range.</summary>
      <param name="value">true to collapse at the start of the text range. false to collapse at the end of the range. The default is true.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.Copy">
      <summary>Copies the text of the text range to the Clipboard.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.Cut">
      <summary>Moves the text of the text range to the Clipboard.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.Delete(Microsoft.UI.Text.TextRangeUnit,System.Int32)">
      <summary>Deletes text from the text range.</summary>
      <param name="unit">The unit of text to delete.</param>
      <param name="count">The number of _unit_s to delete.</param>
      <returns>The number of _unit_s deleted. Deleting the text in a nondegenerate text range counts as one _unit_.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.EndOf(Microsoft.UI.Text.TextRangeUnit,System.Boolean)">
      <summary>Moves or extends the text range to the end of the nearest specified text unit. The text range is moved or extended forward in the document.</summary>
      <param name="unit">The unit by which to move the end position of the text range.</param>
      <param name="extend">true to extend the text range by moving just the end position of the range to the end of the specified unit. false to move both ends of the text range to the end of the specified unit. The default is false.</param>
      <returns>The number of character positions that the range was moved or extended, plus 1 if the text range collapsed to the start of the range. If the text range includes the final carriage return (CR) at the end of the story, and extend is false, the return value is set to –1 to indicate that the collapse occurred before the end of the range. This is because an insertion point cannot exist beyond the final CR.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.Expand(Microsoft.UI.Text.TextRangeUnit)">
      <summary>Expands a text range to completely contain any partial text units.</summary>
      <param name="unit">The text unit to use to expand the range. The default value is Word.</param>
      <returns>The number of characters added to the text range, if the range was expanded to include a partially contained _unit_.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.FindText(System.String,System.Int32,Microsoft.UI.Text.FindOptions)">
      <summary>Searches for a particular text string in a range and, if found, selects the string.</summary>
      <param name="value">The text string to search for.</param>
      <param name="scanLength">The maximum number of characters to search. It can be one of the following.</param>
      <param name="options">The options to use when doing the text search.</param>
      <returns>The length of the matching text string, or zero if no matching string is found.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.GetCharacterUtf32(System.UInt32@,System.Int32)">
      <summary>Retrieves the Unicode Transformation Format (UTF)-32 character code of the character at the specified offset from the end of the text range.</summary>
      <param name="value">The character value.</param>
      <param name="offset">The offset from the end of the text range.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.GetClone">
      <summary>Creates a new object that is identical to this text range object.</summary>
      <returns>The duplicate text range object.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.GetIndex(Microsoft.UI.Text.TextRangeUnit)">
      <summary>Retrieves the story index of the text unit (word, line, sentence, paragraph, and so on) at the starting character position of the text range.</summary>
      <param name="unit">The text unit that is indexed.</param>
      <returns>The index value. The value is zero if unit does not exist.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.GetPoint(Microsoft.UI.Text.HorizontalCharacterAlignment,Microsoft.UI.Text.VerticalCharacterAlignment,Microsoft.UI.Text.PointOptions,Windows.Foundation.Point@)">
      <summary>Retrieves the screen coordinates of a particular location in the text range.</summary>
      <param name="horizontalAlign">The horizontal position to retrieve, relative to the bounding rectangle of the text range.</param>
      <param name="verticalAlign">The vertical position to retrieve, relative to the bounding rectangle of the text range.</param>
      <param name="options">The options for retrieving the coordinates of the specified location in the text range.</param>
      <param name="point">A structure that receives the coordinates of the specified location in the text range, represented as an ordered pair of floating-point x- and y-coordinates that define a point in a two-dimensional plane.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.GetRect(Microsoft.UI.Text.PointOptions,Windows.Foundation.Rect@,System.Int32@)">
      <summary>Retrieves the bounding rectangle that encompasses the text range on the screen.</summary>
      <param name="options">A value that indicates the rectangle to retrieve.</param>
      <param name="rect">A structure that contains four floating-point numbers that represent the location and size of the bounding rectangle.</param>
      <param name="hit">The hit-test value for the text range.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.GetText(Microsoft.UI.Text.TextGetOptions,System.String@)">
      <summary>Retrieves the text in a text range according to the specified conversion flags.</summary>
      <param name="options">The conversion flags that control how the text is retrieved.</param>
      <param name="value">The text in the text range.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.GetTextViaStream(Microsoft.UI.Text.TextGetOptions,Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Retrieves the text in the text range according to the specified conversion flags, as a random access stream.</summary>
      <param name="options">The conversion flags that control how the text is retrieved. A value of default retrieves the plain text in the text range.</param>
      <param name="value">The text stream.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.InRange(Microsoft.UI.Text.ITextRange)">
      <summary>Determines whether this range is in or at the same text as a specified range.</summary>
      <param name="range">Text that is compared to the current range.</param>
      <returns>The comparison result. The result can be null. The method returns true if the range is in or at the same text as ITextRange; otherwise, it returns false.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.InsertImage(System.Int32,System.Int32,System.Int32,Microsoft.UI.Text.VerticalCharacterAlignment,System.String,Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Inserts an image into this range.</summary>
      <param name="width">The width of the image, in Device-independent pixels (DIPs).</param>
      <param name="height">The height of the image, in DIPs.</param>
      <param name="ascent">If _verticalAlign_ is Baseline, this parameter is the distance, in DIPs, that the top of the image extends above the text baseline. If _verticalAlign_ is Baseline and ascent is zero, the bottom of the image is placed at the text baseline.</param>
      <param name="verticalAlign">The vertical alignment of the image.</param>
      <param name="alternateText">The alternate text for the image.</param>
      <param name="value">The stream that contains the image data.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.InStory(Microsoft.UI.Text.ITextRange)">
      <summary>Determines whether this range's story is the same as a specified range's story.</summary>
      <param name="range">The ITextRange object whose story is compared to this range's story.</param>
      <returns>The comparison result. The result can be null. The method returns true if this range's story is the same as that of the ITextRange; otherwise it returns false.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.IsEqual(Microsoft.UI.Text.ITextRange)">
      <summary>Determines whether this range has the same character positions and story as those of a specified range.</summary>
      <param name="range">The text range to compare to this text range.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.MatchSelection">
      <summary>Sets the start and end positions of this range to match the active selection.</summary>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.Move(Microsoft.UI.Text.TextRangeUnit,System.Int32)">
      <summary>Moves the insertion point forward or backward by the specified number of _unit_s. If the text range is nondegenerate, it is collapsed to an insertion point at the start or end position of the text range, depending on _count_, and then is moved.</summary>
      <param name="unit">The units to move the insertion point. The default value is Character.</param>
      <param name="count">The number of _unit_s to move the insertion point. The default value is 1. If _count_ is greater than zero, the insertion point moves forward, toward the end of the story. If _count_ is less than zero, the insertion point moves backward, toward the beginning of the story. If _count_ is zero, the range is unchanged.</param>
      <returns>The actual number of units the insertion point moves.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.MoveEnd(Microsoft.UI.Text.TextRangeUnit,System.Int32)">
      <summary>Moves the end position of the text range.</summary>
      <param name="unit">The unit by which to move the end position of the text range. The default value is Character.</param>
      <param name="count">The number of _unit_s to move the end position of the text range. The default value is 1. If _count_ is greater than zero, the end position moves forward, toward the end of the story. If _count_ is less than zero, the end position move backward, toward the beginning of the story. If _count_ is zero, the end position does not move.</param>
      <returns>The actual number of _unit_s that the end position of the text range moved.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.MoveStart(Microsoft.UI.Text.TextRangeUnit,System.Int32)">
      <summary>Moves the start position of a text range.</summary>
      <param name="unit">The unit by which to move the start position of the text range. The default value is Character.</param>
      <param name="count">The number of _unit_s to move the start position of the text range. The default value is 1. If _count_ is greater than zero, the start position of the text range moves forward, toward the end of the story. If _count_ is less than zero, the start position of the text range moves backward, toward the beginning of the story. If _count_ is zero, the start position doesn't move.</param>
      <returns>The actual number of _unit_s that the start position moved. The pointer can be null.</returns>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.Paste(System.Int32)">
      <summary>Pastes text from the Clipboard into the text range.</summary>
      <param name="format">The clipboard format to use in the paste operation. Zero represents the best format, which usually is Rich Text Format (RTF), but CF_UNICODETEXT and other formats are also possible. The default value is zero.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.ScrollIntoView(Microsoft.UI.Text.PointOptions)">
      <summary>Scrolls this text range into view.</summary>
      <param name="value">The end of the text range to scroll into view. This function uses only the Start, NoHorizontalScroll, and NoVerticalScroll values of the PointOptions enumeration.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.SetIndex(Microsoft.UI.Text.TextRangeUnit,System.Int32,System.Boolean)">
      <summary>Moves the text range to the specified unit of the story.</summary>
      <param name="unit">The unit used to move the text range.</param>
      <param name="index">The index of the specified unit. The text range is relocated to the unit that has this index. If unit is positive, the numbering of units begins at the start of the story and proceeds forward. If negative, the numbering begins at the end of the story and proceeds backward. The start of the story corresponds to index = 1 for all existing units, and the last unit in the story corresponds to index = – 1.</param>
      <param name="extend">Indicates how to change the text range. True extends the text range to include the unit by moving only the end position of the text range. False collapses the text range to an insertion point and then moves the insertion point. The default value is false.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.SetPoint(Windows.Foundation.Point,Microsoft.UI.Text.PointOptions,System.Boolean)">
      <summary>Changes the text range based on the specified point.</summary>
      <param name="point">An ordered pair of floating-point x- and y-coordinates that defines a point in a two-dimensional plane.</param>
      <param name="options">The alignment type of the specified _point_.</param>
      <param name="extend">Indicates how to set the endpoints of the text range. If extend is 0, the text range is an insertion point located at the specified point, or at the nearest point with selectable text. If extend is 1, the endpoint specified by options is moved to the point and the other endpoint is left alone. The default is 0.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.SetRange(System.Int32,System.Int32)">
      <summary>Sets the endpoints of the text range to the specified values.</summary>
      <param name="startPosition">The character position for the start of the text range. This parameter must be less than _endPosition_.</param>
      <param name="endPosition">The character position for the end of the text range.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.SetText(Microsoft.UI.Text.TextSetOptions,System.String)">
      <summary>Replaces the text in the text range.</summary>
      <param name="options">The conversion flags that control how the text is inserted in the text range.</param>
      <param name="value">The new text.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.SetTextViaStream(Microsoft.UI.Text.TextSetOptions,Windows.Storage.Streams.IRandomAccessStream)">
      <summary>Sets the text in the text range based on the contents of a random access stream.</summary>
      <param name="options">The text options.</param>
      <param name="value">The random access stream.</param>
    </member>
    <member name="M:Microsoft.UI.Text.RichEditTextRange.StartOf(Microsoft.UI.Text.TextRangeUnit,System.Boolean)">
      <summary>Moves or extends the text range to the start of the nearest specified text unit. The text range is moved or extended backward in the document.</summary>
      <param name="unit">The unit by which to move the start position of the text range. The default value is Word.</param>
      <param name="extend">true to extend the text range by moving just the start position of the range to the start of the specified unit. false to move both ends of the text range to the start of the specified unit. The default is false.</param>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.Character">
      <summary>Gets or sets the first character of the text range; that is, the character associated with the StartPosition property.</summary>
      <returns>The value of the first character in the text range.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.CharacterFormat">
      <summary>Gets or sets the character formatting attributes of the text range.</summary>
      <returns>The character formatting attributes.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.EndPosition">
      <summary>Gets or sets the end character position of the text range.</summary>
      <returns>The end character position.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.FormattedText">
      <summary>Gets or sets an ITextRange object with the formatted text of the specified range.</summary>
      <returns>The formatted text.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.Gravity">
      <summary>Gets or sets the gravity of the text range.</summary>
      <returns>The gravity of the text range.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.Length">
      <summary>Gets the count of characters in the text range.</summary>
      <returns>The count of characters.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.Link">
      <summary>Gets or sets the URL text associated with a text range.</summary>
      <returns>The URL as text.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.ParagraphFormat">
      <summary>Gets or sets the paragraph formatting attributes of the text range.</summary>
      <returns>The paragraph formatting attributes.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.StartPosition">
      <summary>Gets or sets the start position of the text range.</summary>
      <returns>The character position to set as the start position of the text range.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.StoryLength">
      <summary>Gets the count of characters in the story of the text range.</summary>
      <returns>The count of characters in the story.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.RichEditTextRange.Text">
      <summary>Gets or sets the plain text of the text range.</summary>
      <returns>The plain text.</returns>
    </member>
    <member name="T:Microsoft.UI.Text.SelectionOptions">
      <summary>Describes the options that apply to a selection.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionOptions.Active">
      <summary>The selection is active; that is, the text control has the input focus.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionOptions.AtEndOfLine">
      <summary>For a degenerate selection (insertion point), the character position at the beginning of a line is the same as the character position at the end of the preceding line. As such, the character position is ambiguous. If this flag is 1, display the caret at the end of the preceding line; otherwise, display it at the beginning of the line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionOptions.Overtype">
      <summary>Insert/overtype mode is set to overtype.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionOptions.Replace">
      <summary>Typing and pasting replaces the selection.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionOptions.StartActive">
      <summary>The start position of the selection is the active end; that is, the end that is changed by pressing Shift+Right Arrow and Shift+Left Arrow.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.SelectionType">
      <summary>Specifies the type of a selection.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionType.InlineShape">
      <summary>An image (see ITextRange.InsertImage).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionType.InsertionPoint">
      <summary>An insertion point.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionType.None">
      <summary>No selection and no insertion point.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionType.Normal">
      <summary>A single nondegenerate range.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.SelectionType.Shape">
      <summary>A shape.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.TabAlignment">
      <summary>Alignment options for tab positions.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabAlignment.Bar">
      <summary>A vertical bar is positioned at the tab position. Text is not affected. Alignment bars on nearby lines at the same position form a continuous vertical line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabAlignment.Center">
      <summary>Text is centered on the tab position.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabAlignment.Decimal">
      <summary>The decimal point is set at the tab position. This is useful for aligning a column of decimal numbers.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabAlignment.Left">
      <summary>Text is left justified from the tab position. This is the default.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabAlignment.Right">
      <summary>Text is right justified from the tab position.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.TabLeader">
      <summary>The character that is used to fill the space taken by a tab character.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabLeader.Dashes">
      <summary>A dashed line is used.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabLeader.Dots">
      <summary>Dots are used.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabLeader.Equals">
      <summary>An equal sign is used.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabLeader.Lines">
      <summary>A solid line is used.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabLeader.Spaces">
      <summary>Spaces are used. This is the default.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TabLeader.ThickLines">
      <summary>A thick line is used.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.TextConstants">
      <summary>Defines a set of constants that are used with various methods in the Windows.UI.Text namespace.</summary>
    </member>
    <member name="P:Microsoft.UI.Text.TextConstants.AutoColor">
      <summary>Gets the default color.</summary>
      <returns>The default color.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.TextConstants.MaxUnitCount">
      <summary>Gets the maximum unit count.</summary>
      <returns>The maximum unit count.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.TextConstants.MinUnitCount">
      <summary>Gets the minimum unit count.</summary>
      <returns>The minimum unit count.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.TextConstants.UndefinedColor">
      <summary>Gets the undefined color value.</summary>
      <returns>The undefined color.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.TextConstants.UndefinedFloatValue">
      <summary>Gets the undefined floating-point value.</summary>
      <returns>The undefined floating-point value.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.TextConstants.UndefinedFontStretch">
      <summary>Gets the undefined font stretch value.</summary>
      <returns>The undefined font stretch value.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.TextConstants.UndefinedFontStyle">
      <summary>Gets the undefined font style.</summary>
      <returns>The undefined font style.</returns>
    </member>
    <member name="P:Microsoft.UI.Text.TextConstants.UndefinedInt32Value">
      <summary>Gets the undefined 32-bit integer value.</summary>
      <returns>The undefined 32-bit integer value.</returns>
    </member>
    <member name="T:Microsoft.UI.Text.TextGetOptions">
      <summary>Specifies options for retrieving the text in a document or text range.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextGetOptions.AdjustCrlf">
      <summary>If the starting character position is in the middle of a construct such as a CRLF (U+000D U+000A), surrogate pair, variation-selector sequence, or table-row delimiter, move to the beginning of the construct.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextGetOptions.AllowFinalEop">
      <summary>Allow retrieving the final end-of-paragraph (EOP) if it's included in the range. This EOP exists in all rich-text controls and cannot be deleted. It does not exist in plain-text controls.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextGetOptions.FormatRtf">
      <summary>Retrieve Rich Text Format (RTF) instead of plain text. Rich Text Format (RTF) is a BYTE (8-bit) format, but because GetText returns a string, the Rich Text Format (RTF) is returned as WCHARs (16-bit or UTF-16), not bytes, when you call GetText with the FormatRtf value. When you call ITextRange.SetText with FormatRtf, the method accepts an string containing either bytes or WCHARs, but other Rich Text Format (RTF) readers only understand bytes.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextGetOptions.IncludeNumbering">
      <summary>Include list numbers.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextGetOptions.NoHidden">
      <summary>Don't include hidden text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextGetOptions.None">
      <summary>None of the following options is used.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextGetOptions.UseCrlf">
      <summary>Use carriage return/line feed (CR/LF) in place of a carriage return.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextGetOptions.UseLf">
      <summary>Use line feed (LF) in place of all carriage returns.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextGetOptions.UseObjectText">
      <summary>Retrieve text including the alternate text for the images in the range.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.TextRangeUnit">
      <summary>Specifies the units to use when navigating a text range.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.AllCaps">
      <summary>Text in all uppercase.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Bold">
      <summary>Bold text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Character">
      <summary>A single character.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.CharacterFormat">
      <summary>A text run of characters that all have identical character formatting properties.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Cluster">
      <summary>A complex-script cluster (occurs, for example, in Indic scripts).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.ContentLink">
      <summary>ContentLink text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Disabled">
      <summary>Disabled text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.FontBound">
      <summary>Text is in a font-bound font. That is, characters that can't be displayed with the current font were assigned a different font that could display the characters.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.HardParagraph">
      <summary>A paragraph that is ended by a carriage return (CR) or carriage return/line feed (CR/LF).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Hidden">
      <summary>Hidden text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Imprint">
      <summary>Imprinted (engraved) text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Italic">
      <summary>Italic text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Line">
      <summary>A single line of text on a display, provided that the display is associated with the range. If no display is associated with a range, Line is treated as Paragraph. A selection automatically has a display.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Link">
      <summary>Hyperlink text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.LinkProtected">
      <summary>Characters in one or more contiguous, friendly-name hyperlinks. To work with single links that might be adjacent, use the Link unit.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Object">
      <summary>An embedded object.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Outline">
      <summary>Outline text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Paragraph">
      <summary>A string of text terminated by an end-of-paragraph mark, such as carriage return/line feed (CR/LF), carriage return (CR), vertical tab(VT), line feed (LF), form feed (FF), or the Unicode paragraph separator (0x2029).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.ParagraphFormat">
      <summary>A text run of characters that all have identical paragraph formatting properties.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.ProtectedText">
      <summary>Protected text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Revised">
      <summary>Revised text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Screen">
      <summary>The contents of a screen. Typically, a screen is the amount of content associated with the Page Up or Page Down key.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Section">
      <summary>A section.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Sentence">
      <summary>A string of text that meets the following criteria:
+ Ends with a period, question mark, or exclamation mark.
+ Is followed by one or more ASCII white space characters (9 through 0xd and 0x20), or the Unicode paragraph separator (0x2029). The trailing white space is part of the sentence.
+ The last sentence in a story does not need to have a period, question mark, or exclamation mark.
+ Other sentences must follow a sentence end and cannot begin with a period, question mark, or exclamation mark.
+ The start of a story qualifies as the start of a Sentence, even if the string there doesn't qualify as a sentence grammatically.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Shadow">
      <summary>Shadow text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.SmallCaps">
      <summary>Text in small caps.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Story">
      <summary>A story, which is a contiguous range of text in a document. For example, a story can contain one of the various parts of a document, such as the main text of a document, headers and footers, footnotes, or annotations. In a rich edit control, there is only one story per document, although a client can use multiple documents to represent multiple stories.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Strikethrough">
      <summary>Strikethrough text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Subscript">
      <summary>Text in the subscript character format.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Superscript">
      <summary>Text in the superscript character format.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Underline">
      <summary>Underlined text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Window">
      <summary>The characters between the upper-left and lower-right corners of the window.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextRangeUnit.Word">
      <summary>A span of alphanumeric characters, an end of paragraph, or punctuation that includes any blanks that follow.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.TextScript">
      <summary>Specifies the character repertoire for a run of character formatting.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Aboriginal">
      <summary>Aboriginal</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Ansi">
      <summary>Latin 1 (ANSI)</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Arabic">
      <summary>Arabic</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Armenian">
      <summary>Armenian</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Baltic">
      <summary>From Latin 1 and 2</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Bengali">
      <summary>Bangla</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Big5">
      <summary>Traditional Chinese</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Braille">
      <summary>Braille</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Cherokee">
      <summary>Cherokee</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Cyrillic">
      <summary>Cyrillic</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Default">
      <summary>Default character repertoire</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Deseret">
      <summary>Deseret</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Devanagari">
      <summary>Devanagari</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.EastEurope">
      <summary>Latin 1 and Latin 2</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Emoji">
      <summary>Emoji</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Ethiopic">
      <summary>Ethiopic</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.GB2312">
      <summary>Simplified Chinese</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Georgian">
      <summary>Georgian</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Glagolitic">
      <summary>Glagolitic</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Gothic">
      <summary>Gothic</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Greek">
      <summary>Greek</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Gujarati">
      <summary>Gujarati</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Gurmukhi">
      <summary>Gurmukhi</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Hangul">
      <summary>Hangul</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Hebrew">
      <summary>Hebrew</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Jamo">
      <summary>Jamo</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Kannada">
      <summary>Kannada</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Kayahli">
      <summary>Kayahli</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Kharoshthi">
      <summary>Kharoshthi</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Khmer">
      <summary>Khmer</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Lao">
      <summary>Lao</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Limbu">
      <summary>Limbu</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Lisu">
      <summary>Lisu</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Mac">
      <summary>Main Macintosh character repertoire</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Malayalam">
      <summary>Malayalam</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Mongolian">
      <summary>Mongolian</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Myanmar">
      <summary>Myanmar</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.NewTaiLue">
      <summary>TaiLu</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.NKo">
      <summary>NKo</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Oem">
      <summary>OEM character set (original PC)</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Ogham">
      <summary>Ogham</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Oriya">
      <summary>Odia</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Osmanya">
      <summary>Osmanya</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.PC437">
      <summary>PC437 character set (disk operating system (DOS))</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.PhagsPa">
      <summary>PhagsPa</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Runic">
      <summary>Runic</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.ShiftJis">
      <summary>Japanese</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Sinhala">
      <summary>Sinhala</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.SylotiNagri">
      <summary>Syloti Nagri</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Symbol">
      <summary>Symbol character set (not Unicode)</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Syriac">
      <summary>Syriac</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.TaiLe">
      <summary>TaiLe</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Tamil">
      <summary>Tamil</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Telugu">
      <summary>Telugu</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Thaana">
      <summary>Thaana</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Thai">
      <summary>Thai</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Tibetan">
      <summary>Tibetan</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Tifinagh">
      <summary>Tifinagh</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Turkish">
      <summary>Turkish (Latin 1 + dotless i, and so on)</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Undefined">
      <summary>Undefined</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.UnicodeSymbol">
      <summary>Unicode symbol such as math operators</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Vai">
      <summary>Vai</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Vietnamese">
      <summary>Latin 1 with some combining marks</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextScript.Yi">
      <summary>Yi (Nuosu or Nosu, also known as Northern Yi, Liangshan Yi, and Sichuan Yi)</summary>
    </member>
    <member name="T:Microsoft.UI.Text.TextSetOptions">
      <summary>Specifies options for setting the text in a text range.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextSetOptions.ApplyRtfDocumentDefaults">
      <summary>Apply the Rich Text Format (RTF) default settings for the document. Rich Text Format (RTF) often contains document default properties. These properties are typically ignored when inserting Rich Text Format (RTF) (as distinguished from opening an Rich Text Format (RTF) file).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextSetOptions.CheckTextLimit">
      <summary>Obey the current text limit instead of increasing the limit to fit.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextSetOptions.FormatRtf">
      <summary>Treat input text as Rich Text Format (RTF) (the Rich Text Format (RTF) text will be validated).</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextSetOptions.None">
      <summary>No text setting option is specified.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextSetOptions.Unhide">
      <summary>Don't insert as hidden text.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextSetOptions.UnicodeBidi">
      <summary>Use the Unicode bidirectional algorithm.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.TextSetOptions.Unlink">
      <summary>Don't include text as part of a hyperlink.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.UnderlineType">
      <summary>Specifies the type of character underlining.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.Dash">
      <summary>A dashed line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.DashDot">
      <summary>Alternating dashes and dots.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.DashDotDot">
      <summary>Single dashes, each followed by two dots.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.Dotted">
      <summary>A dotted line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.Double">
      <summary>Two solid double lines.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.DoubleWave">
      <summary>Two wavy lines.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.HeavyWave">
      <summary>A thick wavy line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.LongDash">
      <summary>Long dashes.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.None">
      <summary>Characters are not underlined.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.Single">
      <summary>A single solid line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.Thick">
      <summary>A thick solid line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.ThickDash">
      <summary>Thick dashes.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.ThickDashDot">
      <summary>Thick, alternating dashes and dots.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.ThickDashDotDot">
      <summary>Thick single dashes, each followed by two thick dots.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.ThickDotted">
      <summary>A thick dotted line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.ThickLongDash">
      <summary>Thick long dashes.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.Thin">
      <summary>A thin solid line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.Undefined">
      <summary>No underline type is defined.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.Wave">
      <summary>A wavy line.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.UnderlineType.Words">
      <summary>Underline words, but not the spaces between words.</summary>
    </member>
    <member name="T:Microsoft.UI.Text.VerticalCharacterAlignment">
      <summary>Specifies the vertical position of a character relative to a bounding rectangle.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.VerticalCharacterAlignment.Baseline">
      <summary>The character is positioned at the text baseline.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.VerticalCharacterAlignment.Bottom">
      <summary>The character is positioned at the bottom edge of the bounding rectangle.</summary>
    </member>
    <member name="F:Microsoft.UI.Text.VerticalCharacterAlignment.Top">
      <summary>The character is positioned at the top edge of the bounding rectangle.</summary>
    </member>
  </members>
</doc>