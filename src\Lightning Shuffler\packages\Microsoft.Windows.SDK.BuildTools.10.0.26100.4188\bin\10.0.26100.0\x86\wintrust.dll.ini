[0]
DLL=msisip.dll
GUID={000C10F1-0000-0000-C000-000000000046}
CryptSIPDllCreateIndirectData=MsiSIPCreateIndirectData
CryptSIPDllGetSignedDataMsg=MsiSIPGetSignedDataMsg
CryptSIPDllIsMyFileType2=MsiSIPIsMyTypeOfFile
CryptSIPDllPutSignedDataMsg=MsiSIPPutSignedDataMsg
CryptSIPDllRemoveSignedDataMsg=MsiSIPRemoveSignedDataMsg
CryptSIPDllVerifyIndirectData=MsiSIPVerifyIndirectData


[1]
DLL=AppxSip.dll
GUID={0AC5DF4B-CE07-4DE2-B76E-23C839A09FD1}
CryptSIPDllCreateIndirectData=AppxSipCreateIndirectData
CryptSIPDllGetSignedDataMsg=AppxSipGetSignedDataMsg
CryptSIPDllIsMyFileType2=AppxSipIsFileSupportedName
CryptSIPDllPutSignedDataMsg=AppxSipPutSignedDataMsg
CryptSIPDllRemoveSignedDataMsg=AppxSipRemoveSignedDataMsg
CryptSIPDllVerifyIndirectData=AppxSipVerifyIndirectData


[2]
DLL=AppxSip.dll
GUID={0F5F58B3-AADE-4B9A-A434-95742D92ECEB}
CryptSIPDllCreateIndirectData=AppxBundleSipCreateIndirectData
CryptSIPDllGetSignedDataMsg=AppxBundleSipGetSignedDataMsg
CryptSIPDllIsMyFileType2=AppxBundleSipIsFileSupportedName
CryptSIPDllPutSignedDataMsg=AppxBundleSipPutSignedDataMsg
CryptSIPDllRemoveSignedDataMsg=AppxBundleSipRemoveSignedDataMsg
CryptSIPDllVerifyIndirectData=AppxBundleSipVerifyIndirectData


[3]
DLL=AppxSip.dll
GUID={CF78C6DE-64A2-4799-B506-89ADFF5D16D6}
CryptSIPDllCreateIndirectData=EappxSipCreateIndirectData
CryptSIPDllGetSignedDataMsg=EappxSipGetSignedDataMsg
CryptSIPDllIsMyFileType2=EappxSipIsFileSupportedName
CryptSIPDllPutSignedDataMsg=EappxSipPutSignedDataMsg
CryptSIPDllRemoveSignedDataMsg=EappxSipRemoveSignedDataMsg
CryptSIPDllVerifyIndirectData=EappxSipVerifyIndirectData


[4]
DLL=AppxSip.dll
GUID={D1D04F0C-9ABA-430D-B0E4-D7E96ACCE66C}
CryptSIPDllCreateIndirectData=EappxBundleSipCreateIndirectData
CryptSIPDllGetSignedDataMsg=EappxBundleSipGetSignedDataMsg
CryptSIPDllIsMyFileType2=EappxBundleSipIsFileSupportedName
CryptSIPDllPutSignedDataMsg=EappxBundleSipPutSignedDataMsg
CryptSIPDllRemoveSignedDataMsg=EappxBundleSipRemoveSignedDataMsg
CryptSIPDllVerifyIndirectData=EappxBundleSipVerifyIndirectData