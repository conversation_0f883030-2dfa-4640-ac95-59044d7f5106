﻿<!-- Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. See LICENSE in the project root for license information. -->
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <PropertyGroup>
    <!--Foundation packaging and build behavior is determined by the WindowsAppSdkFoundation property:
        If true, this is the WindowsAppSdk deployment of Foundation, versus the internal deployment for test only.
        This property is defined by deployment-specific props imports, and should not be overridden by clients.-->
    <WindowsAppSdkFoundation>true</WindowsAppSdkFoundation>
  </PropertyGroup>

  <!--
    Ideally, this import would be done only if EnableCoreMrtTooling is true. That's impossible though because that
    property cannot be defined beforehand.
  -->
  <Import Project="$(MSBuildThisFileDirectory)MrtCore.props" />

</Project>
