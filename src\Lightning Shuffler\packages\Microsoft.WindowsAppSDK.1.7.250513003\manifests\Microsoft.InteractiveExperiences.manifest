﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <file name="CoreMessagingXP.dll" xmlns="urn:schemas-microsoft-com:asm.v1">
    <activatableClass name="Microsoft.UI.Dispatching.DispatcherExitDeferral" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Dispatching.DispatcherQueue" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Dispatching.DispatcherQueueController" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="dcompi.dll" xmlns="urn:schemas-microsoft-com:asm.v1">
    <activatableClass name="Microsoft.UI.Composition.AnimationController" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionApiInformation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionCapabilities" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionClip" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionDrawingSurface" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionEasingFunction" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionEffectSourceParameter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionGeometry" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionGradientBrush" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionLight" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionObject" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionPath" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionProjectedShadowCasterCollection" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionShadow" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionShape" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionTransform" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.CompositionVirtualDrawingSurface" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Compositor" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.ContainerVisual" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Core.CompositorController" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Diagnostics.CompositionDebugSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Effects.SceneLightingEffect" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Experimental.ExpCompositionVisualSurface" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Interactions.CompositionConditionalValue" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTracker" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaModifier" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaMotion" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaNaturalMotion" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerInertiaRestingValue" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerVector2InertiaModifier" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Interactions.InteractionTrackerVector2InertiaNaturalMotion" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Interactions.VisualInteractionSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.KeyFrameAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.NaturalMotionAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.ScalarNaturalMotionAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneComponent" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneMaterial" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneMaterialInput" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneMesh" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneMeshRendererComponent" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneMetallicRoughnessMaterial" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneNode" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneObject" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.ScenePbrMaterial" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneRendererComponent" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneSurfaceMaterialInput" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Scenes.SceneVisual" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Vector2NaturalMotionAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Vector3NaturalMotionAnimation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.Visual" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="Microsoft.Graphics.Display.dll" xmlns="urn:schemas-microsoft-com:asm.v1">
    <activatableClass name="Microsoft.Graphics.Display.DisplayInformation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="Microsoft.UI.dll" xmlns="urn:schemas-microsoft-com:asm.v1">
    <activatableClass name="Microsoft.UI.Colors" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.ColorHelper" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.System.ThemeSettings" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="Microsoft.UI.Input.dll" xmlns="urn:schemas-microsoft-com:asm.v1">
    <activatableClass name="Microsoft.UI.Content.ChildSiteLink" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentAppWindowBridge" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentCoordinateConverter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentExternalBackdropLink" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentExternalOutputLink" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentIsland" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentIslandEnvironment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentSite" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentSiteEnvironment" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentSiteEnvironmentView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ContentSiteView" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.CoreWindowSiteBridge" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.DesktopAttachedSiteBridge" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.DesktopChildSiteBridge" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.DesktopPopupSiteBridge" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.DesktopSiteBridge" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.ProcessStarter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Content.SystemVisualSiteBridge" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.DragDrop.DragDropManager" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.DragDrop.DragOperation" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.Experimental.ExpPointerPoint" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.FocusNavigationRequest" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.GestureRecognizer" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputActivationListener" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputCursor" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputCustomCursor" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputDesktopResourceCursor" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputDesktopNamedResourceCursor" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputFocusController" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputFocusNavigationHost" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputKeyboardSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputLightDismissAction" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputObject" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputPreTranslateKeyboardSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputNonClientPointerSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputPointerSource" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.InputSystemCursor" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.Interop.PenDeviceInterop" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Input.PointerPredictor" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="Microsoft.UI.Windowing.dll" xmlns="urn:schemas-microsoft-com:asm.v1">
    <activatableClass name="Microsoft.UI.Windowing.AppWindow" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Windowing.AppWindowPlacementDetails" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Windowing.AppWindowPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Windowing.CompactOverlayPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Windowing.DisplayArea" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Windowing.FullScreenPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Windowing.OverlappedPresenter" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="Microsoft.UI.Windowing.Core.dll" xmlns="urn:schemas-microsoft-com:asm.v1">
    <activatableClass name="Microsoft.UI.Windowing.AppWindowTitleBar" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
  <file name="wuceffectsi.dll" xmlns="urn:schemas-microsoft-com:asm.v1">
    <activatableClass name="Microsoft.UI.Composition.SystemBackdrops.DesktopAcrylicController" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.SystemBackdrops.MicaController" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
    <activatableClass name="Microsoft.UI.Composition.SystemBackdrops.SystemBackdropConfiguration" threadingModel="both" xmlns="urn:schemas-microsoft-com:winrt.v1" />
  </file>
</assembly>