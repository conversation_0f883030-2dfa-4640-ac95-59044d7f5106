<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) Microsoft Corporation and Contributors. -->
<!-- Licensed under the MIT License. -->

<ProjectSchemaDefinitions xmlns="http://schemas.microsoft.com/build/2009/properties">
    <!--
    The “UpToDateCheckInput” attribute on the ItemType definition informs the Fast Up To Date (FUTD) system that it
    should be tracked as part of the *default* set of inputs/outputs. This isn’t the behavior we want, though, because
    .resw files are not an input that influence the “default” set of outputs (e.g. .dll/.exe/.pdb); the timestamp of
    .resw files should not be compared with that of the .dll/.exe/.pdb because there’s no correlation between the two.
    So we override the built-in ItemType definition with our own, which turns off the automatic tracking, and then
    in MrtCore.PriGen.targets explicitly add *.resw to @(UpToDateCheckInputs) as part of a custom FUTD set for the
    output of the project PRI file.
    -->
    <ItemType Name="PRIResource" UpToDateCheckInput="false"/>
</ProjectSchemaDefinitions>
