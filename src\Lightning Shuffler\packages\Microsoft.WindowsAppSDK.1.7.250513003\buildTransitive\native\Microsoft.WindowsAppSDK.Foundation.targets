<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. See LICENSE in the project root for license information. -->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <Import Project="$(MSBuildThisFileDirectory)MrtCore.targets"/>

  <PropertyGroup>
    <Native-Platform Condition="'$(Platform)' == 'Win32'">x86</Native-Platform>
    <Native-Platform Condition="'$(Platform)' != 'Win32'">$(Platform)</Native-Platform>
  </PropertyGroup>

  <ItemGroup Condition="'$(AppxPackage)' != 'true'">
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.ApplicationModel.DynamicDependency.winmd">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <ItemGroup Condition="'$(AppxPackage)' == 'true'">
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.ApplicationModel.DynamicDependency.winmd">
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.winmd">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.ApplicationModel.Background.winmd">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

	<ItemGroup>
		<Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.winmd">
			<Private Condition="'$(WindowsAppSDKBackgroundTask)' == 'true'">true</Private>
			<Private Condition="'$(WindowsAppSDKBackgroundTask)' != 'true'">false</Private>
			<Implementation Condition="'$(WindowsAppSDKBackgroundTask)' == 'true'">$(MSBuildThisFileDirectory)..\..\runtimes\win-$(Platform)\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll</Implementation>
		</Reference>
	</ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.AppLifecycle.winmd">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.AppNotifications.Builder.winmd">
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.AppNotifications.winmd">
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.Globalization.winmd">
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.Management.Deployment.winmd">
      <Private>false</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.Storage.winmd">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.System.Power.winmd">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.Security.AccessControl.winmd">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.ApplicationModel.Resources.winmd">
      <Private>false</Private>
      <Implementation>Microsoft.Windows.ApplicationModel.Resources.dll</Implementation>
    </Reference>
  </ItemGroup>

  <!-- conditionally include experimental metadata -->
  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.System.winmd"
       Condition="Exists('$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.System.winmd')">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.PushNotifications.winmd"
       Condition="Exists('$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.PushNotifications.winmd')">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Security.Authentication.OAuth.winmd"
       Condition="Exists('$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Security.Authentication.OAuth.winmd')">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.Media.Capture.winmd"
       Condition="Exists('$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.Media.Capture.winmd')">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.BadgeNotifications.winmd"
       Condition="Exists('$(MSBuildThisFileDirectory)..\..\lib\uap10.0\Microsoft.Windows.BadgeNotifications.winmd')">
      <Private>false</Private>
      <Implementation>Microsoft.WindowsAppRuntime.dll</Implementation>
    </Reference>
  </ItemGroup>

  <Import Project="$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.BootstrapCommon.targets" />
  <Import Project="$(MSBuildThisFileDirectory)WindowsAppSDK-Nuget-Native.Bootstrap.targets" Condition="'$(WindowsAppSdkBootstrapInitialize)' == 'true'"/>

  <Import Project="$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.UndockedRegFreeWinRTCommon.targets" />
  <Import Project="$(MSBuildThisFileDirectory)WindowsAppSDK-Nuget-Native.UndockedRegFreeWinRT.targets" Condition="'$(WindowsAppSdkUndockedRegFreeWinRTInitialize)' == 'true'"/>

  <Import Project="$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.DeploymentManagerCommon.targets" />
  <Import Project="$(MSBuildThisFileDirectory)WindowsAppSDK-Nuget-Native.DeploymentManager.targets" Condition="'$(WindowsAppSdkDeploymentManagerInitialize)' == 'true'"/>

  <Import Project="$(MSBuildThisFileDirectory)WindowsAppSDK-Nuget-Native.CompatibilitySetter.targets" />

  <Import Project="$(MSBuildThisFileDirectory)..\Microsoft.WindowsAppSDK.AutoInitializerCommon.targets" />
  <Import Project="$(MSBuildThisFileDirectory)WindowsAppSDK-Nuget-Native.AutoInitializer.targets" Condition="'$(WindowsAppSdkAutoInitialize)' == 'true'"/>

</Project>
