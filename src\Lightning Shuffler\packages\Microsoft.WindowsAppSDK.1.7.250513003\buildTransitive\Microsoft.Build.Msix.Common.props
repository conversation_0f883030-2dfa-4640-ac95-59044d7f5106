<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <!-- Set MsAppxPackageTargets to some value so that Microsoft.Common.CurrentVersion.targets doesn't import it -->
    <MsAppxPackageTargets>Dont-Use-MsAppxPackageTargets</MsAppxPackageTargets>

    <!-- We are currently incompatible with the MRT Core tooling (conflicts due to duplicated,
         but not identical, logic and race conditions that depend on order of Nuget imports). For now
         we will disable the MRT Core tooling with an eye on resolving the incompatibilities once we
         share a repo. Tracked by http://task.ms/31928041-->
    <EnablePriGenTooling>false</EnablePriGenTooling>
    <EnableCoreMrtTooling>false</EnableCoreMrtTooling>

    <ProjectTypeGuids>9A19103F-16F7-4668-BE54-9A1E7A4F7556</ProjectTypeGuids>
  </PropertyGroup>

  <PropertyGroup>
    <!-- Defaults for advanced deployment properties -->
    <RemoteDeploymentType Condition="'$(RemoteDeploymentType)' == ''">CopyToDevice</RemoteDeploymentType>
    <RemoveNonLayoutFiles Condition="'$(RemoveNonLayoutFiles)' == ''">False</RemoveNonLayoutFiles>
    <DeployOptionalPackages Condition="'$(DeployOptionalPackages)' == ''">False</DeployOptionalPackages>
  </PropertyGroup>

  <!-- empty out this property since we are disabling the mrt tooling -->
  <Target Name="_UpdatePriIndex"
          AfterTargets="GetPriIndexName"
          Returns="$(PriIndexName)"
          Condition="'$(OutputType)'=='WinExe' or '$(OutputType)'=='Exe'">
    <PropertyGroup>
      <PriIndexName></PriIndexName>
    </PropertyGroup>
  </Target>

  <ItemGroup Condition="'$(EnableDefaultPriItems)'=='true' and '$(EnableDefaultItems)'=='true'and '$(UsingMicrosoftNETSdk)' == 'true'">
    <PRIResource Include="**/*.resw"
                 Exclude="$(DefaultItemExcludes);$(DefaultExcludesInProjectFolder)"/>

    <None Remove="**/*.resw" />
  </ItemGroup>

</Project>
