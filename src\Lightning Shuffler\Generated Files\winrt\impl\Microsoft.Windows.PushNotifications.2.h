// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_PushNotifications_2_H
#define WINRT_Microsoft_Windows_PushNotifications_2_H
#include "winrt/impl/Windows.Foundation.2.h"
#include "winrt/impl/Microsoft.Windows.PushNotifications.1.h"
WINRT_EXPORT namespace winrt::Microsoft::Windows::PushNotifications
{
    struct PushNotificationCreateChannelStatus
    {
        winrt::Microsoft::Windows::PushNotifications::PushNotificationChannelStatus status;
        winrt::hresult extendedError;
        uint32_t retryCount;
    };
    inline bool operator==(PushNotificationCreateChannelStatus const& left, PushNotificationCreateChannelStatus const& right) noexcept
    {
        return left.status == right.status && left.extendedError == right.extendedError && left.retryCount == right.retryCount;
    }
    inline bool operator!=(PushNotificationCreateChannelStatus const& left, PushNotificationCreateChannelStatus const& right) noexcept
    {
        return !(left == right);
    }
    struct WINRT_IMPL_EMPTY_BASES PushNotificationChannel : winrt::Microsoft::Windows::PushNotifications::IPushNotificationChannel
    {
        PushNotificationChannel(std::nullptr_t) noexcept {}
        PushNotificationChannel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::PushNotifications::IPushNotificationChannel(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PushNotificationCreateChannelResult : winrt::Microsoft::Windows::PushNotifications::IPushNotificationCreateChannelResult
    {
        PushNotificationCreateChannelResult(std::nullptr_t) noexcept {}
        PushNotificationCreateChannelResult(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::PushNotifications::IPushNotificationCreateChannelResult(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES PushNotificationManager : winrt::Microsoft::Windows::PushNotifications::IPushNotificationManager
    {
        PushNotificationManager(std::nullptr_t) noexcept {}
        PushNotificationManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::PushNotifications::IPushNotificationManager(ptr, take_ownership_from_abi) {}
        static auto IsSupported();
        [[nodiscard]] static auto Default();
    };
    struct WINRT_IMPL_EMPTY_BASES PushNotificationReceivedEventArgs : winrt::Microsoft::Windows::PushNotifications::IPushNotificationReceivedEventArgs
    {
        PushNotificationReceivedEventArgs(std::nullptr_t) noexcept {}
        PushNotificationReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::PushNotifications::IPushNotificationReceivedEventArgs(ptr, take_ownership_from_abi) {}
    };
}
#endif
