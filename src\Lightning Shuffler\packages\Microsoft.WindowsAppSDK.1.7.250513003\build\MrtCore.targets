<?xml version="1.0" encoding="utf-8" ?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="15.0">

  <!-- We want MRT Core to NOT be used in WAP or UWP projects. Instead, we want the in-box PRI generation tooling to be used. -->
  <PropertyGroup>
    <EnableCoreMrtTooling Condition="'$(EnableCoreMrtTooling)'=='' and '$(WindowsAppContainer)'!='true' and '$(MSBuildProjectExtension)' != '.wapproj'">true</EnableCoreMrtTooling>
  </PropertyGroup>

  <Import Project="$(MSBuildThisFileDirectory)MrtCore.PriGen.targets" Condition="'$(EnableCoreMrtTooling)'=='true'"/>
  <Import Project="$(MSBuildThisFileDirectory)MrtCore.References.targets" Condition="'$(EnableCoreMrtTooling)'=='true'"/>

</Project>
