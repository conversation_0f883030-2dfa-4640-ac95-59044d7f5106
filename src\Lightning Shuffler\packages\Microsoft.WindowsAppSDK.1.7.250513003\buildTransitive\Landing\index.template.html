﻿<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>
        <!-- inject:name -->
        Sample Name
        <!-- end -->
    </title>
    <!-- inject:css -->
    <link href="./style.css" type="text/css" rel="stylesheet">
    <!-- end -->
</head>

<body class="body">
    <div class="left-col mr-28">
        <!-- inject:image -->
        <img alt="application image" class="app-image" src="./image.png">
        <!-- end -->

        <div class="lg additional-links-title">
            <label for="achk">
                    <!-- inject:additionalLinksTitle -->
                    Additional Links
                    <!-- end -->
                    <i class="ms-Icon--ChevronDown ms-Icon sm"></i>
            </label>
        </div>
        <input type="checkbox" id="achk" style="display:none;">
        <div class="alinks">
            <!-- inject:additionalLinks -->
            <a href="#">Publisher Certificate</a>
            <a href="#">Appx File</a>
            <!-- end -->
        </div>
    </div>

    <div class="right-col">
        <div class="app-title xl  mb-24 dark-blue">
            <!-- inject:name -->Sample Name
            <!-- end -->
        </div>
        <div class="app-version lg mb-24">
            <!-- inject:versionTitle -->Version
            <!--end-->
            <!-- inject:version -->Sample Version
            <!-- end -->
        </div>
        <div class="app-description mb-24">
            <!-- inject:description -->
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vivamus gravida posuere libero. Vivamus fringilla, mi vel pellentesque
            porttitor, libero arcu ultricies tortor, quis consequat tellus risus sit amet magna. Nulla consequat rutrum mollis.
            Sed molestie cursus ex, eget tempor elit maximus nec. Maecenas risus orci, venenatis a elementum sagittis, vehicula
            fringilla odio. Maecenas id tristique lacus. Fusce in porttitor lorem. Proin ultrices, metus quis egestas elementum,
            arcu est malesuada mi, pellentesque fringilla orci purus vitae dolor. Sed enim lectus, tincidunt vitae tincidunt
            nec, euismod in lacus. Donec placerat, lacus ac vestibulum egestas, nunc diam commodo lacus, nec ultricies nulla
            sapien sit amet neque. Sed vehicula odio non sapien porta, vitae ultricies libero vulputate. mi vel pellentesque
            porttitor, libero arcu ultricies tortor, quis consequat tellus risus sit amet magna. Nulla consequat rutrum mollis.
            Sed molestie cursus ex, eget tempor elit maximus nec. Maecenas risus orci, venenatis a elementum sagittis, vehicula
            fringilla odio. Maecenas id tristique lacus. Fusce in porttitor lorem. Proin ultrices, metus quis egestas elementum,
            arcu est malesuada mi, pellentesque fringilla orci purus vitae dolor. Sed enim lectus, tincidunt vitae tincidunt
            nec, euismod in lacus. Donec placerat, lacus ac vestibulum egestas, nunc diam commodo lacus, nec ultricies nulla
            sapien sit amet neque. Sed vehicula odio non sapien porta, vitae ultricies libero vulputate.
            <!-- end -->
        </div>
        <div class="mb-12 dlbuttons">
            <!-- inject:buttons -->
            <a href="#">
                <button> Sample Arch 1</button>
            </a>
            <a href="#">
                <button> Sample Arch 2</button>
            </a>
            <a href="#">
                <button> Sample Arch 3</button>
            </a>
            <!-- end -->
        </div>
        <div class="mb-24">
            <a href="https://go.microsoft.com/fwlink/?linkid=870616">
                <!-- inject:troubleshoot -->Troubleshoot installation Link
                <!-- end -->
            </a>
        </div>
        <div class="mb-28 lg">
            <!-- inject:applicationInformation -->
            Application Information
            <!-- end -->
        </div>
        <div class="info-table">
            <div class="info-row">
                <div class="key">
                    <!-- inject:versionTitle -->Version
                    <!-- end -->
                </div>
                <div class="value">
                    <!-- inject:version -->Sample Version
                    <!-- end -->
                </div>
            </div>
            <br/>
            <div class="info-row">
                <div class="key">
                    <!-- inject:RequiredOsTitle -->Required Operating System
                    <!-- end -->
                </div>
                <div class="value">
                    <!-- inject:os -->Sample OS
                    <!-- end -->
                </div>
            </div>
            <br/>
            <div class="info-row">
                <div class="key">
                    <!-- inject:architecturesTitle -->Architectures
                    <!-- end -->
                </div>
                <div class="value">
                    <!-- inject:architectures -->Sample Architectures
                    <!-- end -->
                </div>
            </div>
            <br/>
            <div class="info-row">
                <div class="key">
                    <!-- inject:publisherTitle -->Publisher
                    <!-- end -->
                </div>
                <div class="value">
                    <!-- inject:publisher -->Sample Publisher
                    <!-- end -->
                </div>
            </div>
        </div>
    </div>
</body>

</html>
