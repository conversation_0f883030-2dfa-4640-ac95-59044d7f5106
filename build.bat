@echo off
setlocal enabledelayedexpansion

echo Lightning Shuffler Build Script
echo ================================

:: Set default values
set "CONFIG=Debug"
set "PLATFORM=x64"
set "CLEAN_BUILD=false"
set "RUN_APP=false"

:: Parse command line arguments
:parse_args
if "%~1"=="" goto :start_build
if /i "%~1"=="release" set "CONFIG=Release"
if /i "%~1"=="debug" set "CONFIG=Debug"
if /i "%~1"=="clean" set "CLEAN_BUILD=true"
if /i "%~1"=="run" set "RUN_APP=true"
if /i "%~1"=="x86" set "PLATFORM=x86"
if /i "%~1"=="x64" set "PLATFORM=x64"
if /i "%~1"=="arm64" set "PLATFORM=ARM64"
shift
goto :parse_args

:start_build
echo Configuration: %CONFIG%
echo Platform: %PLATFORM%
echo.

:: Setup Visual Studio environment
echo Setting up Visual Studio environment...
call "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Visual Studio 2022 Community not found!
    echo Please install Visual Studio 2022 with C++ development tools.
    exit /b 1
)

:: Change to project directory
cd /d "%~dp0src\Lightning Shuffler"

:: Clean if requested
if "%CLEAN_BUILD%"=="true" (
    echo Cleaning project...
    msbuild "Lightning Shuffler.sln" /t:Clean /p:Configuration=%CONFIG% /p:Platform=%PLATFORM%
    if errorlevel 1 (
        echo ERROR: Clean failed!
        exit /b 1
    )
    echo Clean completed successfully.
    echo.
)

:: Restore NuGet packages
echo Restoring NuGet packages...
nuget restore "Lightning Shuffler.sln" >nul 2>&1
if errorlevel 1 (
    echo Warning: NuGet restore failed, attempting MSBuild restore...
    msbuild "Lightning Shuffler.sln" /t:Restore
)

:: Build the project
echo Building project...
msbuild "Lightning Shuffler.sln" /p:Configuration=%CONFIG% /p:Platform=%PLATFORM% /verbosity:minimal
if errorlevel 1 (
    echo ERROR: Build failed!
    exit /b 1
)

echo Build completed successfully!
echo.

:: Run if requested
if "%RUN_APP%"=="true" (
    set "EXE_PATH=%PLATFORM%\%CONFIG%\Lightning Shuffler\Lightning_Shuffler.exe"
    if exist "!EXE_PATH!" (
        echo Running Lightning Shuffler...
        echo Executable: !EXE_PATH!
        cd /d "%PLATFORM%\%CONFIG%\Lightning Shuffler"
        start "" "Lightning_Shuffler.exe"
        cd /d "%~dp0src\Lightning Shuffler"
    ) else (
        echo ERROR: Executable not found at !EXE_PATH!
        echo Checking alternative paths...
        dir "%PLATFORM%\%CONFIG%" /s /b | findstr Lightning_Shuffler.exe
        exit /b 1
    )
)

echo Script completed successfully!
pause
