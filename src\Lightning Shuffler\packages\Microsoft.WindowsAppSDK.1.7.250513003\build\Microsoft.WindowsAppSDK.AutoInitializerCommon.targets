<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT License. See LICENSE in the project root for license information. -->
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!-- Targets file common to both managed and native projects -->

  <PropertyGroup Condition="'$(WindowsAppSdkAutoInitializeNeeded)' == '' and
        ('$(WindowsAppSdkBootstrapInitialize)' == 'true' or
        '$(WindowsAppSdkUndockedRegFreeWinRTInitialize)' == 'true' or
        '$(WindowsAppSdkDeploymentManagerInitialize)' == 'true' or
        '$(WindowsAppSdkCompatibilityInitialize)' == 'true')">
    <!-- Any of the individual initialize steps will require the core auto initializer -->
    <WindowsAppSdkAutoInitialize Condition="'$(WindowsAppSdkAutoInitialize)'==''">true</WindowsAppSdkAutoInitialize>
  </PropertyGroup>

</Project>
