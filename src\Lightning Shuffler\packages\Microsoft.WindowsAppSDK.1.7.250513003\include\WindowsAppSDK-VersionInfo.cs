﻿// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License. See <PERSON><PERSON>EN<PERSON> in the project root for license information.

// <auto-generated>
// Exclude this file from StyleCop analysis. This file is generated and may be added to projects.
// DO NOT MODIFY. Changes to this file may cause incorrect behavior and will be lost on updates.
// </auto-generated>

namespace Microsoft.WindowsAppSDK
{
    // Release information
    internal class Release
    {
        /// The major version of the Windows App SDK release.
        internal const ushort Major = 1;

        /// The minor version of the Windows App SDK release.
        internal const ushort Minor = 7;

        /// The patch version of the Windows App SDK release.
        internal const ushort Patch = 513;

        /// The major and minor version of the Windows App SDK release, encoded as a uint32_t (0xMMMMNNNN where M=major, N=minor).
        internal const uint MajorMinor = 0x00010007;

        /// The Windows App SDK release's channel; for example, "preview", or empty string for stable.
        internal const string Channel = "stable";

        /// The Windows App SDK release's version tag; for example, "preview2", or empty string for stable.
        internal const string VersionTag = "";

        /// The Windows App SDK release's short-form version tag; for example, "p2", or empty string for stable.
        internal const string VersionShortTag = "";

        /// The Windows App SDK release's version tag, formatted for concatenation when constructing identifiers; for example, "-preview2", or empty string for stable.
        internal const string FormattedVersionTag = "";

        /// The Windows App SDK release's short-form version tag, formatted for concatenation when constructing identifiers; for example, "-p2", or empty string for stable.
        internal const string FormattedVersionShortTag = "";
    }

    // Runtime information
    namespace Runtime
    {
        internal class Identity
        {
            /// The Windows App SDK runtime's package identity's Publisher.
            internal const string Publisher = "CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US";

            /// The Windows App SDK runtime's package identity's PublisherId.
            internal const string PublisherId = "8wekyb3d8bbwe";
        }

        internal class Version
        {
            /// The major version of the Windows App SDK runtime; for example, 1000.
            internal const ushort Major = 7000;

            /// The minor version of the Windows App SDK runtime; for example, 446.
            internal const ushort Minor = 498;

            /// The build version of the Windows App SDK runtime; for example, 804.
            internal const ushort Build = 2246;

            /// The revision version of the Windows App SDK runtime; for example, 0.
            internal const ushort Revision = 0;

            /// The version of the Windows App SDK runtime, as a uint64l for example, 0x03E801BE03240000.
            internal const ulong UInt64 = 0x1B5801F208C60000;

            /// The version of the Windows App SDK runtime, as a string (const wchar_t*); for example, "1000.446.804.0".
            internal const string DotQuadString = "7000.498.2246.0";
        }

        namespace Packages
        {
            internal class Framework
            {
                /// The Windows App SDK runtime's Framework package's family name.
                internal const string PackageFamilyName = "Microsoft.WindowsAppRuntime.1.7_8wekyb3d8bbwe";
            }
            internal class Main
            {
                /// The Windows App SDK runtime's Main package's family name.
                internal const string PackageFamilyName = "MicrosoftCorporationII.WinAppRuntime.Main.1.7_8wekyb3d8bbwe";
            }
            internal class Singleton
            {
                /// The Windows App SDK runtime's Singleton package's family name.
                internal const string PackageFamilyName = "MicrosoftCorporationII.WinAppRuntime.Singleton_8wekyb3d8bbwe";
            }
            namespace DDLM
            {
                internal class X86
                {
                    /// The Windows App SDK runtime's Dynamic Dependency Lifetime Manager (DDLM) package's family name, for x86.
                    internal const string PackageFamilyName = "Microsoft.WinAppRuntime.DDLM.7000.498.2246.0-x8_8wekyb3d8bbwe";
                }
                internal class X64
                {
                    /// The Windows App SDK runtime's Dynamic Dependency Lifetime Manager (DDLM) package's family name, for x64.
                    internal const string PackageFamilyName = "Microsoft.WinAppRuntime.DDLM.7000.498.2246.0-x6_8wekyb3d8bbwe";
                }
                internal class Arm64
                {
                    /// The Windows App SDK runtime's Dynamic Dependency Lifetime Manager (DDLM) package's family name, for arm64.
                    internal const string PackageFamilyName = "Microsoft.WinAppRuntime.DDLM.7000.498.2246.0-a6_8wekyb3d8bbwe";
                }
            }
        }
    }
}
