<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

    <Target Name="AddUndockedRegFreeWinRTCppDefines" BeforeTargets="ClCompile" Condition="'$(WindowsAppSdkUndockedRegFreeWinRTInitialize)' == 'true'">
        <ItemGroup>
            <ClCompile>
                <PreprocessorDefinitions>MICROSOFT_WINDOWSAPPSDK_UNDOCKEDREGFREEWINRT_AUTO_INITIALIZE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
                <PreprocessorDefinitions Condition="'$(WindowsAppSdkUndockedRegFreeWinRTInitializeLoadLibrary)' == 'true'">MICROSOFT_WINDOWSAPPSDK_UNDOCKEDREGFREEWINRT_AUTO_INITIALIZE_LOADLIBRARY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
            </ClCompile>
        </ItemGroup>
    </Target>

    <Target Name="GenerateUndockedRegFreeWinRTCpp" Condition="'$(WindowsAppSdkUndockedRegFreeWinRTInitialize)' == 'true'">
        <ItemGroup>
            <ClCompile Include="$(MSBuildThisFileDirectory)..\..\include\UndockedRegFreeWinRT-AutoInitializer.cpp">
                <PrecompiledHeader>NotUsing</PrecompiledHeader>
                <PreprocessorDefinitions>MICROSOFT_WINDOWSAPPSDK_UNDOCKEDREGFREEWINRT_AUTO_INITIALIZE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
                <PreprocessorDefinitions Condition="'$(WindowsAppSdkUndockedRegFreeWinRTInitializeLoadLibrary)' == 'true'">MICROSOFT_WINDOWSAPPSDK_UNDOCKEDREGFREEWINRT_AUTO_INITIALIZE_LOADLIBRARY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
            </ClCompile>
        </ItemGroup>
    </Target>

    <PropertyGroup>
        <BeforeClCompileTargets>
            $(BeforeClCompileTargets); GenerateUndockedRegFreeWinRTCpp;
        </BeforeClCompileTargets>
    </PropertyGroup>

</Project>
