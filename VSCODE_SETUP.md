# VSCode Setup Guide for Lightning Shuffler

This guide will help you set up Visual Studio Code for developing the Lightning Shuffler WinUI 3 C++/WinRT application.

## Prerequisites

### Required Software

1. **Visual Studio 2022 Community** (or Professional/Enterprise)
   - Download from: https://visualstudio.microsoft.com/downloads/
   - **Important:** Install with "Desktop development with C++" workload
   - Ensure these components are selected:
     - Windows 11 SDK (latest version)
     - CMake tools for C++
     - C++/WinRT templates and tools

2. **Visual Studio Code**
   - Download from: https://code.visualstudio.com/

### Required VSCode Extensions

The project includes a `.vscode/extensions.json` file with recommended extensions. VSCode will prompt you to install them automatically when you open the project.

**Core Extensions:**
- **C/C++** (`ms-vscode.cpptools`) - IntelliSense, debugging, and code browsing
- **C/C++ Extension Pack** (`ms-vscode.cpptools-extension-pack`) - Complete C++ development tools
- **CMake Tools** (`ms-vscode.cmake-tools`) - CMake integration
- **XML** (`redhat.vscode-xml`) - XAML file support
- **PowerShell** (`ms-vscode.powershell`) - PowerShell script support

## Project Setup

### 1. Open the Project

```bash
# Clone or navigate to the project directory
cd "Lightning Shuffler (A)"

# Open in VSCode
code .
```

### 2. Install Extensions

When you first open the project, VSCode will show a notification asking if you want to install the recommended extensions. Click **"Install"**.

Alternatively, you can install them manually:
1. Press `Ctrl+Shift+X` to open Extensions view
2. Search for each extension by name and install

### 3. Configure C++ IntelliSense

The project includes a `.vscode/c_cpp_properties.json` file that configures IntelliSense for WinUI 3 development. This should work automatically, but if you encounter issues:

1. Press `Ctrl+Shift+P`
2. Type "C/C++: Edit Configurations (UI)"
3. Verify the configuration matches your Visual Studio installation path

## Building and Running

### Method 1: Using Build Script (Recommended)

The project includes a `build.bat` script for easy building:

```bash
# Debug build
build.bat debug

# Release build  
build.bat release

# Build and run
build.bat debug run

# Clean build
build.bat clean
```

### Method 2: Using VSCode Tasks

Press `Ctrl+Shift+P` and type "Tasks: Run Task", then select:

- **Build (Debug)** - Default build task (also `Ctrl+Shift+B`)
- **Build (Release)** - Release configuration
- **Build and Run (Debug)** - Build and launch the app
- **Clean Build** - Clean all build artifacts

### Method 3: Using VSCode Debugger

1. Press `F5` or go to Run and Debug view (`Ctrl+Shift+D`)
2. Select "Launch Lightning Shuffler (Debug)" configuration
3. The project will build automatically and launch with debugger attached

## Development Workflow

### 1. Code Editing

- **IntelliSense**: Auto-completion and error detection work for C++, XAML, and IDL files
- **Go to Definition**: `F12` or `Ctrl+Click` on symbols
- **Find All References**: `Shift+F12`
- **Rename Symbol**: `F2`

### 2. Building

- **Quick Build**: `Ctrl+Shift+B` (runs default build task)
- **Build Output**: Check the Terminal panel for build results
- **Error Navigation**: Click on errors in Problems panel to jump to code

### 3. Debugging

- **Start Debugging**: `F5`
- **Start Without Debugging**: `Ctrl+F5`
- **Breakpoints**: Click in the gutter or press `F9`
- **Debug Console**: Available during debugging sessions

### 4. Terminal Integration

The project configures special terminal profiles:

- **Developer Command Prompt**: Pre-configured with Visual Studio environment
- **Developer PowerShell**: PowerShell with Visual Studio tools

Access via `Ctrl+Shift+`` (backtick) and select the profile.

## File Types and Syntax Highlighting

The project configures VSCode to recognize these file types:

- **`.xaml`** - XML syntax highlighting for UI markup
- **`.idl`** - C++ syntax highlighting for interface definitions  
- **`.h/.cpp`** - Full C++ IntelliSense and debugging support
- **`.winmd`** - Windows Runtime metadata files

## Troubleshooting

### Build Issues

1. **"MSBuild not found"**
   - Ensure Visual Studio 2022 is installed with C++ tools
   - Try running from "Developer Command Prompt" terminal profile

2. **"Windows SDK not found"**
   - Install Windows 11 SDK through Visual Studio Installer
   - Update the SDK version in `c_cpp_properties.json` if needed

3. **NuGet restore failures**
   - Run `nuget restore` manually in the project directory
   - Check internet connection and proxy settings

### IntelliSense Issues

1. **Red squiggles on valid code**
   - Press `Ctrl+Shift+P` → "C/C++: Reset IntelliSense Database"
   - Check that include paths in `c_cpp_properties.json` are correct

2. **XAML files not recognized**
   - Ensure XML extension is installed
   - Check file associations in VSCode settings

### Debugging Issues

1. **Breakpoints not hit**
   - Ensure you're building in Debug configuration
   - Check that the executable path in `launch.json` is correct

2. **App doesn't start**
   - Verify the executable was built successfully
   - Check Windows Defender or antivirus isn't blocking the app

## Advanced Configuration

### Custom Build Configurations

You can modify the build tasks in `.vscode/tasks.json` to add custom configurations:

```json
{
    "label": "Build (Custom)",
    "type": "shell", 
    "command": "msbuild",
    "args": [
        "\"${workspaceFolder}/src/Lightning Shuffler/Lightning Shuffler.sln\"",
        "/p:Configuration=Debug",
        "/p:Platform=x64",
        "/p:CustomProperty=Value"
    ]
}
```

### Workspace Settings

The project includes workspace-specific settings in `.vscode/settings.json`:

- File associations for WinUI 3 file types
- C++ formatting and IntelliSense configuration  
- Terminal profiles with Visual Studio environment
- File exclusions for build artifacts

You can customize these settings for your development preferences.

## Getting Help

- **VSCode C++ Documentation**: https://code.visualstudio.com/docs/languages/cpp
- **WinUI 3 Documentation**: https://docs.microsoft.com/en-us/windows/apps/winui/
- **C++/WinRT Documentation**: https://docs.microsoft.com/en-us/windows/uwp/cpp-and-winrt-apis/

## Summary

With this setup, you can:
- ✅ Build and run Lightning Shuffler from VSCode
- ✅ Debug with full breakpoint and variable inspection support  
- ✅ Get IntelliSense for C++, XAML, and IDL files
- ✅ Use integrated terminal with Visual Studio tools
- ✅ Manage tasks and launch configurations easily

The configuration provides a complete development environment that rivals Visual Studio while maintaining the lightweight feel of VSCode.
