// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_System_0_H
#define WINRT_Microsoft_Windows_System_0_H
WINRT_EXPORT namespace winrt::Microsoft::Windows::System
{
    struct IEnvironmentManager;
    struct IEnvironmentManager2;
    struct IEnvironmentManagerStatics;
    struct EnvironmentManager;
    struct EnvironmentManagerContract;
}
namespace winrt::impl
{
    template <> struct category<winrt::Microsoft::Windows::System::IEnvironmentManager>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::System::IEnvironmentManager2>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::System::IEnvironmentManagerStatics>{ using type = interface_category; };
    template <> struct category<winrt::Microsoft::Windows::System::EnvironmentManager>{ using type = class_category; };
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::System::EnvironmentManager> = L"Microsoft.Windows.System.EnvironmentManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::System::IEnvironmentManager> = L"Microsoft.Windows.System.IEnvironmentManager";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::System::IEnvironmentManager2> = L"Microsoft.Windows.System.IEnvironmentManager2";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::System::IEnvironmentManagerStatics> = L"Microsoft.Windows.System.IEnvironmentManagerStatics";
    template <> inline constexpr auto& name_v<winrt::Microsoft::Windows::System::EnvironmentManagerContract> = L"Microsoft.Windows.System.EnvironmentManagerContract";
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::System::IEnvironmentManager>{ 0xD1B239BB,0x7013,0x5176,{ 0xB0,0x2A,0x63,0x47,0x74,0x10,0xD9,0x86 } }; // D1B239BB-7013-5176-B02A-63477410D986
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::System::IEnvironmentManager2>{ 0xCFC0AD51,0x02B7,0x57FF,{ 0x8C,0xA7,0xE0,0x15,0x25,0x17,0x37,0xCB } }; // CFC0AD51-02B7-57FF-8CA7-E015251737CB
    template <> inline constexpr guid guid_v<winrt::Microsoft::Windows::System::IEnvironmentManagerStatics>{ 0x407B1522,0x6156,0x5398,{ 0x93,0xFD,0xD6,0x41,0x1C,0x35,0xE7,0xB1 } }; // 407B1522-6156-5398-93FD-D6411C35E7B1
    template <> struct default_interface<winrt::Microsoft::Windows::System::EnvironmentManager>{ using type = winrt::Microsoft::Windows::System::IEnvironmentManager; };
    template <> struct abi<winrt::Microsoft::Windows::System::IEnvironmentManager>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetEnvironmentVariables(void**) noexcept = 0;
            virtual int32_t __stdcall GetEnvironmentVariable(void*, void**) noexcept = 0;
            virtual int32_t __stdcall SetEnvironmentVariable(void*, void*) noexcept = 0;
            virtual int32_t __stdcall AppendToPath(void*) noexcept = 0;
            virtual int32_t __stdcall RemoveFromPath(void*) noexcept = 0;
            virtual int32_t __stdcall AddExecutableFileExtension(void*) noexcept = 0;
            virtual int32_t __stdcall RemoveExecutableFileExtension(void*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::System::IEnvironmentManager2>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall get_AreChangesTracked(bool*) noexcept = 0;
        };
    };
    template <> struct abi<winrt::Microsoft::Windows::System::IEnvironmentManagerStatics>
    {
        struct WINRT_IMPL_NOVTABLE type : inspectable_abi
        {
            virtual int32_t __stdcall GetForProcess(void**) noexcept = 0;
            virtual int32_t __stdcall GetForUser(void**) noexcept = 0;
            virtual int32_t __stdcall GetForMachine(void**) noexcept = 0;
            virtual int32_t __stdcall get_IsSupported(bool*) noexcept = 0;
        };
    };
    template <typename D>
    struct consume_Microsoft_Windows_System_IEnvironmentManager
    {
        auto GetEnvironmentVariables() const;
        auto GetEnvironmentVariable(param::hstring const& name) const;
        auto SetEnvironmentVariable(param::hstring const& name, param::hstring const& value) const;
        auto AppendToPath(param::hstring const& path) const;
        auto RemoveFromPath(param::hstring const& path) const;
        auto AddExecutableFileExtension(param::hstring const& pathExt) const;
        auto RemoveExecutableFileExtension(param::hstring const& pathExt) const;
    };
    template <> struct consume<winrt::Microsoft::Windows::System::IEnvironmentManager>
    {
        template <typename D> using type = consume_Microsoft_Windows_System_IEnvironmentManager<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_System_IEnvironmentManager2
    {
        [[nodiscard]] auto AreChangesTracked() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::System::IEnvironmentManager2>
    {
        template <typename D> using type = consume_Microsoft_Windows_System_IEnvironmentManager2<D>;
    };
    template <typename D>
    struct consume_Microsoft_Windows_System_IEnvironmentManagerStatics
    {
        auto GetForProcess() const;
        auto GetForUser() const;
        auto GetForMachine() const;
        [[nodiscard]] auto IsSupported() const;
    };
    template <> struct consume<winrt::Microsoft::Windows::System::IEnvironmentManagerStatics>
    {
        template <typename D> using type = consume_Microsoft_Windows_System_IEnvironmentManagerStatics<D>;
    };
}
#endif
