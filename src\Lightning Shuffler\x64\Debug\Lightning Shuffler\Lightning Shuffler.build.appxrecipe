﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>ASHTONS-PC</Machine>
    <WindowsUser>ashto</WindowsUser>
    <TargetPlatformIdentifier>UAP</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Debug|x64</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>2e3c485d-fd19-46c8-9954-ac4e63301f94</PackageIdentityName>
    <PackageIdentityPublisher>CN=ashto</PackageIdentityPublisher>
    <IntermediateOutputPath>C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\Lightnin.a91a2cd7\x64\Debug\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath></PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\x64\Debug\Lightning Shuffler\AppX</LayoutDir>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\x64\Debug\Lightning Shuffler\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\x64\Debug\Lightning Shuffler\resources.pri">
      <PackagePath>resources.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\x64\Debug\Lightning Shuffler\Lightning_Shuffler.exe">
      <PackagePath>Lightning_Shuffler.exe</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.Web.WebView2.1.0.2903.40\build\..\runtimes\win-x64\native_uap\Microsoft.Web.WebView2.Core.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.Web.WebView2.1.0.2903.40\lib\Microsoft.Web.WebView2.Core.winmd">
      <PackagePath>Microsoft.Web.WebView2.Core.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\Assets\LockScreenLogo.scale-200.png">
      <PackagePath>Assets\LockScreenLogo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\Assets\SplashScreen.scale-200.png">
      <PackagePath>Assets\SplashScreen.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\Assets\Square150x150Logo.scale-200.png">
      <PackagePath>Assets\Square150x150Logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\Assets\Square44x44Logo.scale-200.png">
      <PackagePath>Assets\Square44x44Logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\Assets\Square44x44Logo.targetsize-24_altform-unplated.png">
      <PackagePath>Assets\Square44x44Logo.targetsize-24_altform-unplated.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\Assets\StoreLogo.png">
      <PackagePath>Assets\StoreLogo.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\Assets\Wide310x150Logo.scale-200.png">
      <PackagePath>Assets\Wide310x150Logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\x64\Debug\Lightning Shuffler\Lightning_Shuffler.winmd">
      <PackagePath>Lightning_Shuffler.winmd</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.UniversalCRT.Debug\10.0.26100.0\Redist\Debug\x64\ucrtbased.dll">
      <PackagePath>ucrtbased.dll</PackagePath>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
    <ResolvedSDKReference Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.WindowsAppSDK.1.7.250513003\build\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.498.2246.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.498.2246.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.WindowsAppSDK.1.7.250513003\build\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.WindowsAppSDK.1.7.250513003\build\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.498.2246.0</Version>
      <Architecture>win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.498.2246.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.WindowsAppSDK.1.7.250513003\build\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.WindowsAppSDK.1.7.250513003\build\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.498.2246.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.498.2246.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.WindowsAppSDK.1.7.250513003\build\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.WindowsAppSDK.1.7.250513003\build\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.498.2246.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.498.2246.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\Downloads\projects\Other\Lightning Shuffler %28A%29\src\Lightning Shuffler\packages\Microsoft.WindowsAppSDK.1.7.250513003\build\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug.UWPDesktop</Name>
      <Version>14.0.33728.0</Version>
      <Architecture>ARM</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug.UWPDesktop, MinVersion = 14.0.33728.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\.\AppX\Debug\ARM\Microsoft.VCLibs.ARM.Debug.14.00.Desktop.appx</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug.UWPDesktop</Name>
      <Version>14.0.33728.0</Version>
      <Architecture>ARM64</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug.UWPDesktop, MinVersion = 14.0.33728.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\.\AppX\Debug\ARM64\Microsoft.VCLibs.ARM64.Debug.14.00.Desktop.appx</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug.UWPDesktop</Name>
      <Version>14.0.33728.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug.UWPDesktop, MinVersion = 14.0.33728.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\.\AppX\Debug\x64\Microsoft.VCLibs.x64.Debug.14.00.Desktop.appx</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug.UWPDesktop</Name>
      <Version>14.0.33728.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug.UWPDesktop, MinVersion = 14.0.33728.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs.Desktop\14.0\.\AppX\Debug\x86\Microsoft.VCLibs.x86.Debug.14.00.Desktop.appx</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug</Name>
      <Version>14.0.33519.0</Version>
      <Architecture>ARM</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug, MinVersion = 14.0.33519.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\.\AppX\Debug\ARM\Microsoft.VCLibs.ARM.Debug.14.00.appx</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug</Name>
      <Version>14.0.33519.0</Version>
      <Architecture>ARM64</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug, MinVersion = 14.0.33519.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\.\AppX\Debug\ARM64\Microsoft.VCLibs.ARM64.Debug.14.00.appx</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug</Name>
      <Version>14.0.33519.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug, MinVersion = 14.0.33519.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\.\AppX\Debug\x64\Microsoft.VCLibs.x64.Debug.14.00.appx</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\">
      <Name>Microsoft.VCLibs.140.00.Debug</Name>
      <Version>14.0.33519.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.VCLibs.140.00.Debug, MinVersion = 14.0.33519.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Program Files %28x86%29\Microsoft SDKs\Windows Kits\10\ExtensionSDKs\Microsoft.VCLibs\14.0\.\AppX\Debug\x86\Microsoft.VCLibs.x86.Debug.14.00.appx</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
  </ItemGroup>
</Project>
