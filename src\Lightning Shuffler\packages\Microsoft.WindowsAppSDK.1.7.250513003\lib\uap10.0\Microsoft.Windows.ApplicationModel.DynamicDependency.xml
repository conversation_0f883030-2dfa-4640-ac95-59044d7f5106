<doc>
  <assembly>
    <name>Microsoft.Windows.ApplicationModel.DynamicDependency</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.ApplicationModel.DynamicDependency.AddPackageDependencyOptions">
      <summary>Defines options that can be applied when adding a run-time reference to a framework package by using the PackageDependency.Add method.</summary>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.AddPackageDependencyOptions.#ctor">
      <summary>Creates a new instance of the AddPackageDependencyOptions class.</summary>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.AddPackageDependencyOptions.PrependIfRankCollision">
      <summary>When you call the PackageDependency.Add method and multiple packages are present in the package graph with the same rank as specified by the Rank property, this property indicates whether the resolved package is added before others of the same rank.</summary>
      <returns>If true, the resolved package is added before others of the same rank. If false, the resolved package is not added before others of the same rank.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.AddPackageDependencyOptions.Rank">
      <summary>The rank to use to add the resolved package to the caller's package graph.</summary>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions">
      <summary>Defines criteria that can be applied when creating an install-time reference to a framework package by using the PackageDependency.Create method. This informs the OS that your unpackaged app has a dependency upon a framework package that meets the specified criteria.</summary>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions.#ctor">
      <summary>Creates a new instance of the CreatePackageDependencyOptions class.</summary>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions.Architectures">
      <summary>Gets or sets the processor architectures of the framework package on which your unpackaged app has a dependency.</summary>
      <returns>A bitwise combination of values that indicates the processor architectures of the framework package on which your unpackaged app has a dependency.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions.LifetimeArtifact">
      <summary>Gets or sets the name of the artifact used to define the lifetime of the package dependency, if the LifetimeArtifactKind property is set to PackageDependencyLifetimeArtifactKind.FilePath or PackageDependencyLifetimeArtifactKind.RegistryKey.</summary>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions.LifetimeArtifactKind">
      <summary>Gets or sets the type of artifact to use to define the lifetime of the package dependency.</summary>
      <returns>The type of artifact to use to define the lifetime of the package dependency.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions.VerifyDependencyResolution">
      <summary>Gets or sets a value that indicates whether to disable dependency resolution when pinning a package dependency. This is useful for installers running as user contexts other than the target user (for example, installers running as LocalSystem).</summary>
      <returns>Specify true to verify dependency resolution when pinning a package dependency; specify false to disable dependency resolution.</returns>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency">
      <summary>Represents a framework package on which the current app has a dependency, and includes members you can use to manage the lifetime of the dependency.</summary>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.Add">
      <summary>Adds a run-time reference for the framework package dependency you created earlier by using the Create method. After this method successfully returns, your app may activate types and use content from the framework package.</summary>
      <returns>An object that provides context info about the framework package dependency and enables you to remove the run-time reference.</returns>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.Add(Microsoft.Windows.ApplicationModel.DynamicDependency.AddPackageDependencyOptions)">
      <summary>Adds a run-time reference for the framework package dependency you created earlier by using the Create method, with the specified options. After this method successfully returns, your app can activate types and use content from the framework package.</summary>
      <param name="options">Defines additional options to specify the framework package reference.</param>
      <returns>An object that provides context info about the framework package dependency and enables you to remove the run-time reference.</returns>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.Create(System.String,Windows.ApplicationModel.PackageVersion,Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions)">
      <summary>Creates an install-time reference for a framework package dependency for the current app, using the specified package family name and minimum version and the specified options. When you use this method, the framework package dependency is accessible to the current user only. To create a framework package dependency that is accessible to all users, use the CreateForSystem method instead.</summary>
      <param name="packageFamilyName">The package family name of the framework package on which to take dependency.</param>
      <param name="minVersion">The minimum version of the framework package on which to take dependency.</param>
      <param name="options">Defines additional criteria to specify the framework package you want to use in your app.</param>
      <returns>The object that represents the package dependency, and provides members you can use to manage the lifetime of the dependency.</returns>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.Create(System.String,Windows.ApplicationModel.PackageVersion)">
      <summary>Creates an install-time reference for a framework package dependency for the current app, using the specified package family name and minimum version. When you use this method, the framework package dependency is accessible to the current user only. To create a framework package dependency that is accessible to all users, use the CreateForSystem method instead.</summary>
      <param name="packageFamilyName">The package family name of the framework package on which to take dependency.</param>
      <param name="minVersion">The minimum version of the framework package on which to take dependency.</param>
      <returns>The object that represents the package dependency, and provides members you can use to manage the lifetime of the dependency.</returns>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.CreateForSystem(System.String,Windows.ApplicationModel.PackageVersion,Microsoft.Windows.ApplicationModel.DynamicDependency.CreatePackageDependencyOptions)">
      <summary>Creates an install-time reference for a framework package dependency for the current app, using the specified package family name and minimum version and the specified options. This method creates a framework package dependency that is accessible to all users, and this method requires that the caller has administrative privileges. To create a framework package dependency that is accessible only to the current user, use the Create method instead.</summary>
      <param name="packageFamilyName">The package family name of the framework package on which to take dependency.</param>
      <param name="minVersion">The minimum version of the framework package on which to take dependency.</param>
      <param name="options">Defines additional criteria to specify the framework package you want to use in your app.</param>
      <returns>The object that represents the package dependency, and provides members you can use to manage the lifetime of the dependency.</returns>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.Delete">
      <summary>Deletes the install-time reference for the framework package dependency you created earlier by using the Create method. This method informs the OS that it is safe to remove the framework package if no other apps have a dependency on it.</summary>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.GetFromId(System.String)">
      <summary>Creates a new package dependency instance from the specified package dependency ID.</summary>
      <param name="id">The existing package dependency ID from which to create the new package dependency object.</param>
      <returns>The object that represents the new package dependency, and provides members you can use to manage the lifetime of the dependency.</returns>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.GetFromIdForSystem(System.String)">
      <summary>Creates a new package dependency instance from the specified package dependency ID. The package dependency is accessible to all users, , and this method requires that the caller has administrative privileges.</summary>
      <param name="id">The existing package dependency ID from which to create the new package dependency object.</param>
      <returns>The object that represents the new package dependency, and provides members you can use to manage the lifetime of the dependency.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.GenerationId">
      <summary>Gets the package graph's current generation ID.</summary>
      <returns>The package graph's current generation ID.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.Id">
      <summary>Gets the ID of the package dependency. This value is available after successful calls to the Create and CreateForSystem methods.</summary>
      <returns>The ID of the package dependency.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependency.PackageGraphRevisionId">
      <summary>Gets the package graph's current generation ID.</summary>
      <returns>The package graph's current generation ID.</returns>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContext">
      <summary>Provides context info about a resolved framework package dependency that was created by using the PackageDependency.Add method.</summary>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContext.#ctor(Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContextId)">
      <summary>Creates a new instance of the PackageDependencyContext class based on the specified context ID.</summary>
      <param name="contextId">The context ID on which to base the new PackageDependencyContext.</param>
    </member>
    <member name="M:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContext.Remove">
      <summary>Removes a resolved package dependency from the current process' package graph (that is, a run-time reference for a framework package dependency that was added by using the PackageDependency.Add.</summary>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContext.ContextId">
      <summary>Gets the context ID of the resolved framework package dependency for the current context PackageDependencyContext object.</summary>
      <returns>The context ID of the resolved framework package dependency for the current context PackageDependencyContext object.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContext.PackageDependencyId">
      <summary>Gets the ID of the resolved framework package dependency for the current context PackageDependencyContext object.</summary>
      <returns>The ID of the resolved framework package dependency for the current context PackageDependencyContext object.</returns>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContext.PackageFullName">
      <summary>Gets the package full name for the resolved framework package dependency for the current context PackageDependencyContext object.</summary>
      <returns>The package full name for the resolved framework package dependency for the current context PackageDependencyContext object.</returns>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContextId">
      <summary>Encapsulates a unique ID for a resolved framework package dependency that is described by a PackageDependencyContext object.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyContextId.Id">
      <summary>The unique ID for a resolved framework package dependency.</summary>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyLifetimeArtifactKind">
      <summary>Defines the type of artifacts you can assign to the LifetimeArtifactKind property to define the lifetime of a package dependency.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyLifetimeArtifactKind.FilePath">
      <summary>The lifetime artifact is an absolute filename or path. The package dependency is implicitly deleted when this is deleted.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyLifetimeArtifactKind.Process">
      <summary>The current process is the lifetime artifact. The package dependency is implicitly deleted when the process terminates.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyLifetimeArtifactKind.RegistryKey">
      <summary>The lifetime artifact is a registry key in the format root\subkey, where root is one of the following: HKEY_LOCAL_MACHINE, HKEY_CURRENT_USER, HKEY_CLASSES_ROOT, or HKEY_USERS. The package dependency is implicitly deleted when this is deleted.</summary>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyProcessorArchitectures">
      <summary>Defines the processor architectures for a framework package dependency that you create by using the PackageDependency.Create method.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyProcessorArchitectures.Arm">
      <summary>Specifies the ARM architecture.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyProcessorArchitectures.Arm64">
      <summary>Specifies the ARM64 architecture.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyProcessorArchitectures.Neutral">
      <summary>Specifies the neutral architecture.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyProcessorArchitectures.None">
      <summary>No processor architecture is specified.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyProcessorArchitectures.X64">
      <summary>Specifies the x64 architecture.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyProcessorArchitectures.X86">
      <summary>Specifies the x86 architecture.</summary>
    </member>
    <member name="F:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyProcessorArchitectures.X86OnArm64">
      <summary>Specifies the x86/A64 architecture.</summary>
    </member>
    <member name="T:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyRank">
      <summary>Represents the default rank value to use to resolve a framework package dependency when using the PackageDependency.Add method.</summary>
    </member>
    <member name="P:Microsoft.Windows.ApplicationModel.DynamicDependency.PackageDependencyRank.Default">
      <summary>Gets the default rank value to use to resolve a framework package dependency when using the PackageDependency.Add method.</summary>
      <returns>The default rank value (currently this value is 0).</returns>
    </member>
  </members>
</doc>