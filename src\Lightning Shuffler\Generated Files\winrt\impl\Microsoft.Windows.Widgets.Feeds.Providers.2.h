// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Widgets_Feeds_Providers_2_H
#define WINRT_Microsoft_Windows_Widgets_Feeds_Providers_2_H
#include "winrt/impl/Windows.Storage.Streams.1.h"
#include "winrt/impl/Microsoft.Windows.Widgets.Feeds.Providers.1.h"
WINRT_EXPORT namespace winrt::Microsoft::Windows::Widgets::Feeds::Providers
{
    struct WINRT_IMPL_EMPTY_BASES CustomQueryParametersRequestedArgs : winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersRequestedArgs
    {
        CustomQueryParametersRequestedArgs(std::nullptr_t) noexcept {}
        CustomQueryParametersRequestedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersRequestedArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES CustomQueryParametersUpdateOptions : winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptions
    {
        CustomQueryParametersUpdateOptions(std::nullptr_t) noexcept {}
        CustomQueryParametersUpdateOptions(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::ICustomQueryParametersUpdateOptions(ptr, take_ownership_from_abi) {}
        CustomQueryParametersUpdateOptions(param::hstring const& feedProviderDefinitionId, param::hstring const& customQueryParameters);
    };
    struct WINRT_IMPL_EMPTY_BASES FeedAnalyticsInfoReportedArgs : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnalyticsInfoReportedArgs
    {
        FeedAnalyticsInfoReportedArgs(std::nullptr_t) noexcept {}
        FeedAnalyticsInfoReportedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedAnalyticsInfoReportedArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedDisabledArgs : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedDisabledArgs
    {
        FeedDisabledArgs(std::nullptr_t) noexcept {}
        FeedDisabledArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedDisabledArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedEnabledArgs : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedEnabledArgs
    {
        FeedEnabledArgs(std::nullptr_t) noexcept {}
        FeedEnabledArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedEnabledArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedErrorInfoReportedArgs : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedErrorInfoReportedArgs
    {
        FeedErrorInfoReportedArgs(std::nullptr_t) noexcept {}
        FeedErrorInfoReportedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedErrorInfoReportedArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedManager : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager,
        impl::require<FeedManager, winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager2>
    {
        FeedManager(std::nullptr_t) noexcept {}
        FeedManager(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedManager(ptr, take_ownership_from_abi) {}
        static auto GetDefault();
    };
    struct WINRT_IMPL_EMPTY_BASES FeedMessageReceivedArgs : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedMessageReceivedArgs
    {
        FeedMessageReceivedArgs(std::nullptr_t) noexcept {}
        FeedMessageReceivedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedMessageReceivedArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedProviderDisabledArgs : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderDisabledArgs
    {
        FeedProviderDisabledArgs(std::nullptr_t) noexcept {}
        FeedProviderDisabledArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderDisabledArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedProviderEnabledArgs : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderEnabledArgs
    {
        FeedProviderEnabledArgs(std::nullptr_t) noexcept {}
        FeedProviderEnabledArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderEnabledArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedProviderInfo : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderInfo
    {
        FeedProviderInfo(std::nullptr_t) noexcept {}
        FeedProviderInfo(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedProviderInfo(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedResourceRequest : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequest
    {
        FeedResourceRequest(std::nullptr_t) noexcept {}
        FeedResourceRequest(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequest(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedResourceRequestedArgs : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequestedArgs
    {
        FeedResourceRequestedArgs(std::nullptr_t) noexcept {}
        FeedResourceRequestedArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceRequestedArgs(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES FeedResourceResponse : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponse
    {
        FeedResourceResponse(std::nullptr_t) noexcept {}
        FeedResourceResponse(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Microsoft::Windows::Widgets::Feeds::Providers::IFeedResourceResponse(ptr, take_ownership_from_abi) {}
        FeedResourceResponse(winrt::Windows::Storage::Streams::IRandomAccessStreamReference const& content, param::hstring const& reasonPhrase, int32_t statusCode);
    };
}
#endif
