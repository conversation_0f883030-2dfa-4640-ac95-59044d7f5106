// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_System_Power_1_H
#define WINRT_Microsoft_Windows_System_Power_1_H
#include "winrt/impl/Microsoft.Windows.System.Power.0.h"
WINRT_EXPORT namespace winrt::Microsoft::Windows::System::Power
{
    struct WINRT_IMPL_EMPTY_BASES IPowerManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPowerManagerStatics>
    {
        IPowerManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IPowerManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct WINRT_IMPL_EMPTY_BASES IPowerManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPowerManagerStatics2>
    {
        IPowerManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IPowerManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
