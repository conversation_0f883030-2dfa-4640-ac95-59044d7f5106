<doc>
  <assembly>
    <name>Microsoft.Windows.AppLifecycle.Projection</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Windows.AppLifecycle.ActivationRegistrationManager">
      <summary>Provides static methods you can use to register and unregister for certain types of activations for your app.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.ActivationRegistrationManager.RegisterForFileTypeActivation(System.String[],System.String,System.String,System.String[],System.String)">
      <summary>Registers to activate the app when the specified file type is opened via ShellExecute, or the command-line.</summary>
      <param name="supportedFileTypes">One or more supported file types, specified by the file extension including the leading ., such as .docx.</param>
      <param name="logo">The path to the image or resource used by Windows for the file type. For packaged apps, this parameter is a package-relative path to an image file. For unpackaged, this parameter is a literal filepath to a binary file (DLL, EXE) plus a resource index.</param>
      <param name="displayName">This display name used by Windows for the file type.</param>
      <param name="supportedVerbs">Zero or more app-defined verbs. Each verb is added to the File Explorer context menu when a registered file is right-clicked, and the selected verb is passed to the app as the IFileActivatedEventArgs.Verb property.</param>
      <param name="exePath">The path to the executable to be activated. If you pass an empty string, the current executable will be activated by default. Typically this parameter is specified if the caller of this method is the app's installer rather than the app itself.</param>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.ActivationRegistrationManager.RegisterForProtocolActivation(System.String,System.String,System.String,System.String)">
      <summary>Registers to activate the app when the specified URI scheme is executed via ShellExecute, or the command-line.</summary>
      <param name="scheme">The URI scheme to register for activations, such as https.</param>
      <param name="logo">The path to the image or resource used by Windows for the URI scheme. For packaged apps, this parameter is a package-relative path to an image file. For unpackaged, this parameter is a literal filepath to a binary file (DLL, EXE) plus a resource index.</param>
      <param name="displayName">This display name used by Windows for the URI scheme.</param>
      <param name="exePath">The path to the executable to be activated. If you pass an empty string, the current exectuable will be activated by default. Typically this parameter is specified if the caller of this method is the app's installer rather than the app itself.</param>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.ActivationRegistrationManager.RegisterForStartupActivation(System.String,System.String)">
      <summary>Registers to activate the app when when the app is started by the user logging into the Windows OS, either because of a registry key, or because of a shortcut in a well-known startup folder.</summary>
      <param name="taskId">An app-defined ID that can be used to unregister for startup activations later by using the UnregisterForStartupActivation method.</param>
      <param name="exePath">The path to the executable to be activated. If you pass an empty string, the current exectuable will be activated by default. Typically this parameter is specified if the caller of this method is the app's installer rather than the app itself.</param>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.ActivationRegistrationManager.UnregisterForFileTypeActivation(System.String[],System.String)">
      <summary>Unregisters a file type activation that was registered earlier by using the RegisterForFileTypeActivation method.</summary>
      <param name="fileTypes">The file type that was previously registered for protocol activation.</param>
      <param name="exePath">The path to the executable that was previously registered for protocol activation.</param>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.ActivationRegistrationManager.UnregisterForProtocolActivation(System.String,System.String)">
      <summary>Unregisters a protocol activation that was registered earlier by using the RegisterForProtocolActivation method.</summary>
      <param name="scheme">The URI scheme that was previously registered for protocol activation.</param>
      <param name="exePath">The path to the executable that was previously registered for protocol activation.</param>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.ActivationRegistrationManager.UnregisterForStartupActivation(System.String)">
      <summary>Unregisters a startup activation that was registered earlier by using the RegisterForStartupActivation method.</summary>
      <param name="taskId">The task ID that was previously registered for protocol activation.</param>
    </member>
    <member name="T:Microsoft.Windows.AppLifecycle.AppActivationArguments">
      <summary>Contains information about the type and data payload for an app activation that was registered by using one of the static methods of the ActivationRegistrationManager class.</summary>
    </member>
    <member name="P:Microsoft.Windows.AppLifecycle.AppActivationArguments.Data">
      <summary>Gets the data payload for a registered activation.</summary>
      <returns>The data payload for a registered activation. For more information about the type of this object, see the remarks.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppLifecycle.AppActivationArguments.Kind">
      <summary>Gets the type of a registered activation.</summary>
      <returns>The type of a registered activation.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppLifecycle.AppInstance">
      <summary>Represents an instance of an app.</summary>
    </member>
    <member name="E:Microsoft.Windows.AppLifecycle.AppInstance.Activated">
      <summary>Raised for activations that have been redirected via Microsoft.Windows.AppLifecycle.AppInstance.RedirectActivationToAsync.</summary>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.AppInstance.FindOrRegisterForKey(System.String)">
      <summary>Registers an app instance with the platform, or finds an existing instance if another instance has already registered this key.</summary>
      <param name="key">A non-empty string as a key for the instance.</param>
      <returns>An app instance that represents the first app that registered the key. The caller can determine whether that instance is the current instance.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.AppInstance.GetActivatedEventArgs">
      <summary>Retrieves the event arguments for an app activation that was registered by using one of the static methods of the ActivationRegistrationManager class.</summary>
      <returns>An object that contains the activation type and the data payload, or null. See Remarks.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.AppInstance.GetCurrent">
      <summary>Retrieves the current running instance of the app.</summary>
      <returns>The current running instance of the app.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.AppInstance.GetInstances">
      <summary>Retrieves a collection of all running instances of the app.</summary>
      <returns>The collection of all running instances of the app.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.AppInstance.RedirectActivationToAsync(Microsoft.Windows.AppLifecycle.AppActivationArguments)">
      <summary>Redirects the current activation request to another app instance.</summary>
      <param name="args">The activation arguments to pass to the other app instance.</param>
      <returns>An object that represents the results of the asynchronous operation.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.AppInstance.Restart(System.String)">
      <summary>Restarts the application instance.</summary>
      <param name="arguments">The arguments to pass to the restarted instance.</param>
      <returns>The status of the restart request.</returns>
    </member>
    <member name="M:Microsoft.Windows.AppLifecycle.AppInstance.UnregisterKey">
      <summary>Unregisters a given key for this app instance.</summary>
    </member>
    <member name="P:Microsoft.Windows.AppLifecycle.AppInstance.IsCurrent">
      <summary>Gets a value that indicates whether this AppInstance object represents the current instance of the app or a different instance.</summary>
      <returns>true indicates that this AppInstance object represents the current instance of the app; false indicates it represents a different instance.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppLifecycle.AppInstance.Key">
      <summary>Gets an app-defined string value that identifies the current app instance for redirection purposes.</summary>
      <returns>An app-defined string value that identifies the current app instance for redirection purposes.</returns>
    </member>
    <member name="P:Microsoft.Windows.AppLifecycle.AppInstance.ProcessId">
      <summary>Gets the process ID of the app instance.</summary>
      <returns>The process ID of the app instance.</returns>
    </member>
    <member name="T:Microsoft.Windows.AppLifecycle.ExtendedActivationKind">
      <summary>Defines values that represent activation types.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.AppNotification">
      <summary>The app was activated by an app notification.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.AppointmentsProvider">
      <summary>The user wants to manage appointments that are provided by the app.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.BarcodeScannerProvider">
      <summary>The app was activated as a barcode scanner provider.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.CachedFileUpdater">
      <summary>The user wants to save a file that the app provides content management for.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.CameraSettings">
      <summary>The app captures photos or video from an attached camera.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.CommandLineLaunch">
      <summary>The app was launched from the command line.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.ComponentUI">
      <summary>Reserved for system use.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.Contact">
      <summary>The user wants to handle calls or messages for the phone number of a contact that is provided by the app.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.ContactPanel">
      <summary>The app was launched from the My People UI.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.ContactPicker">
      <summary>The user wants to pick contacts.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.Device">
      <summary>The app handles AutoPlay.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.DevicePairing">
      <summary>This app was activated as a result of pairing a device.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.DialReceiver">
      <summary>This app was launched by another app on a different device by using the DIAL protocol.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.File">
      <summary>An app launched a file whose file type this app is registered to handle.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.FileOpenPicker">
      <summary>The user wants to pick files that are provided by the app.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.FilePickerExperience">
      <summary>Reserved for system use.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.FileSavePicker">
      <summary>The user wants to save a file and selected the app as the location.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.GameUIProvider">
      <summary>The app was activated because it was launched by the OS due to a request for game-specific UI.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.Launch">
      <summary>The user launched the app or tapped a content tile.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.LockScreen">
      <summary>The app was activated as the lock screen.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.LockScreenCall">
      <summary>The app launches a call from the lock screen. If the user wants to accept the call, the app displays its call UI directly on the lock screen without requiring the user to unlock. A lock-screen call is a special type of launch activation.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.LockScreenComponent">
      <summary>Reserved for system use.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.PhoneCallActivation">
      <summary>The app was activated by a phone call.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.PickerReturned">
      <summary>Not supported. The app was activated after the completion of a picker.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.PickFileContinuation">
      <summary>Not supported. The app was activated after the app was suspended for a file picker operation.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.PickFolderContinuation">
      <summary>Not supported. The app was activated after the app was suspended for a folder picker operation.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.PickSaveFileContinuation">
      <summary>Not supported. The app was activated after the app was suspended for a file save picker operation.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.Print3DWorkflow">
      <summary>This app was launched by another app to provide a customized printing experience for a 3D printer.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.PrintSupportJobUI">
      <summary>The app was activated as print support job UI.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.PrintSupportSettingsUI">
      <summary>The app was activated as print support settings UI.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.PrintTaskSettings">
      <summary>The app handles print tasks.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.PrintWorkflowForegroundTask">
      <summary>The app was activated because the user is printing to a printer that has a Print Workflow App associated with it which has requested user input.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.Protocol">
      <summary>An app launched a URI whose scheme name this app is registered to handle.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.ProtocolForResults">
      <summary>The app was launched by another app with the expectation that it will return a result back to the caller.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.Push">
      <summary>The app was activated by a push notification.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.RestrictedLaunch">
      <summary>The user launched the restricted app.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.Search">
      <summary>The user wants to search with the app.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.ShareTarget">
      <summary>The app is activated as a target for share operations.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.StartupTask">
      <summary>The app was activated because the app is specified to launch at system startup or user log-in.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.ToastNotification">
      <summary>The app was activated when a user tapped on the body of a toast notification or performed an action inside a toast notification.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.UserDataAccountsProvider">
      <summary>The app was launched to handle the user interface for account management. In circumstances where the system would have shown the default system user interface, it instead has invoked your app with the UserDataAccountProvider contract. The activation payload contains information about the type of operation being requested and all the information necessary to replicate the system-provided user interface. This activation kind is limited to 1st party apps. To use this field, you must add the userDataAccountsProvider capability in your app's package manifest. For more info see App capability declarations.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.VoiceCommand">
      <summary>The app was activated as the result of a voice command.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.VpnForeground">
      <summary>The app was activated as a VPN app in the foreground.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.WalletAction">
      <summary>Not supported. The app was activated to perform a Wallet operation.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.WebAccountProvider">
      <summary>The app was activated by a web account provider.</summary>
    </member>
    <member name="F:Microsoft.Windows.AppLifecycle.ExtendedActivationKind.WebAuthenticationBrokerContinuation">
      <summary>Not supported. The app was activated after the app was suspended for a web authentication broker operation.</summary>
    </member>
  </members>
</doc>