// Copyright (c) Microsoft Corporation and Contributors.
// Licensed under the MIT License. See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.

// <auto-generated>
// Exclude this file from StyleCop analysis. This file isn't generated but is added to projects.
// DO NOT MODIFY. Changes to this file may cause incorrect behavior and will be lost on updates.
// </auto-generated>

// If any options are defined use them, else use the default
#if !MICROSOFT_WINDOWSAPPSDK_DEPLOYMENTMANAGER_AUTO_INITIALIZE_OPTIONS_DEFAULT
// Default isn't defined. Define it if no options are defined
#if MICROSOFT_WINDOWSAPPSDK_DEPLOYMENTMANAGER_AUTO_INITIALIZE_OPTIONS_NONE
#elif MICROSOFT_WINDOWSAPPSDK_DEPLOYMENTMANAGER_AUTO_INITIALIZE_OPTIONS_ONERRORSHOWUI
#else
// No options specified! Use the default
#define MICROSOFT_WINDOWSAPPSDK_DEPLOYMENTMANAGER_AUTO_INITIALIZE_OPTIONS_DEFAULT
#endif
#endif

using System.Reflection;
using System.Runtime.InteropServices;

namespace Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentManagerCS
{
    class AutoInitialize
    {
        // Called by WindowsAppRuntimeAutoInitializer.cs
        internal static void AccessWindowsAppSDK()
        {
            var options = Options;
            global::Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentResult deploymentResult = global::Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentManager.Initialize(options);
            if (deploymentResult.Status != global::Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentStatus.Ok)
            {
                int hr = deploymentResult.ExtendedError.HResult;
                global::System.Environment.Exit(hr);
                global::System.Environment.FailFast("WindowsAppRuntime.DeploymentManager.Initialize error 0x{hr:X}");
            }
        }

        internal static global::Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentInitializeOptions Options
        {
            get
            {
                var options = new global::Microsoft.Windows.ApplicationModel.WindowsAppRuntime.DeploymentInitializeOptions();
#if MICROSOFT_WINDOWSAPPSDK_DEPLOYMENTMANAGER_AUTO_INITIALIZE_OPTIONS_DEFAULT
                // Use the default options
                options.OnErrorShowUI = true;
#elif MICROSOFT_WINDOWSAPPSDK_DEPLOYMENTMANAGER_AUTO_INITIALIZE_OPTIONS_NONE
                // No options!
#else
                // Use the specified options
#if MICROSOFT_WINDOWSAPPSDK_DEPLOYMENTMANAGER_AUTO_INITIALIZE_OPTIONS_ONERRORSHOWUI
                options.OnErrorShowUI = true;
#endif
#endif
                return options;
            }
        }
    }
}
