<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <!--
      Microsoft.Common.CurrentVersion.targets ends up importing a bunch of .NET based targets, which import Microsoft.Nuget.targets,
      where they has a target called ComputeNetCoreFrameworkInjectionParameters, this target only runs when the 
      _ComputeNetCoreFrameworkInjectionParametersBeforeTargets is defined, and we don't need/want this target to run in C++ based projects.
    -->
    <_ComputeNetCoreFrameworkInjectionParametersBeforeTargets></_ComputeNetCoreFrameworkInjectionParametersBeforeTargets>

    <!-- Ensure that the correct Debugger is selected by default -->
    <DebuggerFlavor Condition="'$(WindowsPackageType)'=='Msix' and '$(DebuggerFlavor)'=='WindowsLocalDebugger'">AppHostLocalDebugger</DebuggerFlavor>
    <DebuggerFlavor Condition="'$(WindowsPackageType)'!='Msix' and '$(DebuggerFlavor)'=='AppHostLocalDebugger'">WindowsLocalDebugger</DebuggerFlavor>
  </PropertyGroup>
  <!--
    If a C++/CX project references this package, it will disable the ClCompile.CompileAsWinRT flag.
    We'll assume that a project with AppContainerApplication set to true, and isn't referencing
    C++/WinRT, falls into this category. This can always be overridden.
  -->
  <ItemDefinitionGroup>
    <ClCompile>
      <CompileAsWinRT Condition="'$(CppWinRTPackage)'!='true' and '$(AppContainerApplication)'=='true' and '%(ClCompile.CompileAsWinRT)' == ''">true</CompileAsWinRT>
    </ClCompile>
  </ItemDefinitionGroup>
</Project>
