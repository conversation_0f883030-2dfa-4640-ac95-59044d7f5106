// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.240405.15

#pragma once
#ifndef WINRT_Microsoft_Windows_Security_AccessControl_1_H
#define WINRT_Microsoft_Windows_Security_AccessControl_1_H
#include "winrt/impl/Microsoft.Windows.Security.AccessControl.0.h"
WINRT_EXPORT namespace winrt::Microsoft::Windows::Security::AccessControl
{
    struct WINRT_IMPL_EMPTY_BASES ISecurityDescriptorHelpersStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<ISecurityDescriptorHelpersStatics>
    {
        ISecurityDescriptorHelpersStatics(std::nullptr_t = nullptr) noexcept {}
        ISecurityDescriptorHelpersStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
