//  Copyright (c) Microsoft Corporation. All rights reserved.

import "oaidl.idl";
import "dxgi.idl";

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WIN8)")

// The following typedefs are used internally by MIDL.
cpp_quote("#if 0")
    /* MIDL 2.0+ definitions */
    typedef RECT *REFRECT;
cpp_quote("#endif // 0")

cpp_quote("#ifndef REFRECT")
cpp_quote("#ifdef __cplusplus")
cpp_quote("#define REFRECT const RECT &")
cpp_quote("#else // !__cplusplus")
cpp_quote("#define REFRECT const RECT * __MIDL_CONST")
cpp_quote("#endif // __cplusplus")
cpp_quote("#endif //REFRECT")

[
    object,
    uuid( e4cecd6c-f14b-4f46-83c3-8bbda27c6504 ),
    local,
    pointer_default(unique)
]
interface ISurfaceImageSourceNative: IUnknown
{
    HRESULT SetDevice([in, annotation("_In_")] IDXGIDevice *device);
    HRESULT BeginDraw([in, annotation("_In_")] RECT updateRect, [out, annotation("_Out_")] IDXGISurface** surface, [out, annotation("_Out_")] POINT* offset);
    HRESULT EndDraw();
}

[
    object,
    uuid( e8e84ac7-b7b8-40f4-b033-f877a756c52b ),
    local,
    pointer_default(unique)
]
interface IVirtualSurfaceUpdatesCallbackNative: IUnknown
{
    HRESULT UpdatesNeeded();
}

[
    object,
    uuid( 9e43c18e-7816-474c-840f-5c9c8b0e2207 ),
    local,
    pointer_default(unique)
]
interface IVirtualSurfaceImageSourceNative: ISurfaceImageSourceNative
{
    HRESULT Invalidate([in, annotation("_In_")] RECT updateRect);
    HRESULT GetUpdateRectCount([out, annotation("_Out_")] DWORD *count);
    HRESULT GetUpdateRects([out, size_is(count), annotation("_Out_writes_(count)")] RECT *updates, [in, annotation("_In_")] DWORD count);
    HRESULT GetVisibleBounds([out, annotation("_Out_")] RECT *bounds);
    HRESULT RegisterForUpdatesNeeded([in, annotation("_In_opt_")] IVirtualSurfaceUpdatesCallbackNative *callback);
    HRESULT Resize([in, annotation("_In_")] INT newWidth, [in, annotation("_In_")] INT newHeight);
}

[
    object,
    uuid( 24d43d84-4246-4aa7-9774-8604cb73d90d ),
    local,
    pointer_default(unique)
]
interface ISwapChainBackgroundPanelNative: IUnknown
{
    HRESULT SetSwapChain([in, annotation("_In_")] IDXGISwapChain *swapChain);
}

cpp_quote("#endif // NTDDI_VERSION >= NTDDI_WIN8")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WINBLUE)")

cpp_quote("#define E_SURFACE_CONTENTS_LOST 0x802b0020")

[
    object,
    uuid( 81521d7e-ff74-4a6b-8289-44bfd11cf0cc ),
    local,
    pointer_default(unique)
]
interface ISurfaceImageSourceManagerNative: IUnknown
{
    HRESULT FlushAllSurfacesWithDevice([in, annotation("_In_")] IUnknown *device);
}

// ISurfaceImageSourceNativeWithD2D:  Interface which enables coalesced D2D batches and drawing off-thread.
// Clients QI for this interface off of SurfaceImageSource or VirtualSurfaceImageSource.
[
    object,
    uuid( cb833102-d5d1-448b-a31a-52a9509f24e6 ),
    local,
    pointer_default(unique)
]
interface ISurfaceImageSourceNativeWithD2D: IUnknown
{
    HRESULT SetDevice([in, annotation("_In_")] IUnknown* device);
    HRESULT BeginDraw([in, annotation("_In_")] REFRECT updateRect, [in, annotation("_In_")] REFIID iid, [out, annotation("_COM_Outptr_")] void** updateObject, [out, annotation("_Out_")] POINT* offset);
    HRESULT EndDraw();
    HRESULT SuspendDraw();
    HRESULT ResumeDraw();
}

[
    object,
    uuid( 63aad0b8-7c24-40ff-85a8-640d944cc325 ),
    local,
    pointer_default(unique)
]
interface ISwapChainPanelNative: IUnknown
{
    HRESULT SetSwapChain([in, annotation("_In_")] IDXGISwapChain *swapChain);
}

cpp_quote("#endif // NTDDI_VERSION >= NTDDI_WINBLUE")

cpp_quote("#if (NTDDI_VERSION >= NTDDI_WINTHRESHOLD)")
[
    object,
    uuid( 88fd8248-10da-4810-bb4c-010dd27faea9 ),
    local,
    pointer_default(unique)
]
interface ISwapChainPanelNative2: ISwapChainPanelNative
{
    HRESULT SetSwapChainHandle([in, annotation("_In_")] HANDLE swapChainHandle);
}
cpp_quote("#endif // NTDDI_VERSION >= NTDDI_WINTHRESHOLD")

